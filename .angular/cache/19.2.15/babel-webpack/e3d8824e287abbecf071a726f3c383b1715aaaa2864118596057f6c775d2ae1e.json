{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { SwuiTimeDurationComponent } from './swui-time-duration.component';\nimport { CommonModule } from '@angular/common';\nimport { UntypedFormControl, ReactiveFormsModule } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { MatInputModule } from '@angular/material/input';\ndescribe('SwuiTimeDurationComponent', () => {\n  let component;\n  let fixture;\n  let testTime;\n  let host;\n  let daysControl;\n  let hoursControl;\n  let minutesControl;\n  let secondsControl;\n  let formTestValue;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [CommonModule, BrowserAnimationsModule, ReactiveFormsModule, MatInputModule],\n      declarations: [SwuiTimeDurationComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiTimeDurationComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    host = fixture.debugElement;\n    testTime = 1.296e+8;\n    formTestValue = {\n      days: '0',\n      hours: '0',\n      minutes: '1',\n      seconds: '10'\n    };\n    daysControl = component.form.get('days');\n    hoursControl = component.form.get('hours');\n    minutesControl = component.form.get('minutes');\n    secondsControl = component.form.get('seconds');\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should set value', () => {\n    component.value = testTime;\n    expect(component.value).toBe(coerceNumberProperty(testTime, 0));\n  });\n  it('should set required', () => {\n    component.required = true;\n    expect(component.required).toBe(coerceBooleanProperty(true));\n  });\n  it('should set disabled', () => {\n    component.disabled = true;\n    expect(component.disabled).toBe(coerceBooleanProperty(true));\n  });\n  it('should set enabled', () => {\n    component.disabled = false;\n    expect(component.disabled).toBe(coerceBooleanProperty(false));\n  });\n  it('should disable inner form on disabled', () => {\n    component.disabled = true;\n    expect(component.form.disabled).toBe(true);\n  });\n  it('should enable inner form on disabled', () => {\n    component.disabled = false;\n    expect(component.form.disabled).toBe(false);\n  });\n  it('should disable seconds', () => {\n    component.secondsDisabled = true;\n    expect(secondsControl.disabled).toBe(true);\n  });\n  it('should set placeholder', () => {\n    component.placeholder = 'test';\n    expect(component.placeholder).toBe('test');\n  });\n  it('should get empty false if controls are null', () => {\n    component.value = null;\n    expect(component.empty).toBe(false);\n  });\n  it('should get empty false if controls are equals 0', () => {\n    component.value = 0;\n    expect(component.empty).toBe(false);\n  });\n  it('should get empty false if controls are not empty', () => {\n    component.value = testTime;\n    expect(component.empty).toBe(false);\n  });\n  it('should get error state false', () => {\n    expect(component.errorState).toBeFalsy();\n  });\n  it('should set state changes', () => {\n    const nextSpy = spyOn(component.stateChanges, 'next');\n    component.required = true;\n    expect(nextSpy).toHaveBeenCalled();\n  });\n  it('should controlType to be defined', () => {\n    expect(component.controlType).toBe('lib-swui-time-duration');\n  });\n  it('should set host id', () => {\n    expect(host.nativeElement.getAttribute('id')).toBeDefined();\n  });\n  it('should shouldLabelFloat to be true when not empty', () => {\n    component.value = testTime;\n    expect(component.shouldLabelFloat).toBe(true);\n  });\n  it('should shouldLabelFloat to be true when host focused', () => {\n    dispatchFakeEvent(host.nativeElement, 'focus');\n    expect(component.shouldLabelFloat).toBe(true);\n  });\n  it('should shouldLabelFloat return true', () => {\n    expect(component.shouldLabelFloat).toBe(true);\n  });\n  it('should set host class floating when it is not empty', () => {\n    component.value = testTime;\n    fixture.detectChanges();\n    expect(host.nativeElement.classList.contains('floating')).toBe(true);\n  });\n  it('should set host class floating when host focused', () => {\n    dispatchFakeEvent(host.nativeElement, 'focus');\n    fixture.detectChanges();\n    expect(host.nativeElement.classList.contains('floating')).toBe(true);\n  });\n  it('should set aria-describedby on host', () => {\n    expect(host.nativeElement.getAttribute('aria-describedby')).toBe('');\n  });\n  it('should call onTouched on focus', () => {\n    spyOn(component, 'onTouched');\n    dispatchFakeEvent(host.nativeElement, 'focus');\n    dispatchFakeEvent(host.nativeElement, 'blur');\n    expect(component.onTouched).toHaveBeenCalled();\n  });\n  it('should init form', () => {\n    expect(component.form).toBeDefined();\n  });\n  it('should call onChange onInit when form value changed', () => {\n    spyOn(component, 'onChange');\n    component.form.setValue(formTestValue);\n    expect(component.onChange).toHaveBeenCalled();\n  });\n  it('should set new value when form value changed', () => {\n    component.form.setValue(formTestValue);\n    expect(component.value).toBe(70000);\n  });\n  it('should complete state change on destroy', () => {\n    const completeSpy = spyOn(component.stateChanges, 'complete');\n    component.ngOnDestroy();\n    expect(completeSpy).toHaveBeenCalled();\n  });\n  it('should setDescribedByIds', () => {\n    const testIds = ['test1', 'test2'];\n    component.setDescribedByIds(testIds);\n    expect(component.describedBy).toBe(testIds.join(' '));\n  });\n  it('patchform on writevalue', () => {\n    component.writeValue(testTime);\n    expect(component.value).toBe(testTime);\n  });\n  it('should set onChange in registerOnChange', () => {\n    let test = false;\n    const fn = () => {\n      test = true;\n    };\n    component.registerOnChange(fn);\n    component.writeValue(testTime);\n    expect(test).toBe(true);\n  });\n  it('should set onChange in registerOnTouched', () => {\n    let test = false;\n    const fn = () => {\n      test = true;\n    };\n    component.registerOnTouched(fn);\n    dispatchFakeEvent(host.nativeElement, 'focus');\n    dispatchFakeEvent(host.nativeElement, 'blur');\n    expect(test).toBe(true);\n  });\n  it('should disable form', () => {\n    component.setDisabledState(true);\n    expect(component.form.disabled).toBe(true);\n  });\n  it('should enable form', () => {\n    component.setDisabledState(false);\n    expect(component.form.disabled).toBe(false);\n  });\n  it('should not cut last symbol if days value.length > 2 && control !== hours && firstSymbol !== 0', () => {\n    daysControl.setValue('123');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(daysControl.value).toBe('123');\n  });\n  it('should set 00 if minutes euqals to 60', () => {\n    minutesControl.setValue('60');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(minutesControl.value).toBe('00');\n  });\n  it('should convert 66 minutes to 01 hour and 06 minutes', () => {\n    minutesControl.setValue('66');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(hoursControl.value).toBe('01');\n    expect(minutesControl.value).toBe('06');\n  });\n  it('should cut first symbol 0 if first symbol = 0  && value.length > 2', () => {\n    minutesControl.setValue('012');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(minutesControl.value).toBe('12');\n  });\n  it('should take a full hour and leave a minutes', () => {\n    minutesControl.setValue('066');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(minutesControl.value).toBe('06');\n  });\n  it('should count the hours to days and hours', () => {\n    hoursControl.setValue('066');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(daysControl.value).toBe('02');\n    expect(hoursControl.value).toBe('18');\n  });\n  it('should set max hours value if value > maxHoursValue && el === hours', () => {\n    hoursControl.setValue('24');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(hoursControl.value).toBe('00');\n  });\n  it('should set empty string if value is not digits', () => {\n    minutesControl.setValue('string value');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(minutesControl.value).toBe('00');\n  });\n  it('should set 0 value instead of negative', () => {\n    minutesControl.setValue('-20');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(minutesControl.value).toBe('00');\n  });\n  it('should not set valueAccessor if form control', () => {\n    fixture.componentInstance.ngControl = new UntypedFormControl(testTime);\n    expect(component.ngControl.valueAccessor).toBeUndefined();\n  });\n});\nfunction createFakeEvent(type) {\n  const event = document.createEvent('Event');\n  event.initEvent(type, true, true);\n  return event;\n}\nfunction dispatchFakeEvent(node, type) {\n  node.dispatchEvent(createFakeEvent(type));\n}", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "SwuiTimeDurationComponent", "CommonModule", "UntypedFormControl", "ReactiveFormsModule", "BrowserAnimationsModule", "coerceBooleanProperty", "coerceNumberProperty", "MatInputModule", "describe", "component", "fixture", "testTime", "host", "daysControl", "hoursControl", "minutesControl", "secondsControl", "formTestValue", "beforeEach", "configureTestingModule", "imports", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "debugElement", "days", "hours", "minutes", "seconds", "form", "get", "it", "expect", "toBeTruthy", "value", "toBe", "required", "disabled", "secondsDisabled", "placeholder", "empty", "errorState", "toBeFalsy", "nextSpy", "spyOn", "stateChanges", "toHaveBeenCalled", "controlType", "nativeElement", "getAttribute", "toBeDefined", "shouldLabelFloat", "dispatchFakeEvent", "classList", "contains", "onTouched", "setValue", "onChange", "completeSpy", "ngOnDestroy", "testIds", "setDescribedByIds", "describedBy", "join", "writeValue", "test", "fn", "registerOnChange", "registerOnTouched", "setDisabledState", "processInputValue", "ngControl", "valueAccessor", "toBeUndefined", "createFakeEvent", "type", "event", "document", "createEvent", "initEvent", "node", "dispatchEvent"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-time-duration/swui-time-duration.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\nimport { SwuiTimeDurationComponent } from './swui-time-duration.component';\nimport { CommonModule } from '@angular/common';\nimport { UntypedFormControl, ReactiveFormsModule } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { DebugElement } from '@angular/core';\nimport { MatInputModule } from '@angular/material/input';\n\n\ndescribe('SwuiTimeDurationComponent', () => {\n  let component: SwuiTimeDurationComponent;\n  let fixture: ComponentFixture<SwuiTimeDurationComponent>;\n  let testTime: number;\n  let host: DebugElement;\n  let daysControl: UntypedFormControl;\n  let hoursControl: UntypedFormControl;\n  let minutesControl: UntypedFormControl;\n  let secondsControl: UntypedFormControl;\n  let formTestValue: Object;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [\n        CommonModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        MatInputModule,\n      ],\n      declarations: [SwuiTimeDurationComponent]\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiTimeDurationComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    host = fixture.debugElement;\n    testTime = 1.296e+8;\n    formTestValue = { days: '0', hours: '0', minutes: '1', seconds: '10' };\n    daysControl = component.form.get('days') as UntypedFormControl;\n    hoursControl = component.form.get('hours') as UntypedFormControl;\n    minutesControl = component.form.get('minutes') as UntypedFormControl;\n    secondsControl = component.form.get('seconds') as UntypedFormControl;\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should set value', () => {\n    component.value = testTime;\n    expect(component.value).toBe(coerceNumberProperty(testTime, 0));\n  });\n\n  it('should set required', () => {\n    component.required = true;\n    expect(component.required).toBe(coerceBooleanProperty(true));\n  });\n\n  it('should set disabled', () => {\n    component.disabled = true;\n    expect(component.disabled).toBe(coerceBooleanProperty(true));\n  });\n\n  it('should set enabled', () => {\n    component.disabled = false;\n    expect(component.disabled).toBe(coerceBooleanProperty(false));\n  });\n\n  it('should disable inner form on disabled', () => {\n    component.disabled = true;\n    expect(component.form.disabled).toBe(true);\n  });\n\n  it('should enable inner form on disabled', () => {\n    component.disabled = false;\n    expect(component.form.disabled).toBe(false);\n  });\n\n  it('should disable seconds', () => {\n    component.secondsDisabled = true;\n    expect(secondsControl.disabled).toBe(true);\n  });\n\n  it('should set placeholder', () => {\n    component.placeholder = 'test';\n    expect(component.placeholder).toBe('test');\n  });\n\n  it('should get empty false if controls are null', () => {\n    component.value = null;\n    expect(component.empty).toBe(false);\n  });\n\n  it('should get empty false if controls are equals 0', () => {\n    component.value = 0;\n    expect(component.empty).toBe(false);\n  });\n\n  it('should get empty false if controls are not empty', () => {\n    component.value = testTime;\n    expect(component.empty).toBe(false);\n  });\n\n  it('should get error state false', () => {\n    expect(component.errorState).toBeFalsy();\n  });\n\n  it('should set state changes', () => {\n    const nextSpy = spyOn(component.stateChanges, 'next');\n    component.required = true;\n    expect(nextSpy).toHaveBeenCalled();\n  });\n\n  it('should controlType to be defined', () => {\n    expect(component.controlType).toBe('lib-swui-time-duration');\n  });\n\n  it('should set host id', () => {\n    expect(host.nativeElement.getAttribute('id')).toBeDefined();\n  });\n\n  it('should shouldLabelFloat to be true when not empty', () => {\n    component.value = testTime;\n    expect(component.shouldLabelFloat).toBe(true);\n  });\n\n  it('should shouldLabelFloat to be true when host focused', () => {\n    dispatchFakeEvent(host.nativeElement, 'focus');\n    expect(component.shouldLabelFloat).toBe(true);\n  });\n\n  it('should shouldLabelFloat return true', () => {\n    expect(component.shouldLabelFloat).toBe(true);\n  });\n\n  it('should set host class floating when it is not empty', () => {\n    component.value = testTime;\n    fixture.detectChanges();\n    expect(host.nativeElement.classList.contains('floating')).toBe(true);\n  });\n\n  it('should set host class floating when host focused', () => {\n    dispatchFakeEvent(host.nativeElement, 'focus');\n    fixture.detectChanges();\n    expect(host.nativeElement.classList.contains('floating')).toBe(true);\n  });\n\n  it('should set aria-describedby on host', () => {\n    expect(host.nativeElement.getAttribute('aria-describedby')).toBe('');\n  });\n\n  it('should call onTouched on focus', () => {\n    spyOn(component, 'onTouched');\n    dispatchFakeEvent(host.nativeElement, 'focus');\n    dispatchFakeEvent(host.nativeElement, 'blur');\n    expect(component.onTouched).toHaveBeenCalled();\n  });\n\n  it('should init form', () => {\n    expect(component.form).toBeDefined();\n  });\n\n  it('should call onChange onInit when form value changed', () => {\n    spyOn(component, 'onChange');\n    component.form.setValue(formTestValue);\n    expect(component.onChange).toHaveBeenCalled();\n  });\n\n  it('should set new value when form value changed', () => {\n    component.form.setValue(formTestValue);\n    expect(component.value).toBe(70000);\n  });\n\n  it('should complete state change on destroy', () => {\n    const completeSpy = spyOn(component.stateChanges, 'complete');\n    component.ngOnDestroy();\n    expect(completeSpy).toHaveBeenCalled();\n  });\n\n  it('should setDescribedByIds', () => {\n    const testIds = ['test1', 'test2'];\n    component.setDescribedByIds(testIds);\n    expect(component.describedBy).toBe(testIds.join(' '));\n  });\n\n  it('patchform on writevalue', () => {\n    component.writeValue(testTime);\n    expect(component.value).toBe(testTime);\n  });\n\n  it('should set onChange in registerOnChange', () => {\n    let test = false;\n    const fn = () => {\n      test = true;\n    };\n    component.registerOnChange(fn);\n    component.writeValue(testTime);\n    expect(test).toBe(true);\n  });\n\n  it('should set onChange in registerOnTouched', () => {\n    let test = false;\n    const fn = () => {\n      test = true;\n    };\n    component.registerOnTouched(fn);\n    dispatchFakeEvent(host.nativeElement, 'focus');\n    dispatchFakeEvent(host.nativeElement, 'blur');\n    expect(test).toBe(true);\n  });\n\n  it('should disable form', () => {\n    component.setDisabledState(true);\n    expect(component.form.disabled).toBe(true);\n  });\n\n  it('should enable form', () => {\n    component.setDisabledState(false);\n    expect(component.form.disabled).toBe(false);\n  });\n\n  it('should not cut last symbol if days value.length > 2 && control !== hours && firstSymbol !== 0', () => {\n    daysControl.setValue('123');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(daysControl.value).toBe('123');\n  });\n\n  it('should set 00 if minutes euqals to 60', () => {\n    minutesControl.setValue('60');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(minutesControl.value).toBe('00');\n  });\n\n  it('should convert 66 minutes to 01 hour and 06 minutes', () => {\n    minutesControl.setValue('66');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(hoursControl.value).toBe('01');\n    expect(minutesControl.value).toBe('06');\n  });\n\n  it('should cut first symbol 0 if first symbol = 0  && value.length > 2', () => {\n    minutesControl.setValue('012');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(minutesControl.value).toBe('12');\n  });\n\n  it('should take a full hour and leave a minutes', () => {\n    minutesControl.setValue('066');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(minutesControl.value).toBe('06');\n  });\n\n  it('should count the hours to days and hours', () => {\n    hoursControl.setValue('066');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(daysControl.value).toBe('02');\n    expect(hoursControl.value).toBe('18');\n  });\n\n  it('should set max hours value if value > maxHoursValue && el === hours', () => {\n    hoursControl.setValue('24');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(hoursControl.value).toBe('00');\n  });\n\n  it('should set empty string if value is not digits', () => {\n    minutesControl.setValue('string value');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(minutesControl.value).toBe('00');\n  });\n\n  it('should set 0 value instead of negative', () => {\n    minutesControl.setValue('-20');\n    fixture.detectChanges();\n    component.processInputValue();\n    expect(minutesControl.value).toBe('00');\n  });\n\n  it('should not set valueAccessor if form control', () => {\n    (fixture.componentInstance as any).ngControl = new UntypedFormControl(testTime);\n    expect(component.ngControl.valueAccessor).toBeUndefined();\n  });\n\n});\n\n\nfunction createFakeEvent( type: string ) {\n  const event = document.createEvent('Event');\n  event.initEvent(type, true, true);\n  return event;\n}\n\nfunction dispatchFakeEvent( node: Node | Window, type: string ) {\n  node.dispatchEvent(createFakeEvent(type));\n}\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAC/E,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,gBAAgB;AACxE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AAEnF,SAASC,cAAc,QAAQ,yBAAyB;AAGxDC,QAAQ,CAAC,2BAA2B,EAAE,MAAK;EACzC,IAAIC,SAAoC;EACxC,IAAIC,OAAoD;EACxD,IAAIC,QAAgB;EACpB,IAAIC,IAAkB;EACtB,IAAIC,WAA+B;EACnC,IAAIC,YAAgC;EACpC,IAAIC,cAAkC;EACtC,IAAIC,cAAkC;EACtC,IAAIC,aAAqB;EAEzBC,UAAU,CAACnB,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACqB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPnB,YAAY,EACZG,uBAAuB,EACvBD,mBAAmB,EACnBI,cAAc,CACf;MACDc,YAAY,EAAE,CAACrB,yBAAyB;KACzC,CAAC,CACCsB,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHJ,UAAU,CAAC,MAAK;IACdR,OAAO,GAAGZ,OAAO,CAACyB,eAAe,CAACvB,yBAAyB,CAAC;IAC5DS,SAAS,GAAGC,OAAO,CAACc,iBAAiB;IACrCd,OAAO,CAACe,aAAa,EAAE;IACvBb,IAAI,GAAGF,OAAO,CAACgB,YAAY;IAC3Bf,QAAQ,GAAG,QAAQ;IACnBM,aAAa,GAAG;MAAEU,IAAI,EAAE,GAAG;MAAEC,KAAK,EAAE,GAAG;MAAEC,OAAO,EAAE,GAAG;MAAEC,OAAO,EAAE;IAAI,CAAE;IACtEjB,WAAW,GAAGJ,SAAS,CAACsB,IAAI,CAACC,GAAG,CAAC,MAAM,CAAuB;IAC9DlB,YAAY,GAAGL,SAAS,CAACsB,IAAI,CAACC,GAAG,CAAC,OAAO,CAAuB;IAChEjB,cAAc,GAAGN,SAAS,CAACsB,IAAI,CAACC,GAAG,CAAC,SAAS,CAAuB;IACpEhB,cAAc,GAAGP,SAAS,CAACsB,IAAI,CAACC,GAAG,CAAC,SAAS,CAAuB;EACtE,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACzB,SAAS,CAAC,CAAC0B,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,kBAAkB,EAAE,MAAK;IAC1BxB,SAAS,CAAC2B,KAAK,GAAGzB,QAAQ;IAC1BuB,MAAM,CAACzB,SAAS,CAAC2B,KAAK,CAAC,CAACC,IAAI,CAAC/B,oBAAoB,CAACK,QAAQ,EAAE,CAAC,CAAC,CAAC;EACjE,CAAC,CAAC;EAEFsB,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7BxB,SAAS,CAAC6B,QAAQ,GAAG,IAAI;IACzBJ,MAAM,CAACzB,SAAS,CAAC6B,QAAQ,CAAC,CAACD,IAAI,CAAChC,qBAAqB,CAAC,IAAI,CAAC,CAAC;EAC9D,CAAC,CAAC;EAEF4B,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7BxB,SAAS,CAAC8B,QAAQ,GAAG,IAAI;IACzBL,MAAM,CAACzB,SAAS,CAAC8B,QAAQ,CAAC,CAACF,IAAI,CAAChC,qBAAqB,CAAC,IAAI,CAAC,CAAC;EAC9D,CAAC,CAAC;EAEF4B,EAAE,CAAC,oBAAoB,EAAE,MAAK;IAC5BxB,SAAS,CAAC8B,QAAQ,GAAG,KAAK;IAC1BL,MAAM,CAACzB,SAAS,CAAC8B,QAAQ,CAAC,CAACF,IAAI,CAAChC,qBAAqB,CAAC,KAAK,CAAC,CAAC;EAC/D,CAAC,CAAC;EAEF4B,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/CxB,SAAS,CAAC8B,QAAQ,GAAG,IAAI;IACzBL,MAAM,CAACzB,SAAS,CAACsB,IAAI,CAACQ,QAAQ,CAAC,CAACF,IAAI,CAAC,IAAI,CAAC;EAC5C,CAAC,CAAC;EAEFJ,EAAE,CAAC,sCAAsC,EAAE,MAAK;IAC9CxB,SAAS,CAAC8B,QAAQ,GAAG,KAAK;IAC1BL,MAAM,CAACzB,SAAS,CAACsB,IAAI,CAACQ,QAAQ,CAAC,CAACF,IAAI,CAAC,KAAK,CAAC;EAC7C,CAAC,CAAC;EAEFJ,EAAE,CAAC,wBAAwB,EAAE,MAAK;IAChCxB,SAAS,CAAC+B,eAAe,GAAG,IAAI;IAChCN,MAAM,CAAClB,cAAc,CAACuB,QAAQ,CAAC,CAACF,IAAI,CAAC,IAAI,CAAC;EAC5C,CAAC,CAAC;EAEFJ,EAAE,CAAC,wBAAwB,EAAE,MAAK;IAChCxB,SAAS,CAACgC,WAAW,GAAG,MAAM;IAC9BP,MAAM,CAACzB,SAAS,CAACgC,WAAW,CAAC,CAACJ,IAAI,CAAC,MAAM,CAAC;EAC5C,CAAC,CAAC;EAEFJ,EAAE,CAAC,6CAA6C,EAAE,MAAK;IACrDxB,SAAS,CAAC2B,KAAK,GAAG,IAAI;IACtBF,MAAM,CAACzB,SAAS,CAACiC,KAAK,CAAC,CAACL,IAAI,CAAC,KAAK,CAAC;EACrC,CAAC,CAAC;EAEFJ,EAAE,CAAC,iDAAiD,EAAE,MAAK;IACzDxB,SAAS,CAAC2B,KAAK,GAAG,CAAC;IACnBF,MAAM,CAACzB,SAAS,CAACiC,KAAK,CAAC,CAACL,IAAI,CAAC,KAAK,CAAC;EACrC,CAAC,CAAC;EAEFJ,EAAE,CAAC,kDAAkD,EAAE,MAAK;IAC1DxB,SAAS,CAAC2B,KAAK,GAAGzB,QAAQ;IAC1BuB,MAAM,CAACzB,SAAS,CAACiC,KAAK,CAAC,CAACL,IAAI,CAAC,KAAK,CAAC;EACrC,CAAC,CAAC;EAEFJ,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtCC,MAAM,CAACzB,SAAS,CAACkC,UAAU,CAAC,CAACC,SAAS,EAAE;EAC1C,CAAC,CAAC;EAEFX,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClC,MAAMY,OAAO,GAAGC,KAAK,CAACrC,SAAS,CAACsC,YAAY,EAAE,MAAM,CAAC;IACrDtC,SAAS,CAAC6B,QAAQ,GAAG,IAAI;IACzBJ,MAAM,CAACW,OAAO,CAAC,CAACG,gBAAgB,EAAE;EACpC,CAAC,CAAC;EAEFf,EAAE,CAAC,kCAAkC,EAAE,MAAK;IAC1CC,MAAM,CAACzB,SAAS,CAACwC,WAAW,CAAC,CAACZ,IAAI,CAAC,wBAAwB,CAAC;EAC9D,CAAC,CAAC;EAEFJ,EAAE,CAAC,oBAAoB,EAAE,MAAK;IAC5BC,MAAM,CAACtB,IAAI,CAACsC,aAAa,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC,CAACC,WAAW,EAAE;EAC7D,CAAC,CAAC;EAEFnB,EAAE,CAAC,mDAAmD,EAAE,MAAK;IAC3DxB,SAAS,CAAC2B,KAAK,GAAGzB,QAAQ;IAC1BuB,MAAM,CAACzB,SAAS,CAAC4C,gBAAgB,CAAC,CAAChB,IAAI,CAAC,IAAI,CAAC;EAC/C,CAAC,CAAC;EAEFJ,EAAE,CAAC,sDAAsD,EAAE,MAAK;IAC9DqB,iBAAiB,CAAC1C,IAAI,CAACsC,aAAa,EAAE,OAAO,CAAC;IAC9ChB,MAAM,CAACzB,SAAS,CAAC4C,gBAAgB,CAAC,CAAChB,IAAI,CAAC,IAAI,CAAC;EAC/C,CAAC,CAAC;EAEFJ,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7CC,MAAM,CAACzB,SAAS,CAAC4C,gBAAgB,CAAC,CAAChB,IAAI,CAAC,IAAI,CAAC;EAC/C,CAAC,CAAC;EAEFJ,EAAE,CAAC,qDAAqD,EAAE,MAAK;IAC7DxB,SAAS,CAAC2B,KAAK,GAAGzB,QAAQ;IAC1BD,OAAO,CAACe,aAAa,EAAE;IACvBS,MAAM,CAACtB,IAAI,CAACsC,aAAa,CAACK,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAACnB,IAAI,CAAC,IAAI,CAAC;EACtE,CAAC,CAAC;EAEFJ,EAAE,CAAC,kDAAkD,EAAE,MAAK;IAC1DqB,iBAAiB,CAAC1C,IAAI,CAACsC,aAAa,EAAE,OAAO,CAAC;IAC9CxC,OAAO,CAACe,aAAa,EAAE;IACvBS,MAAM,CAACtB,IAAI,CAACsC,aAAa,CAACK,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAACnB,IAAI,CAAC,IAAI,CAAC;EACtE,CAAC,CAAC;EAEFJ,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7CC,MAAM,CAACtB,IAAI,CAACsC,aAAa,CAACC,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAACd,IAAI,CAAC,EAAE,CAAC;EACtE,CAAC,CAAC;EAEFJ,EAAE,CAAC,gCAAgC,EAAE,MAAK;IACxCa,KAAK,CAACrC,SAAS,EAAE,WAAW,CAAC;IAC7B6C,iBAAiB,CAAC1C,IAAI,CAACsC,aAAa,EAAE,OAAO,CAAC;IAC9CI,iBAAiB,CAAC1C,IAAI,CAACsC,aAAa,EAAE,MAAM,CAAC;IAC7ChB,MAAM,CAACzB,SAAS,CAACgD,SAAS,CAAC,CAACT,gBAAgB,EAAE;EAChD,CAAC,CAAC;EAEFf,EAAE,CAAC,kBAAkB,EAAE,MAAK;IAC1BC,MAAM,CAACzB,SAAS,CAACsB,IAAI,CAAC,CAACqB,WAAW,EAAE;EACtC,CAAC,CAAC;EAEFnB,EAAE,CAAC,qDAAqD,EAAE,MAAK;IAC7Da,KAAK,CAACrC,SAAS,EAAE,UAAU,CAAC;IAC5BA,SAAS,CAACsB,IAAI,CAAC2B,QAAQ,CAACzC,aAAa,CAAC;IACtCiB,MAAM,CAACzB,SAAS,CAACkD,QAAQ,CAAC,CAACX,gBAAgB,EAAE;EAC/C,CAAC,CAAC;EAEFf,EAAE,CAAC,8CAA8C,EAAE,MAAK;IACtDxB,SAAS,CAACsB,IAAI,CAAC2B,QAAQ,CAACzC,aAAa,CAAC;IACtCiB,MAAM,CAACzB,SAAS,CAAC2B,KAAK,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;EACrC,CAAC,CAAC;EAEFJ,EAAE,CAAC,yCAAyC,EAAE,MAAK;IACjD,MAAM2B,WAAW,GAAGd,KAAK,CAACrC,SAAS,CAACsC,YAAY,EAAE,UAAU,CAAC;IAC7DtC,SAAS,CAACoD,WAAW,EAAE;IACvB3B,MAAM,CAAC0B,WAAW,CAAC,CAACZ,gBAAgB,EAAE;EACxC,CAAC,CAAC;EAEFf,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClC,MAAM6B,OAAO,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;IAClCrD,SAAS,CAACsD,iBAAiB,CAACD,OAAO,CAAC;IACpC5B,MAAM,CAACzB,SAAS,CAACuD,WAAW,CAAC,CAAC3B,IAAI,CAACyB,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,CAAC;EACvD,CAAC,CAAC;EAEFhC,EAAE,CAAC,yBAAyB,EAAE,MAAK;IACjCxB,SAAS,CAACyD,UAAU,CAACvD,QAAQ,CAAC;IAC9BuB,MAAM,CAACzB,SAAS,CAAC2B,KAAK,CAAC,CAACC,IAAI,CAAC1B,QAAQ,CAAC;EACxC,CAAC,CAAC;EAEFsB,EAAE,CAAC,yCAAyC,EAAE,MAAK;IACjD,IAAIkC,IAAI,GAAG,KAAK;IAChB,MAAMC,EAAE,GAAGA,CAAA,KAAK;MACdD,IAAI,GAAG,IAAI;IACb,CAAC;IACD1D,SAAS,CAAC4D,gBAAgB,CAACD,EAAE,CAAC;IAC9B3D,SAAS,CAACyD,UAAU,CAACvD,QAAQ,CAAC;IAC9BuB,MAAM,CAACiC,IAAI,CAAC,CAAC9B,IAAI,CAAC,IAAI,CAAC;EACzB,CAAC,CAAC;EAEFJ,EAAE,CAAC,0CAA0C,EAAE,MAAK;IAClD,IAAIkC,IAAI,GAAG,KAAK;IAChB,MAAMC,EAAE,GAAGA,CAAA,KAAK;MACdD,IAAI,GAAG,IAAI;IACb,CAAC;IACD1D,SAAS,CAAC6D,iBAAiB,CAACF,EAAE,CAAC;IAC/Bd,iBAAiB,CAAC1C,IAAI,CAACsC,aAAa,EAAE,OAAO,CAAC;IAC9CI,iBAAiB,CAAC1C,IAAI,CAACsC,aAAa,EAAE,MAAM,CAAC;IAC7ChB,MAAM,CAACiC,IAAI,CAAC,CAAC9B,IAAI,CAAC,IAAI,CAAC;EACzB,CAAC,CAAC;EAEFJ,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7BxB,SAAS,CAAC8D,gBAAgB,CAAC,IAAI,CAAC;IAChCrC,MAAM,CAACzB,SAAS,CAACsB,IAAI,CAACQ,QAAQ,CAAC,CAACF,IAAI,CAAC,IAAI,CAAC;EAC5C,CAAC,CAAC;EAEFJ,EAAE,CAAC,oBAAoB,EAAE,MAAK;IAC5BxB,SAAS,CAAC8D,gBAAgB,CAAC,KAAK,CAAC;IACjCrC,MAAM,CAACzB,SAAS,CAACsB,IAAI,CAACQ,QAAQ,CAAC,CAACF,IAAI,CAAC,KAAK,CAAC;EAC7C,CAAC,CAAC;EAEFJ,EAAE,CAAC,+FAA+F,EAAE,MAAK;IACvGpB,WAAW,CAAC6C,QAAQ,CAAC,KAAK,CAAC;IAC3BhD,OAAO,CAACe,aAAa,EAAE;IACvBhB,SAAS,CAAC+D,iBAAiB,EAAE;IAC7BtC,MAAM,CAACrB,WAAW,CAACuB,KAAK,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;EACvC,CAAC,CAAC;EAEFJ,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/ClB,cAAc,CAAC2C,QAAQ,CAAC,IAAI,CAAC;IAC7BhD,OAAO,CAACe,aAAa,EAAE;IACvBhB,SAAS,CAAC+D,iBAAiB,EAAE;IAC7BtC,MAAM,CAACnB,cAAc,CAACqB,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACzC,CAAC,CAAC;EAEFJ,EAAE,CAAC,qDAAqD,EAAE,MAAK;IAC7DlB,cAAc,CAAC2C,QAAQ,CAAC,IAAI,CAAC;IAC7BhD,OAAO,CAACe,aAAa,EAAE;IACvBhB,SAAS,CAAC+D,iBAAiB,EAAE;IAC7BtC,MAAM,CAACpB,YAAY,CAACsB,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACrCH,MAAM,CAACnB,cAAc,CAACqB,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACzC,CAAC,CAAC;EAEFJ,EAAE,CAAC,oEAAoE,EAAE,MAAK;IAC5ElB,cAAc,CAAC2C,QAAQ,CAAC,KAAK,CAAC;IAC9BhD,OAAO,CAACe,aAAa,EAAE;IACvBhB,SAAS,CAAC+D,iBAAiB,EAAE;IAC7BtC,MAAM,CAACnB,cAAc,CAACqB,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACzC,CAAC,CAAC;EAEFJ,EAAE,CAAC,6CAA6C,EAAE,MAAK;IACrDlB,cAAc,CAAC2C,QAAQ,CAAC,KAAK,CAAC;IAC9BhD,OAAO,CAACe,aAAa,EAAE;IACvBhB,SAAS,CAAC+D,iBAAiB,EAAE;IAC7BtC,MAAM,CAACnB,cAAc,CAACqB,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACzC,CAAC,CAAC;EAEFJ,EAAE,CAAC,0CAA0C,EAAE,MAAK;IAClDnB,YAAY,CAAC4C,QAAQ,CAAC,KAAK,CAAC;IAC5BhD,OAAO,CAACe,aAAa,EAAE;IACvBhB,SAAS,CAAC+D,iBAAiB,EAAE;IAC7BtC,MAAM,CAACrB,WAAW,CAACuB,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACpCH,MAAM,CAACpB,YAAY,CAACsB,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACvC,CAAC,CAAC;EAEFJ,EAAE,CAAC,qEAAqE,EAAE,MAAK;IAC7EnB,YAAY,CAAC4C,QAAQ,CAAC,IAAI,CAAC;IAC3BhD,OAAO,CAACe,aAAa,EAAE;IACvBhB,SAAS,CAAC+D,iBAAiB,EAAE;IAC7BtC,MAAM,CAACpB,YAAY,CAACsB,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACvC,CAAC,CAAC;EAEFJ,EAAE,CAAC,gDAAgD,EAAE,MAAK;IACxDlB,cAAc,CAAC2C,QAAQ,CAAC,cAAc,CAAC;IACvChD,OAAO,CAACe,aAAa,EAAE;IACvBhB,SAAS,CAAC+D,iBAAiB,EAAE;IAC7BtC,MAAM,CAACnB,cAAc,CAACqB,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACzC,CAAC,CAAC;EAEFJ,EAAE,CAAC,wCAAwC,EAAE,MAAK;IAChDlB,cAAc,CAAC2C,QAAQ,CAAC,KAAK,CAAC;IAC9BhD,OAAO,CAACe,aAAa,EAAE;IACvBhB,SAAS,CAAC+D,iBAAiB,EAAE;IAC7BtC,MAAM,CAACnB,cAAc,CAACqB,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACzC,CAAC,CAAC;EAEFJ,EAAE,CAAC,8CAA8C,EAAE,MAAK;IACrDvB,OAAO,CAACc,iBAAyB,CAACiD,SAAS,GAAG,IAAIvE,kBAAkB,CAACS,QAAQ,CAAC;IAC/EuB,MAAM,CAACzB,SAAS,CAACgE,SAAS,CAACC,aAAa,CAAC,CAACC,aAAa,EAAE;EAC3D,CAAC,CAAC;AAEJ,CAAC,CAAC;AAGF,SAASC,eAAeA,CAAEC,IAAY;EACpC,MAAMC,KAAK,GAAGC,QAAQ,CAACC,WAAW,CAAC,OAAO,CAAC;EAC3CF,KAAK,CAACG,SAAS,CAACJ,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjC,OAAOC,KAAK;AACd;AAEA,SAASxB,iBAAiBA,CAAE4B,IAAmB,EAAEL,IAAY;EAC3DK,IAAI,CAACC,aAAa,CAACP,eAAe,CAACC,IAAI,CAAC,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}