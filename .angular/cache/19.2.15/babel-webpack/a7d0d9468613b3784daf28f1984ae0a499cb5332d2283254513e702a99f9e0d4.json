{"ast": null, "code": "var _SwuiSettingsDialogComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-settings-dialog.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-settings-dialog.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup } from '@angular/forms';\nimport { isEqual } from 'lodash';\nimport * as moment from 'moment';\nimport 'moment-timezone';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { DEFAULT_SETTINGS, SettingsService } from '../../services/settings/settings.service';\nconst TIMEZONES = moment.tz.names().filter(name => name.indexOf('Etc') === -1).map(name => {\n  const zone = moment.tz(name);\n  return {\n    text: `${zone.format('Z')} ${name}`,\n    zone: zone.utcOffset(),\n    id: name\n  };\n}).sort((a, b) => {\n  const zoneDiff = b.zone - a.zone;\n  if (zoneDiff !== 0) {\n    return zoneDiff;\n  }\n  if (a.text > b.text) {\n    return 1;\n  }\n  if (a.text < b.text) {\n    return -1;\n  }\n  return 0;\n});\nconst TIME_FORMATS = ['HH:mm:ss', 'HH:mm', 'HH.mm.ss', 'HH.mm'].map(format => ({\n  text: moment.utc().format(format),\n  id: format\n}));\nconst DATE_FORMATS = ['DD.MM.YYYY', 'DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY/MM/DD', 'YY/MM/DD'].map(format => ({\n  text: moment.utc().format(format),\n  id: format\n}));\nconst EXAMPLE_NUMBER = 1234567.89;\nconst CURRENCY_FORMATS = ['de-DE', 'zh-CN', 'ru-RU'].map(format => {\n  const toLocale = lang => EXAMPLE_NUMBER.toLocaleString(lang, {\n    minimumFractionDigits: 2\n  });\n  const text = toLocale(format);\n  const primary = text === toLocale(window.navigator.language);\n  return {\n    id: primary ? window.navigator.language : format,\n    text,\n    primary\n  };\n});\nlet SwuiSettingsDialogComponent = (_SwuiSettingsDialogComponent = class SwuiSettingsDialogComponent {\n  constructor(service) {\n    this.service = service;\n    this.destroyed = new Subject();\n    this.form = new UntypedFormGroup({\n      timezoneName: new UntypedFormControl(''),\n      pageSize: new UntypedFormControl(''),\n      dateFormat: new UntypedFormControl(''),\n      timeFormat: new UntypedFormControl(''),\n      currencyFormat: new UntypedFormControl('')\n    });\n  }\n  ngOnInit() {\n    this.service.appSettings$.pipe(takeUntil(this.destroyed)).subscribe(settings => {\n      this.setValue(settings);\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed.next(undefined);\n    this.destroyed.complete();\n  }\n  get timezoneOptions() {\n    return TIMEZONES;\n  }\n  get pageSizeOptions() {\n    return [10, 20, 30, 50, 100];\n  }\n  get dateFormatOptions() {\n    return DATE_FORMATS;\n  }\n  get timeFormatOptions() {\n    return TIME_FORMATS;\n  }\n  get currencyFormatOptions() {\n    return CURRENCY_FORMATS;\n  }\n  get hasDefaultValues() {\n    return isEqual(this.form.value, DEFAULT_SETTINGS);\n  }\n  onReset(event) {\n    event.preventDefault();\n    this.setValue(DEFAULT_SETTINGS);\n    this.form.markAsDirty();\n  }\n  setValue(settings) {\n    this.form.setValue({\n      timezoneName: settings.timezoneName,\n      pageSize: settings.pageSize,\n      dateFormat: settings.dateFormat,\n      timeFormat: settings.timeFormat,\n      currencyFormat: settings.currencyFormat\n    }, {\n      emitEvent: false\n    });\n  }\n}, _SwuiSettingsDialogComponent.ctorParameters = () => [{\n  type: SettingsService\n}], _SwuiSettingsDialogComponent);\nSwuiSettingsDialogComponent = __decorate([Component({\n  selector: 'lib-swui-settings-dialog',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSettingsDialogComponent);\nexport { SwuiSettingsDialogComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectionStrategy", "Component", "UntypedFormControl", "UntypedFormGroup", "isEqual", "moment", "Subject", "takeUntil", "DEFAULT_SETTINGS", "SettingsService", "TIMEZONES", "tz", "names", "filter", "name", "indexOf", "map", "zone", "text", "format", "utcOffset", "id", "sort", "a", "b", "zoneDiff", "TIME_FORMATS", "utc", "DATE_FORMATS", "EXAMPLE_NUMBER", "CURRENCY_FORMATS", "toLocale", "lang", "toLocaleString", "minimumFractionDigits", "primary", "window", "navigator", "language", "SwuiSettingsDialogComponent", "_SwuiSettingsDialogComponent", "constructor", "service", "destroyed", "form", "timezoneName", "pageSize", "dateFormat", "timeFormat", "currencyFormat", "ngOnInit", "appSettings$", "pipe", "subscribe", "settings", "setValue", "ngOnDestroy", "next", "undefined", "complete", "timezoneOptions", "pageSizeOptions", "dateFormatOptions", "timeFormatOptions", "currencyFormatOptions", "hasDefault<PERSON><PERSON>ues", "value", "onReset", "event", "preventDefault", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emitEvent", "ctorParameters", "type", "selector", "template", "changeDetection", "OnPush", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/settings-dialog/swui-settings-dialog.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-settings-dialog.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-settings-dialog.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup } from '@angular/forms';\nimport { isEqual } from 'lodash';\nimport * as moment from 'moment';\nimport 'moment-timezone';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { DEFAULT_SETTINGS, SettingsService } from '../../services/settings/settings.service';\nconst TIMEZONES = moment.tz.names()\n    .filter(name => name.indexOf('Etc') === -1)\n    .map(name => {\n    const zone = moment.tz(name);\n    return {\n        text: `${zone.format('Z')} ${name}`,\n        zone: zone.utcOffset(),\n        id: name\n    };\n})\n    .sort((a, b) => {\n    const zoneDiff = b.zone - a.zone;\n    if (zoneDiff !== 0) {\n        return zoneDiff;\n    }\n    if (a.text > b.text) {\n        return 1;\n    }\n    if (a.text < b.text) {\n        return -1;\n    }\n    return 0;\n});\nconst TIME_FORMATS = ['HH:mm:ss', 'HH:mm', 'HH.mm.ss', 'HH.mm'].map(format => ({\n    text: moment.utc().format(format),\n    id: format\n}));\nconst DATE_FORMATS = ['DD.MM.YYYY', 'DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY/MM/DD', 'YY/MM/DD'].map(format => ({\n    text: moment.utc().format(format),\n    id: format\n}));\nconst EXAMPLE_NUMBER = 1234567.89;\nconst CURRENCY_FORMATS = ['de-DE', 'zh-CN', 'ru-RU'].map(format => {\n    const toLocale = (lang) => EXAMPLE_NUMBER.toLocaleString(lang, { minimumFractionDigits: 2 });\n    const text = toLocale(format);\n    const primary = text === toLocale(window.navigator.language);\n    return {\n        id: primary ? window.navigator.language : format,\n        text,\n        primary\n    };\n});\nlet SwuiSettingsDialogComponent = class SwuiSettingsDialogComponent {\n    constructor(service) {\n        this.service = service;\n        this.destroyed = new Subject();\n        this.form = new UntypedFormGroup({\n            timezoneName: new UntypedFormControl(''),\n            pageSize: new UntypedFormControl(''),\n            dateFormat: new UntypedFormControl(''),\n            timeFormat: new UntypedFormControl(''),\n            currencyFormat: new UntypedFormControl('')\n        });\n    }\n    ngOnInit() {\n        this.service.appSettings$.pipe(takeUntil(this.destroyed)).subscribe(settings => {\n            this.setValue(settings);\n        });\n    }\n    ngOnDestroy() {\n        this.destroyed.next(undefined);\n        this.destroyed.complete();\n    }\n    get timezoneOptions() {\n        return TIMEZONES;\n    }\n    get pageSizeOptions() {\n        return [10, 20, 30, 50, 100];\n    }\n    get dateFormatOptions() {\n        return DATE_FORMATS;\n    }\n    get timeFormatOptions() {\n        return TIME_FORMATS;\n    }\n    get currencyFormatOptions() {\n        return CURRENCY_FORMATS;\n    }\n    get hasDefaultValues() {\n        return isEqual(this.form.value, DEFAULT_SETTINGS);\n    }\n    onReset(event) {\n        event.preventDefault();\n        this.setValue(DEFAULT_SETTINGS);\n        this.form.markAsDirty();\n    }\n    setValue(settings) {\n        this.form.setValue({\n            timezoneName: settings.timezoneName,\n            pageSize: settings.pageSize,\n            dateFormat: settings.dateFormat,\n            timeFormat: settings.timeFormat,\n            currencyFormat: settings.currencyFormat\n        }, { emitEvent: false });\n    }\n    static { this.ctorParameters = () => [\n        { type: SettingsService }\n    ]; }\n};\nSwuiSettingsDialogComponent = __decorate([\n    Component({\n        selector: 'lib-swui-settings-dialog',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiSettingsDialogComponent);\nexport { SwuiSettingsDialogComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,kDAAkD;AACnF,OAAOC,oBAAoB,MAAM,kDAAkD;AACnF,SAASC,uBAAuB,EAAEC,SAAS,QAAQ,eAAe;AAClE,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,gBAAgB;AACrE,SAASC,OAAO,QAAQ,QAAQ;AAChC,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,OAAO,iBAAiB;AACxB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,gBAAgB,EAAEC,eAAe,QAAQ,0CAA0C;AAC5F,MAAMC,SAAS,GAAGL,MAAM,CAACM,EAAE,CAACC,KAAK,CAAC,CAAC,CAC9BC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAC1CC,GAAG,CAACF,IAAI,IAAI;EACb,MAAMG,IAAI,GAAGZ,MAAM,CAACM,EAAE,CAACG,IAAI,CAAC;EAC5B,OAAO;IACHI,IAAI,EAAE,GAAGD,IAAI,CAACE,MAAM,CAAC,GAAG,CAAC,IAAIL,IAAI,EAAE;IACnCG,IAAI,EAAEA,IAAI,CAACG,SAAS,CAAC,CAAC;IACtBC,EAAE,EAAEP;EACR,CAAC;AACL,CAAC,CAAC,CACGQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EAChB,MAAMC,QAAQ,GAAGD,CAAC,CAACP,IAAI,GAAGM,CAAC,CAACN,IAAI;EAChC,IAAIQ,QAAQ,KAAK,CAAC,EAAE;IAChB,OAAOA,QAAQ;EACnB;EACA,IAAIF,CAAC,CAACL,IAAI,GAAGM,CAAC,CAACN,IAAI,EAAE;IACjB,OAAO,CAAC;EACZ;EACA,IAAIK,CAAC,CAACL,IAAI,GAAGM,CAAC,CAACN,IAAI,EAAE;IACjB,OAAO,CAAC,CAAC;EACb;EACA,OAAO,CAAC;AACZ,CAAC,CAAC;AACF,MAAMQ,YAAY,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAACV,GAAG,CAACG,MAAM,KAAK;EAC3ED,IAAI,EAAEb,MAAM,CAACsB,GAAG,CAAC,CAAC,CAACR,MAAM,CAACA,MAAM,CAAC;EACjCE,EAAE,EAAEF;AACR,CAAC,CAAC,CAAC;AACH,MAAMS,YAAY,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,CAAC,CAACZ,GAAG,CAACG,MAAM,KAAK;EACrGD,IAAI,EAAEb,MAAM,CAACsB,GAAG,CAAC,CAAC,CAACR,MAAM,CAACA,MAAM,CAAC;EACjCE,EAAE,EAAEF;AACR,CAAC,CAAC,CAAC;AACH,MAAMU,cAAc,GAAG,UAAU;AACjC,MAAMC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAACd,GAAG,CAACG,MAAM,IAAI;EAC/D,MAAMY,QAAQ,GAAIC,IAAI,IAAKH,cAAc,CAACI,cAAc,CAACD,IAAI,EAAE;IAAEE,qBAAqB,EAAE;EAAE,CAAC,CAAC;EAC5F,MAAMhB,IAAI,GAAGa,QAAQ,CAACZ,MAAM,CAAC;EAC7B,MAAMgB,OAAO,GAAGjB,IAAI,KAAKa,QAAQ,CAACK,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAC;EAC5D,OAAO;IACHjB,EAAE,EAAEc,OAAO,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,GAAGnB,MAAM;IAChDD,IAAI;IACJiB;EACJ,CAAC;AACL,CAAC,CAAC;AACF,IAAII,2BAA2B,IAAAC,4BAAA,GAAG,MAAMD,2BAA2B,CAAC;EAChEE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAG,IAAIrC,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACsC,IAAI,GAAG,IAAIzC,gBAAgB,CAAC;MAC7B0C,YAAY,EAAE,IAAI3C,kBAAkB,CAAC,EAAE,CAAC;MACxC4C,QAAQ,EAAE,IAAI5C,kBAAkB,CAAC,EAAE,CAAC;MACpC6C,UAAU,EAAE,IAAI7C,kBAAkB,CAAC,EAAE,CAAC;MACtC8C,UAAU,EAAE,IAAI9C,kBAAkB,CAAC,EAAE,CAAC;MACtC+C,cAAc,EAAE,IAAI/C,kBAAkB,CAAC,EAAE;IAC7C,CAAC,CAAC;EACN;EACAgD,QAAQA,CAAA,EAAG;IACP,IAAI,CAACR,OAAO,CAACS,YAAY,CAACC,IAAI,CAAC7C,SAAS,CAAC,IAAI,CAACoC,SAAS,CAAC,CAAC,CAACU,SAAS,CAACC,QAAQ,IAAI;MAC5E,IAAI,CAACC,QAAQ,CAACD,QAAQ,CAAC;IAC3B,CAAC,CAAC;EACN;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACb,SAAS,CAACc,IAAI,CAACC,SAAS,CAAC;IAC9B,IAAI,CAACf,SAAS,CAACgB,QAAQ,CAAC,CAAC;EAC7B;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAOlD,SAAS;EACpB;EACA,IAAImD,eAAeA,CAAA,EAAG;IAClB,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAChC;EACA,IAAIC,iBAAiBA,CAAA,EAAG;IACpB,OAAOlC,YAAY;EACvB;EACA,IAAImC,iBAAiBA,CAAA,EAAG;IACpB,OAAOrC,YAAY;EACvB;EACA,IAAIsC,qBAAqBA,CAAA,EAAG;IACxB,OAAOlC,gBAAgB;EAC3B;EACA,IAAImC,gBAAgBA,CAAA,EAAG;IACnB,OAAO7D,OAAO,CAAC,IAAI,CAACwC,IAAI,CAACsB,KAAK,EAAE1D,gBAAgB,CAAC;EACrD;EACA2D,OAAOA,CAACC,KAAK,EAAE;IACXA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACd,QAAQ,CAAC/C,gBAAgB,CAAC;IAC/B,IAAI,CAACoC,IAAI,CAAC0B,WAAW,CAAC,CAAC;EAC3B;EACAf,QAAQA,CAACD,QAAQ,EAAE;IACf,IAAI,CAACV,IAAI,CAACW,QAAQ,CAAC;MACfV,YAAY,EAAES,QAAQ,CAACT,YAAY;MACnCC,QAAQ,EAAEQ,QAAQ,CAACR,QAAQ;MAC3BC,UAAU,EAAEO,QAAQ,CAACP,UAAU;MAC/BC,UAAU,EAAEM,QAAQ,CAACN,UAAU;MAC/BC,cAAc,EAAEK,QAAQ,CAACL;IAC7B,CAAC,EAAE;MAAEsB,SAAS,EAAE;IAAM,CAAC,CAAC;EAC5B;AAIJ,CAAC,EAHY/B,4BAAA,CAAKgC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEhE;AAAgB,CAAC,CAC5B,EAAA+B,4BAAA,CACJ;AACDD,2BAA2B,GAAG1C,UAAU,CAAC,CACrCI,SAAS,CAAC;EACNyE,QAAQ,EAAE,0BAA0B;EACpCC,QAAQ,EAAE7E,oBAAoB;EAC9B8E,eAAe,EAAE5E,uBAAuB,CAAC6E,MAAM;EAC/CC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAChF,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEwC,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}