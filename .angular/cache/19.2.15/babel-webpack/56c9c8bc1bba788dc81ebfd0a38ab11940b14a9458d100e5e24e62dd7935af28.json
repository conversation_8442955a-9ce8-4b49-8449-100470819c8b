{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatToolbarRow, _MatToolbar, _MatToolbarModule;\nconst _c0 = [\"*\", [[\"mat-toolbar-row\"]]];\nconst _c1 = [\"*\", \"mat-toolbar-row\"];\nimport * as i0 from '@angular/core';\nimport { Directive, inject, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, NgModule } from '@angular/core';\nimport { Platform } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nclass MatToolbarRow {}\n_MatToolbarRow = MatToolbarRow;\n_defineProperty(MatToolbarRow, \"\\u0275fac\", function _MatToolbarRow_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatToolbarRow)();\n});\n_defineProperty(MatToolbarRow, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatToolbarRow,\n  selectors: [[\"mat-toolbar-row\"]],\n  hostAttrs: [1, \"mat-toolbar-row\"],\n  exportAs: [\"matToolbarRow\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbarRow, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-toolbar-row',\n      exportAs: 'matToolbarRow',\n      host: {\n        'class': 'mat-toolbar-row'\n      }\n    }]\n  }], null, null);\n})();\nclass MatToolbar {\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /**\n     * Theme color of the toolbar. This API is supported in M2 themes only, it has\n     * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/toolbar/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    _defineProperty(this, \"color\", void 0);\n    /** Reference to all toolbar row elements that have been projected. */\n    _defineProperty(this, \"_toolbarRows\", void 0);\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._checkToolbarMixedModes();\n      this._toolbarRows.changes.subscribe(() => this._checkToolbarMixedModes());\n    }\n  }\n  /**\n   * Throws an exception when developers are attempting to combine the different toolbar row modes.\n   */\n  _checkToolbarMixedModes() {\n    if (this._toolbarRows.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      // Check if there are any other DOM nodes that can display content but aren't inside of\n      // a <mat-toolbar-row> element.\n      const isCombinedUsage = Array.from(this._elementRef.nativeElement.childNodes).filter(node => !(node.classList && node.classList.contains('mat-toolbar-row'))).filter(node => node.nodeType !== (this._document ? this._document.COMMENT_NODE : 8)).some(node => !!(node.textContent && node.textContent.trim()));\n      if (isCombinedUsage) {\n        throwToolbarMixedModesError();\n      }\n    }\n  }\n}\n_MatToolbar = MatToolbar;\n_defineProperty(MatToolbar, \"\\u0275fac\", function _MatToolbar_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatToolbar)();\n});\n_defineProperty(MatToolbar, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatToolbar,\n  selectors: [[\"mat-toolbar\"]],\n  contentQueries: function _MatToolbar_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatToolbarRow, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._toolbarRows = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-toolbar\"],\n  hostVars: 6,\n  hostBindings: function _MatToolbar_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n      i0.ɵɵclassProp(\"mat-toolbar-multiple-rows\", ctx._toolbarRows.length > 0)(\"mat-toolbar-single-row\", ctx._toolbarRows.length === 0);\n    }\n  },\n  inputs: {\n    color: \"color\"\n  },\n  exportAs: [\"matToolbar\"],\n  ngContentSelectors: _c1,\n  decls: 2,\n  vars: 0,\n  template: function _MatToolbar_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c0);\n      i0.ɵɵprojection(0);\n      i0.ɵɵprojection(1, 1);\n    }\n  },\n  styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mdc-text-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mdc-outlined-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbar, [{\n    type: Component,\n    args: [{\n      selector: 'mat-toolbar',\n      exportAs: 'matToolbar',\n      host: {\n        'class': 'mat-toolbar',\n        '[class]': 'color ? \"mat-\" + color : \"\"',\n        '[class.mat-toolbar-multiple-rows]': '_toolbarRows.length > 0',\n        '[class.mat-toolbar-single-row]': '_toolbarRows.length === 0'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<ng-content></ng-content>\\n<ng-content select=\\\"mat-toolbar-row\\\"></ng-content>\\n\",\n      styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mdc-text-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mdc-outlined-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}\\n\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    _toolbarRows: [{\n      type: ContentChildren,\n      args: [MatToolbarRow, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * Throws an exception when attempting to combine the different toolbar row modes.\n * @docs-private\n */\nfunction throwToolbarMixedModesError() {\n  throw Error('MatToolbar: Attempting to combine different toolbar modes. ' + 'Either specify multiple `<mat-toolbar-row>` elements explicitly or just place content ' + 'inside of a `<mat-toolbar>` for a single row.');\n}\nclass MatToolbarModule {}\n_MatToolbarModule = MatToolbarModule;\n_defineProperty(MatToolbarModule, \"\\u0275fac\", function _MatToolbarModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatToolbarModule)();\n});\n_defineProperty(MatToolbarModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatToolbarModule,\n  imports: [MatCommonModule, MatToolbar, MatToolbarRow],\n  exports: [MatToolbar, MatToolbarRow, MatCommonModule]\n}));\n_defineProperty(MatToolbarModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatToolbar, MatToolbarRow],\n      exports: [MatToolbar, MatToolbarRow, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatToolbar, MatToolbarModule, MatToolbarRow, throwToolbarMixedModesError };\n//# sourceMappingURL=toolbar.mjs.map", "map": {"version": 3, "names": ["i0", "Directive", "inject", "ElementRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ContentChildren", "NgModule", "Platform", "DOCUMENT", "M", "MatCommonModule", "MatToolbarRow", "_MatToolbarRow", "_defineProperty", "_MatToolbarRow_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "exportAs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "MatToolbar", "constructor", "ngAfterViewInit", "_platform", "<PERSON><PERSON><PERSON><PERSON>", "_checkToolbarMixedModes", "_toolbarRows", "changes", "subscribe", "length", "isCombinedUsage", "Array", "from", "_elementRef", "nativeElement", "childNodes", "filter", "node", "classList", "contains", "nodeType", "_document", "COMMENT_NODE", "some", "textContent", "trim", "throwToolbarMixedModesError", "_MatToolbar", "_MatToolbar_Factory", "ɵɵdefineComponent", "contentQueries", "_MatToolbar_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostVars", "hostBindings", "_MatToolbar_HostBindings", "ɵɵclassMap", "color", "ɵɵclassProp", "inputs", "ngContentSelectors", "_c1", "decls", "vars", "template", "_MatToolbar_Template", "ɵɵprojectionDef", "_c0", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "OnPush", "None", "descendants", "Error", "MatToolbarModule", "_MatToolbarModule", "_MatToolbarModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/toolbar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, inject, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, NgModule } from '@angular/core';\nimport { Platform } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\nclass MatToolbarRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatToolbarRow, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatToolbarRow, isStandalone: true, selector: \"mat-toolbar-row\", host: { classAttribute: \"mat-toolbar-row\" }, exportAs: [\"matToolbarRow\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatToolbarRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-toolbar-row',\n                    exportAs: 'matToolbarRow',\n                    host: { 'class': 'mat-toolbar-row' },\n                }]\n        }] });\nclass MatToolbar {\n    _elementRef = inject(ElementRef);\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT);\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /**\n     * Theme color of the toolbar. This API is supported in M2 themes only, it has\n     * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/toolbar/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Reference to all toolbar row elements that have been projected. */\n    _toolbarRows;\n    constructor() { }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            this._checkToolbarMixedModes();\n            this._toolbarRows.changes.subscribe(() => this._checkToolbarMixedModes());\n        }\n    }\n    /**\n     * Throws an exception when developers are attempting to combine the different toolbar row modes.\n     */\n    _checkToolbarMixedModes() {\n        if (this._toolbarRows.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            // Check if there are any other DOM nodes that can display content but aren't inside of\n            // a <mat-toolbar-row> element.\n            const isCombinedUsage = Array.from(this._elementRef.nativeElement.childNodes)\n                .filter(node => !(node.classList && node.classList.contains('mat-toolbar-row')))\n                .filter(node => node.nodeType !== (this._document ? this._document.COMMENT_NODE : 8))\n                .some(node => !!(node.textContent && node.textContent.trim()));\n            if (isCombinedUsage) {\n                throwToolbarMixedModesError();\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatToolbar, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatToolbar, isStandalone: true, selector: \"mat-toolbar\", inputs: { color: \"color\" }, host: { properties: { \"class\": \"color ? \\\"mat-\\\" + color : \\\"\\\"\", \"class.mat-toolbar-multiple-rows\": \"_toolbarRows.length > 0\", \"class.mat-toolbar-single-row\": \"_toolbarRows.length === 0\" }, classAttribute: \"mat-toolbar\" }, queries: [{ propertyName: \"_toolbarRows\", predicate: MatToolbarRow, descendants: true }], exportAs: [\"matToolbar\"], ngImport: i0, template: \"<ng-content></ng-content>\\n<ng-content select=\\\"mat-toolbar-row\\\"></ng-content>\\n\", styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mdc-text-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mdc-outlined-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatToolbar, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-toolbar', exportAs: 'matToolbar', host: {\n                        'class': 'mat-toolbar',\n                        '[class]': 'color ? \"mat-\" + color : \"\"',\n                        '[class.mat-toolbar-multiple-rows]': '_toolbarRows.length > 0',\n                        '[class.mat-toolbar-single-row]': '_toolbarRows.length === 0',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<ng-content></ng-content>\\n<ng-content select=\\\"mat-toolbar-row\\\"></ng-content>\\n\", styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mdc-text-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mdc-outlined-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input\n            }], _toolbarRows: [{\n                type: ContentChildren,\n                args: [MatToolbarRow, { descendants: true }]\n            }] } });\n/**\n * Throws an exception when attempting to combine the different toolbar row modes.\n * @docs-private\n */\nfunction throwToolbarMixedModesError() {\n    throw Error('MatToolbar: Attempting to combine different toolbar modes. ' +\n        'Either specify multiple `<mat-toolbar-row>` elements explicitly or just place content ' +\n        'inside of a `<mat-toolbar>` for a single row.');\n}\n\nclass MatToolbarModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatToolbarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatToolbarModule, imports: [MatCommonModule, MatToolbar, MatToolbarRow], exports: [MatToolbar, MatToolbarRow, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatToolbarModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatToolbarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatToolbar, MatToolbarRow],\n                    exports: [MatToolbar, MatToolbarRow, MatCommonModule],\n                }]\n        }] });\n\nexport { MatToolbar, MatToolbarModule, MatToolbarRow, throwToolbarMixedModesError };\n//# sourceMappingURL=toolbar.mjs.map\n"], "mappings": ";;;;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACtJ,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;AAE1B,MAAMC,aAAa,CAAC;AAGnBC,cAAA,GAHKD,aAAa;AAAAE,eAAA,CAAbF,aAAa,wBAAAG,uBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IACoFJ,cAAa;AAAA;AAAAE,eAAA,CAD9GF,aAAa,8BAI8Dd,EAAE,CAAAmB,iBAAA;EAAAC,IAAA,EAFQN,cAAa;EAAAO,SAAA;EAAAC,SAAA;EAAAC,QAAA;AAAA;AAExG;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFxB,EAAE,CAAAyB,iBAAA,CAAQX,aAAa,EAAc,CAAC;IAC3GM,IAAI,EAAEnB,SAAS;IACfyB,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BJ,QAAQ,EAAE,eAAe;MACzBK,IAAI,EAAE;QAAE,OAAO,EAAE;MAAkB;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMC,UAAU,CAAC;EAebC,WAAWA,CAAA,EAAG;IAAAd,eAAA,sBAdAd,MAAM,CAACC,UAAU,CAAC;IAAAa,eAAA,oBACpBd,MAAM,CAACQ,QAAQ,CAAC;IAAAM,eAAA,oBAChBd,MAAM,CAACS,QAAQ,CAAC;IAC5B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IANIK,eAAA;IAQA;IAAAA,eAAA;EAEgB;EAChBe,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,SAAS,CAACC,SAAS,EAAE;MAC1B,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAACC,YAAY,CAACC,OAAO,CAACC,SAAS,CAAC,MAAM,IAAI,CAACH,uBAAuB,CAAC,CAAC,CAAC;IAC7E;EACJ;EACA;AACJ;AACA;EACIA,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACC,YAAY,CAACG,MAAM,KAAK,OAAOd,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E;MACA;MACA,MAAMe,eAAe,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,UAAU,CAAC,CACxEC,MAAM,CAACC,IAAI,IAAI,EAAEA,IAAI,CAACC,SAAS,IAAID,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAC/EH,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACG,QAAQ,MAAM,IAAI,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACC,YAAY,GAAG,CAAC,CAAC,CAAC,CACpFC,IAAI,CAACN,IAAI,IAAI,CAAC,EAAEA,IAAI,CAACO,WAAW,IAAIP,IAAI,CAACO,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;MAClE,IAAIf,eAAe,EAAE;QACjBgB,2BAA2B,CAAC,CAAC;MACjC;IACJ;EACJ;AAGJ;AAACC,WAAA,GAxCK3B,UAAU;AAAAb,eAAA,CAAVa,UAAU,wBAAA4B,oBAAAvC,iBAAA;EAAA,YAAAA,iBAAA,IAsCuFW,WAAU;AAAA;AAAAb,eAAA,CAtC3Ga,UAAU,8BARiE7B,EAAE,CAAA0D,iBAAA;EAAAtC,IAAA,EA+CQS,WAAU;EAAAR,SAAA;EAAAsC,cAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;IAAA,IAAAF,EAAA;MA/CpB7D,EAAE,CAAAgE,cAAA,CAAAD,QAAA,EA+CkXjD,aAAa;IAAA;IAAA,IAAA+C,EAAA;MAAA,IAAAI,EAAA;MA/CjYjE,EAAE,CAAAkE,cAAA,CAAAD,EAAA,GAAFjE,EAAE,CAAAmE,WAAA,QAAAL,GAAA,CAAA3B,YAAA,GAAA8B,EAAA;IAAA;EAAA;EAAA3C,SAAA;EAAA8C,QAAA;EAAAC,YAAA,WAAAC,yBAAAT,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF7D,EAAE,CAAAuE,UAAA,CAAAT,GAAA,CAAAU,KAAA,GA+CgB,MAAM,GAAAV,GAAA,CAAAU,KAAA,GAAW,EAAhB,CAAC;MA/CpBxE,EAAE,CAAAyE,WAAA,8BAAAX,GAAA,CAAA3B,YAAA,CAAAG,MAAA,GA+C8B,CAAb,CAAC,2BAAAwB,GAAA,CAAA3B,YAAA,CAAAG,MAAA,KAAc,CAAf,CAAC;IAAA;EAAA;EAAAoC,MAAA;IAAAF,KAAA;EAAA;EAAAjD,QAAA;EAAAoD,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,qBAAAnB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA/CpB7D,EAAE,CAAAiF,eAAA,CAAAC,GAAA;MAAFlF,EAAE,CAAAmF,YAAA,EA+Cke,CAAC;MA/CrenF,EAAE,CAAAmF,YAAA,KA+CwhB,CAAC;IAAA;EAAA;EAAAC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAE5mB;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KAjDiFxB,EAAE,CAAAyB,iBAAA,CAiDQI,UAAU,EAAc,CAAC;IACxGT,IAAI,EAAEhB,SAAS;IACfsB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAa;MAAEJ,QAAQ,EAAE,YAAY;MAAEK,IAAI,EAAE;QACpD,OAAO,EAAE,aAAa;QACtB,SAAS,EAAE,6BAA6B;QACxC,mCAAmC,EAAE,yBAAyB;QAC9D,gCAAgC,EAAE;MACtC,CAAC;MAAE0D,eAAe,EAAEjF,uBAAuB,CAACkF,MAAM;MAAEF,aAAa,EAAE/E,iBAAiB,CAACkF,IAAI;MAAET,QAAQ,EAAE,mFAAmF;MAAEK,MAAM,EAAE,CAAC,ogEAAogE;IAAE,CAAC;EACttE,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEZ,KAAK,EAAE,CAAC;MAChDpD,IAAI,EAAEb;IACV,CAAC,CAAC;IAAE4B,YAAY,EAAE,CAAC;MACff,IAAI,EAAEZ,eAAe;MACrBkB,IAAI,EAAE,CAACZ,aAAa,EAAE;QAAE2E,WAAW,EAAE;MAAK,CAAC;IAC/C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,SAASlC,2BAA2BA,CAAA,EAAG;EACnC,MAAMmC,KAAK,CAAC,6DAA6D,GACrE,wFAAwF,GACxF,+CAA+C,CAAC;AACxD;AAEA,MAAMC,gBAAgB,CAAC;AAItBC,iBAAA,GAJKD,gBAAgB;AAAA3E,eAAA,CAAhB2E,gBAAgB,wBAAAE,0BAAA3E,iBAAA;EAAA,YAAAA,iBAAA,IACiFyE,iBAAgB;AAAA;AAAA3E,eAAA,CADjH2E,gBAAgB,8BAzE2D3F,EAAE,CAAA8F,gBAAA;EAAA1E,IAAA,EA2EqBuE,iBAAgB;EAAAI,OAAA,GAAYlF,eAAe,EAAEgB,UAAU,EAAEf,aAAa;EAAAkF,OAAA,GAAanE,UAAU,EAAEf,aAAa,EAAED,eAAe;AAAA;AAAAG,eAAA,CAF/N2E,gBAAgB,8BAzE2D3F,EAAE,CAAAiG,gBAAA;EAAAF,OAAA,GA4EiDlF,eAAe,EAAEA,eAAe;AAAA;AAEpK;EAAA,QAAAW,SAAA,oBAAAA,SAAA,KA9EiFxB,EAAE,CAAAyB,iBAAA,CA8EQkE,gBAAgB,EAAc,CAAC;IAC9GvE,IAAI,EAAEX,QAAQ;IACdiB,IAAI,EAAE,CAAC;MACCqE,OAAO,EAAE,CAAClF,eAAe,EAAEgB,UAAU,EAAEf,aAAa,CAAC;MACrDkF,OAAO,EAAE,CAACnE,UAAU,EAAEf,aAAa,EAAED,eAAe;IACxD,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASgB,UAAU,EAAE8D,gBAAgB,EAAE7E,aAAa,EAAEyC,2BAA2B;AACjF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}