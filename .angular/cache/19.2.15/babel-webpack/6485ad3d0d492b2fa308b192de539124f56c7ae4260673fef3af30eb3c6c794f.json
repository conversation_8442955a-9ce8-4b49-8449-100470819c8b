{"ast": null, "code": "import { Observable } from '../Observable';\nexport function fromSubscribable(subscribable) {\n  return new Observable(function (subscriber) {\n    return subscribable.subscribe(subscriber);\n  });\n}\n//# sourceMappingURL=fromSubscribable.js.map", "map": {"version": 3, "names": ["Observable", "fromSubscribable", "subscribable", "subscriber", "subscribe"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/fromSubscribable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport function fromSubscribable(subscribable) {\n    return new Observable(function (subscriber) { return subscribable.subscribe(subscriber); });\n}\n//# sourceMappingURL=fromSubscribable.js.map"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,gBAAgBA,CAACC,YAAY,EAAE;EAC3C,OAAO,IAAIF,UAAU,CAAC,UAAUG,UAAU,EAAE;IAAE,OAAOD,YAAY,CAACE,SAAS,CAACD,UAAU,CAAC;EAAE,CAAC,CAAC;AAC/F;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}