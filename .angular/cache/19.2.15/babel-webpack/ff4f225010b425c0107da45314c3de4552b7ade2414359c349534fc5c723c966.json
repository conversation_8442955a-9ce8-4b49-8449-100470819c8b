{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { SwuiGridRowActionsComponent } from './row-actions.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatButtonModule } from '@angular/material/button';\ndescribe('SwuiGridRowActionsComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [TranslateModule.forRoot(), MatIconModule, MatButtonModule, MatMenuModule, MatTooltipModule],\n      declarations: [SwuiGridRowActionsComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiGridRowActionsComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "SwuiGridRowActionsComponent", "TranslateModule", "MatMenuModule", "MatIconModule", "MatTooltipModule", "MatButtonModule", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "forRoot", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/row-actions/row-actions.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\n\nimport { SwuiGridRowActionsComponent } from './row-actions.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatButtonModule } from '@angular/material/button';\n\ndescribe('SwuiGridRowActionsComponent', () => {\n  let component: SwuiGridRowActionsComponent;\n  let fixture: ComponentFixture<SwuiGridRowActionsComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [\n        TranslateModule.forRoot(),\n        MatIconModule,\n        MatButtonModule,\n        MatMenuModule,\n        MatTooltipModule,\n      ],\n      declarations: [SwuiGridRowActionsComponent]\n    }).compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiGridRowActionsComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,2BAA2B,QAAQ,yBAAyB;AACrE,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAE1DC,QAAQ,CAAC,6BAA6B,EAAE,MAAK;EAC3C,IAAIC,SAAsC;EAC1C,IAAIC,OAAsD;EAE1DC,UAAU,CAACV,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACY,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPV,eAAe,CAACW,OAAO,EAAE,EACzBT,aAAa,EACbE,eAAe,EACfH,aAAa,EACbE,gBAAgB,CACjB;MACDS,YAAY,EAAE,CAACb,2BAA2B;KAC3C,CAAC,CAACc,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHL,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGV,OAAO,CAACiB,eAAe,CAACf,2BAA2B,CAAC;IAC9DO,SAAS,GAAGC,OAAO,CAACQ,iBAAiB;IACrCR,OAAO,CAACS,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACZ,SAAS,CAAC,CAACa,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}