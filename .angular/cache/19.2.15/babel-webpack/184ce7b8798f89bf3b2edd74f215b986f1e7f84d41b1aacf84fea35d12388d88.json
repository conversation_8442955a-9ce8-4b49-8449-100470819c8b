{"ast": null, "code": "export function getSymbolIterator() {\n  if (typeof Symbol !== 'function' || !Symbol.iterator) {\n    return '@@iterator';\n  }\n  return Symbol.iterator;\n}\nexport var iterator = getSymbolIterator();\n//# sourceMappingURL=iterator.js.map", "map": {"version": 3, "names": ["getSymbolIterator", "Symbol", "iterator"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/symbol/iterator.js"], "sourcesContent": ["export function getSymbolIterator() {\n    if (typeof Symbol !== 'function' || !Symbol.iterator) {\n        return '@@iterator';\n    }\n    return Symbol.iterator;\n}\nexport var iterator = getSymbolIterator();\n//# sourceMappingURL=iterator.js.map"], "mappings": "AAAA,OAAO,SAASA,iBAAiBA,CAAA,EAAG;EAChC,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,CAACA,MAAM,CAACC,QAAQ,EAAE;IAClD,OAAO,YAAY;EACvB;EACA,OAAOD,MAAM,CAACC,QAAQ;AAC1B;AACA,OAAO,IAAIA,QAAQ,GAAGF,iBAAiB,CAAC,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}