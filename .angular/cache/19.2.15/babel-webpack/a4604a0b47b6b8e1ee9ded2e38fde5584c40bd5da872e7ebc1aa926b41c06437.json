{"ast": null, "code": "// This file demonstrates global theme configuration\n// The CSS resources addon is deprecated in Storybook v9\n// Global styles should be configured in .storybook/preview.js instead\nconst meta = {\n  title: 'Styles/Themes',\n  parameters: {\n    layout: 'centered',\n    docs: {\n      description: {\n        component: 'Global theme configuration for the component library.'\n      }\n    }\n  }\n};\nexport default meta;\nexport const ThemeDemo = {\n  render: () => ({\n    template: `\n      <div style=\"padding: 20px;\">\n        <h2>Theme Configuration</h2>\n        <p>Global styles and themes are configured in .storybook/preview.js</p>\n        <p>This replaces the deprecated CSS resources addon.</p>\n      </div>\n    `\n  })\n};", "map": {"version": 3, "names": ["meta", "title", "parameters", "layout", "docs", "description", "component", "ThemeDemo", "render", "template"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/styles/themes/themes-global.stories.ts"], "sourcesContent": ["// This file demonstrates global theme configuration\n// The CSS resources addon is deprecated in Storybook v9\n// Global styles should be configured in .storybook/preview.js instead\nconst meta = {\n    title: 'Styles/Themes',\n    parameters: {\n        layout: 'centered',\n        docs: {\n            description: {\n                component: 'Global theme configuration for the component library.',\n            },\n        },\n    },\n};\nexport default meta;\nexport const ThemeDemo = {\n    render: () => ({\n        template: `\n      <div style=\"padding: 20px;\">\n        <h2>Theme Configuration</h2>\n        <p>Global styles and themes are configured in .storybook/preview.js</p>\n        <p>This replaces the deprecated CSS resources addon.</p>\n      </div>\n    `,\n    }),\n};\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,IAAI,GAAG;EACTC,KAAK,EAAE,eAAe;EACtBC,UAAU,EAAE;IACRC,MAAM,EAAE,UAAU;IAClBC,IAAI,EAAE;MACFC,WAAW,EAAE;QACTC,SAAS,EAAE;MACf;IACJ;EACJ;AACJ,CAAC;AACD,eAAeN,IAAI;AACnB,OAAO,MAAMO,SAAS,GAAG;EACrBC,MAAM,EAAEA,CAAA,MAAO;IACXC,QAAQ,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;EACI,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}