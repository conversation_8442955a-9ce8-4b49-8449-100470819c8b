{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatInputModule } from '@angular/material/input';\nimport { SwuiStartTimeComponent } from './swui-start-time.component';\nexport const START_TIME_MODULES = [ReactiveFormsModule, MatInputModule];\nlet SwuiStartTimeModule = class SwuiStartTimeModule {};\nSwuiStartTimeModule = __decorate([NgModule({\n  declarations: [SwuiStartTimeComponent],\n  exports: [SwuiStartTimeComponent],\n  imports: [CommonModule, ...START_TIME_MODULES]\n})], SwuiStartTimeModule);\nexport { SwuiStartTimeModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "ReactiveFormsModule", "MatInputModule", "SwuiStartTimeComponent", "START_TIME_MODULES", "SwuiStartTimeModule", "declarations", "exports", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-start-time/swui-start-time.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatInputModule } from '@angular/material/input';\nimport { SwuiStartTimeComponent } from './swui-start-time.component';\nexport const START_TIME_MODULES = [\n    ReactiveFormsModule,\n    MatInputModule,\n];\nlet SwuiStartTimeModule = class SwuiStartTimeModule {\n};\nSwuiStartTimeModule = __decorate([\n    NgModule({\n        declarations: [SwuiStartTimeComponent],\n        exports: [SwuiStartTimeComponent],\n        imports: [\n            CommonModule,\n            ...START_TIME_MODULES,\n        ]\n    })\n], SwuiStartTimeModule);\nexport { SwuiStartTimeModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,OAAO,MAAMC,kBAAkB,GAAG,CAC9BH,mBAAmB,EACnBC,cAAc,CACjB;AACD,IAAIG,mBAAmB,GAAG,MAAMA,mBAAmB,CAAC,EACnD;AACDA,mBAAmB,GAAGP,UAAU,CAAC,CAC7BC,QAAQ,CAAC;EACLO,YAAY,EAAE,CAACH,sBAAsB,CAAC;EACtCI,OAAO,EAAE,CAACJ,sBAAsB,CAAC;EACjCK,OAAO,EAAE,CACLR,YAAY,EACZ,GAAGI,kBAAkB;AAE7B,CAAC,CAAC,CACL,EAAEC,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}