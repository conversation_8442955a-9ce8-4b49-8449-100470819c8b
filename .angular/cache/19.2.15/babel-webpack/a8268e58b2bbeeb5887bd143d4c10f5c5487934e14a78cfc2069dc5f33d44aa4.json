{"ast": null, "code": "export const DEFAULT_PAGE_SIZE = 20;", "map": {"version": 3, "names": ["DEFAULT_PAGE_SIZE"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/settings/app-settings.ts"], "sourcesContent": ["export const DEFAULT_PAGE_SIZE = 20;\n"], "mappings": "AAAA,OAAO,MAAMA,iBAAiB,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}