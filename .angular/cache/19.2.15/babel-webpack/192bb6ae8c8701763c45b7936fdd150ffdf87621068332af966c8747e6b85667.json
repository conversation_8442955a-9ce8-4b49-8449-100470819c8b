{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _InputOutletComponent;\nimport { __decorate } from \"tslib\";\nimport { Component, Inject, Injector, Input, Optional } from '@angular/core';\nimport { SWUI_DYNAMIC_FORM_CONFIG, SWUI_DYNAMIC_FORM_WIDGET_CONFIG } from '../dynamic-form.model';\nlet InputOutletComponent = (_InputOutletComponent = class InputOutletComponent {\n  constructor(injector, config) {\n    this.injector = injector;\n    this.readonly = false;\n    this.submitted = false;\n    this.widgets = ((config === null || config === void 0 ? void 0 : config.widgets) || []).reduce((result, {\n      type,\n      component\n    }) => _objectSpread(_objectSpread({}, result), {}, {\n      [type]: component\n    }), {});\n  }\n  get component() {\n    return this.widgets[this.type || ''];\n  }\n  get componentInjector() {\n    const config = {\n      control: this.control,\n      id: this.id,\n      option: this.option,\n      readonly: this.readonly,\n      submitted: this.submitted\n    };\n    return this.option && Injector.create({\n      providers: [{\n        provide: SWUI_DYNAMIC_FORM_WIDGET_CONFIG,\n        useValue: config\n      }],\n      parent: this.injector\n    });\n  }\n}, _InputOutletComponent.ctorParameters = () => [{\n  type: Injector\n}, {\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_DYNAMIC_FORM_CONFIG]\n  }, {\n    type: Optional\n  }]\n}], _InputOutletComponent.propDecorators = {\n  type: [{\n    type: Input\n  }],\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  option: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }]\n}, _InputOutletComponent);\nInputOutletComponent = __decorate([Component({\n  selector: 'lib-input-outlet',\n  template: `\n    <ng-container *ngIf=\"componentInjector && component\">\n      <ng-container *ngComponentOutlet=\"component; injector: componentInjector\"></ng-container>\n    </ng-container>\n  `,\n  standalone: false\n})], InputOutletComponent);\nexport { InputOutletComponent };", "map": {"version": 3, "names": ["__decorate", "Component", "Inject", "Injector", "Input", "Optional", "SWUI_DYNAMIC_FORM_CONFIG", "SWUI_DYNAMIC_FORM_WIDGET_CONFIG", "InputOutletComponent", "_InputOutletComponent", "constructor", "injector", "config", "readonly", "submitted", "widgets", "reduce", "result", "type", "component", "_objectSpread", "componentInjector", "control", "id", "option", "create", "providers", "provide", "useValue", "parent", "ctorParameters", "undefined", "decorators", "args", "propDecorators", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-outlet/input-outlet.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Component, Inject, Injector, Input, Optional } from '@angular/core';\nimport { SWUI_DYNAMIC_FORM_CONFIG, SWUI_DYNAMIC_FORM_WIDGET_CONFIG } from '../dynamic-form.model';\nlet InputOutletComponent = class InputOutletComponent {\n    constructor(injector, config) {\n        this.injector = injector;\n        this.readonly = false;\n        this.submitted = false;\n        this.widgets = (config?.widgets || []).reduce((result, { type, component }) => ({\n            ...result,\n            [type]: component\n        }), {});\n    }\n    get component() {\n        return this.widgets[this.type || ''];\n    }\n    get componentInjector() {\n        const config = {\n            control: this.control,\n            id: this.id,\n            option: this.option,\n            readonly: this.readonly,\n            submitted: this.submitted\n        };\n        return this.option && Injector.create({\n            providers: [{\n                    provide: SWUI_DYNAMIC_FORM_WIDGET_CONFIG,\n                    useValue: config\n                }], parent: this.injector\n        });\n    }\n    static { this.ctorParameters = () => [\n        { type: Injector },\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_DYNAMIC_FORM_CONFIG,] }, { type: Optional }] }\n    ]; }\n    static { this.propDecorators = {\n        type: [{ type: Input }],\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        option: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }]\n    }; }\n};\nInputOutletComponent = __decorate([\n    Component({\n        selector: 'lib-input-outlet',\n        template: `\n    <ng-container *ngIf=\"componentInjector && component\">\n      <ng-container *ngComponentOutlet=\"component; injector: componentInjector\"></ng-container>\n    </ng-container>\n  `,\n        standalone: false\n    })\n], InputOutletComponent);\nexport { InputOutletComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC5E,SAASC,wBAAwB,EAAEC,+BAA+B,QAAQ,uBAAuB;AACjG,IAAIC,oBAAoB,IAAAC,qBAAA,GAAG,MAAMD,oBAAoB,CAAC;EAClDE,WAAWA,CAACC,QAAQ,EAAEC,MAAM,EAAE;IAC1B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAAH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,OAAO,KAAI,EAAE,EAAEC,MAAM,CAAC,CAACC,MAAM,EAAE;MAAEC,IAAI;MAAEC;IAAU,CAAC,KAAAC,aAAA,CAAAA,aAAA,KACnEH,MAAM;MACT,CAACC,IAAI,GAAGC;IAAS,EACnB,EAAE,CAAC,CAAC,CAAC;EACX;EACA,IAAIA,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACJ,OAAO,CAAC,IAAI,CAACG,IAAI,IAAI,EAAE,CAAC;EACxC;EACA,IAAIG,iBAAiBA,CAAA,EAAG;IACpB,MAAMT,MAAM,GAAG;MACXU,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,EAAE,EAAE,IAAI,CAACA,EAAE;MACXC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBX,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,SAAS,EAAE,IAAI,CAACA;IACpB,CAAC;IACD,OAAO,IAAI,CAACU,MAAM,IAAIrB,QAAQ,CAACsB,MAAM,CAAC;MAClCC,SAAS,EAAE,CAAC;QACJC,OAAO,EAAEpB,+BAA+B;QACxCqB,QAAQ,EAAEhB;MACd,CAAC,CAAC;MAAEiB,MAAM,EAAE,IAAI,CAAClB;IACzB,CAAC,CAAC;EACN;AAaJ,CAAC,EAZYF,qBAAA,CAAKqB,cAAc,GAAG,MAAM,CACjC;EAAEZ,IAAI,EAAEf;AAAS,CAAC,EAClB;EAAEe,IAAI,EAAEa,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEd,IAAI,EAAEhB,MAAM;IAAE+B,IAAI,EAAE,CAAC3B,wBAAwB;EAAG,CAAC,EAAE;IAAEY,IAAI,EAAEb;EAAS,CAAC;AAAE,CAAC,CAC7G,EACQI,qBAAA,CAAKyB,cAAc,GAAG;EAC3BhB,IAAI,EAAE,CAAC;IAAEA,IAAI,EAAEd;EAAM,CAAC,CAAC;EACvBkB,OAAO,EAAE,CAAC;IAAEJ,IAAI,EAAEd;EAAM,CAAC,CAAC;EAC1BmB,EAAE,EAAE,CAAC;IAAEL,IAAI,EAAEd;EAAM,CAAC,CAAC;EACrBoB,MAAM,EAAE,CAAC;IAAEN,IAAI,EAAEd;EAAM,CAAC,CAAC;EACzBS,QAAQ,EAAE,CAAC;IAAEK,IAAI,EAAEd;EAAM,CAAC,CAAC;EAC3BU,SAAS,EAAE,CAAC;IAAEI,IAAI,EAAEd;EAAM,CAAC;AAC/B,CAAC,EAAAK,qBAAA,CACJ;AACDD,oBAAoB,GAAGR,UAAU,CAAC,CAC9BC,SAAS,CAAC;EACNkC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE;AAClB;AACA;AACA;AACA,GAAG;EACKC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAE7B,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}