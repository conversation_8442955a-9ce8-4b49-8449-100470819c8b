{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _InputColorComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-color.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-color.component.scss?ngResource\";\nimport { Component, Input, ViewChild } from '@angular/core';\nimport { ColorPickerDirective } from 'ngx-color-picker';\nlet InputColorComponent = (_InputColorComponent = class InputColorComponent {\n  constructor() {\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n    this.isActive = false;\n    this.isIdentical = false;\n  }\n  set componentOptions(value) {\n    var _value$validation;\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.defaultValue = value === null || value === void 0 ? void 0 : value.defaultValue;\n    this.required = value === null || value === void 0 ? void 0 : value.required;\n    this.errorMessages = _objectSpread({\n      invalidColorHexFormat: 'VALIDATION.invalidColorHexFormat'\n    }, (value === null || value === void 0 || (_value$validation = value.validation) === null || _value$validation === void 0 ? void 0 : _value$validation.messages) || {});\n  }\n  ngOnInit() {\n    if (this.control) {\n      this.isIdentical = this.control.value === this.defaultValue;\n    }\n  }\n  onToggleDialog(isOpen) {\n    this.isActive = isOpen;\n  }\n  onResetClick(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    if (this.colorPicker) {\n      this.colorPicker.closeDialog();\n    }\n    if (this.isIdentical) {\n      return;\n    }\n    if (this.control) {\n      this.control.setValue(this.defaultValue);\n    }\n    this.isIdentical = true;\n  }\n  handleColorChange(color) {\n    if (this.control) {\n      this.control.setValue(color);\n    }\n    this.isIdentical = color === this.defaultValue;\n  }\n}, _InputColorComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }],\n  colorPicker: [{\n    type: ViewChild,\n    args: [ColorPickerDirective, {\n      static: true\n    }]\n  }]\n}, _InputColorComponent);\nInputColorComponent = __decorate([Component({\n  selector: 'lib-input-color',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], InputColorComponent);\nexport { InputColorComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "ViewChild", "ColorPickerDirective", "InputColorComponent", "_InputColorComponent", "constructor", "id", "readonly", "submitted", "isActive", "isIdentical", "componentOptions", "value", "_value$validation", "title", "defaultValue", "required", "errorMessages", "_objectSpread", "invalidColorHexFormat", "validation", "messages", "ngOnInit", "control", "onToggleDialog", "isOpen", "onResetClick", "event", "preventDefault", "stopPropagation", "colorPicker", "closeDialog", "setValue", "handleColorChange", "color", "propDecorators", "type", "args", "static", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-color/input-color.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-color.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-color.component.scss?ngResource\";\nimport { Component, Input, ViewChild } from '@angular/core';\nimport { ColorPickerDirective } from 'ngx-color-picker';\nlet InputColorComponent = class InputColorComponent {\n    constructor() {\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n        this.isActive = false;\n        this.isIdentical = false;\n    }\n    set componentOptions(value) {\n        this.title = value?.title;\n        this.defaultValue = value?.defaultValue;\n        this.required = value?.required;\n        this.errorMessages = {\n            invalidColorHexFormat: 'VALIDATION.invalidColorHexFormat',\n            ...(value?.validation?.messages || {})\n        };\n    }\n    ngOnInit() {\n        if (this.control) {\n            this.isIdentical = this.control.value === this.defaultValue;\n        }\n    }\n    onToggleDialog(isOpen) {\n        this.isActive = isOpen;\n    }\n    onResetClick(event) {\n        event.preventDefault();\n        event.stopPropagation();\n        if (this.colorPicker) {\n            this.colorPicker.closeDialog();\n        }\n        if (this.isIdentical) {\n            return;\n        }\n        if (this.control) {\n            this.control.setValue(this.defaultValue);\n        }\n        this.isIdentical = true;\n    }\n    handleColorChange(color) {\n        if (this.control) {\n            this.control.setValue(color);\n        }\n        this.isIdentical = color === this.defaultValue;\n    }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }],\n        colorPicker: [{ type: ViewChild, args: [ColorPickerDirective, { static: true },] }]\n    }; }\n};\nInputColorComponent = __decorate([\n    Component({\n        selector: 'lib-input-color',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], InputColorComponent);\nexport { InputColorComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,SAASC,SAAS,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AAC3D,SAASC,oBAAoB,QAAQ,kBAAkB;AACvD,IAAIC,mBAAmB,IAAAC,oBAAA,GAAG,MAAMD,mBAAmB,CAAC;EAChDE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,WAAW,GAAG,KAAK;EAC5B;EACA,IAAIC,gBAAgBA,CAACC,KAAK,EAAE;IAAA,IAAAC,iBAAA;IACxB,IAAI,CAACC,KAAK,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,KAAK;IACzB,IAAI,CAACC,YAAY,GAAGH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,YAAY;IACvC,IAAI,CAACC,QAAQ,GAAGJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,QAAQ;IAC/B,IAAI,CAACC,aAAa,GAAAC,aAAA;MACdC,qBAAqB,EAAE;IAAkC,GACrD,CAAAP,KAAK,aAALA,KAAK,gBAAAC,iBAAA,GAALD,KAAK,CAAEQ,UAAU,cAAAP,iBAAA,uBAAjBA,iBAAA,CAAmBQ,QAAQ,KAAI,CAAC,CAAC,CACxC;EACL;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACb,WAAW,GAAG,IAAI,CAACa,OAAO,CAACX,KAAK,KAAK,IAAI,CAACG,YAAY;IAC/D;EACJ;EACAS,cAAcA,CAACC,MAAM,EAAE;IACnB,IAAI,CAAChB,QAAQ,GAAGgB,MAAM;EAC1B;EACAC,YAAYA,CAACC,KAAK,EAAE;IAChBA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACC,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACC,WAAW,CAAC,CAAC;IAClC;IACA,IAAI,IAAI,CAACrB,WAAW,EAAE;MAClB;IACJ;IACA,IAAI,IAAI,CAACa,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACS,QAAQ,CAAC,IAAI,CAACjB,YAAY,CAAC;IAC5C;IACA,IAAI,CAACL,WAAW,GAAG,IAAI;EAC3B;EACAuB,iBAAiBA,CAACC,KAAK,EAAE;IACrB,IAAI,IAAI,CAACX,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACS,QAAQ,CAACE,KAAK,CAAC;IAChC;IACA,IAAI,CAACxB,WAAW,GAAGwB,KAAK,KAAK,IAAI,CAACnB,YAAY;EAClD;AASJ,CAAC,EARYX,oBAAA,CAAK+B,cAAc,GAAG;EAC3BZ,OAAO,EAAE,CAAC;IAAEa,IAAI,EAAEpC;EAAM,CAAC,CAAC;EAC1BM,EAAE,EAAE,CAAC;IAAE8B,IAAI,EAAEpC;EAAM,CAAC,CAAC;EACrBO,QAAQ,EAAE,CAAC;IAAE6B,IAAI,EAAEpC;EAAM,CAAC,CAAC;EAC3BQ,SAAS,EAAE,CAAC;IAAE4B,IAAI,EAAEpC;EAAM,CAAC,CAAC;EAC5BW,gBAAgB,EAAE,CAAC;IAAEyB,IAAI,EAAEpC;EAAM,CAAC,CAAC;EACnC8B,WAAW,EAAE,CAAC;IAAEM,IAAI,EAAEnC,SAAS;IAAEoC,IAAI,EAAE,CAACnC,oBAAoB,EAAE;MAAEoC,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC;AACtF,CAAC,EAAAlC,oBAAA,CACJ;AACDD,mBAAmB,GAAGP,UAAU,CAAC,CAC7BG,SAAS,CAAC;EACNwC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE3C,oBAAoB;EAC9B4C,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC5C,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEK,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}