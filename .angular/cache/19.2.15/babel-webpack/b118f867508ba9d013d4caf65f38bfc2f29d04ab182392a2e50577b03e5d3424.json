{"ast": null, "code": "var _ButtonActionComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./button-action.component.html?ngResource\";\nimport { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';\nlet ButtonActionComponent = (_ButtonActionComponent = class ButtonActionComponent {\n  constructor() {\n    this.action = new EventEmitter();\n  }\n  set value(action) {\n    this.data = typeof action === 'string' ? {\n      label: action\n    } : action;\n  }\n  onClick(event) {\n    event.preventDefault();\n    this.action.emit();\n  }\n}, _ButtonActionComponent.propDecorators = {\n  value: [{\n    type: Input\n  }],\n  action: [{\n    type: Output\n  }]\n}, _ButtonActionComponent);\nButtonActionComponent = __decorate([Component({\n  selector: 'lib-button-action',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false\n})], ButtonActionComponent);\nexport { ButtonActionComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "ChangeDetectionStrategy", "Component", "EventEmitter", "Input", "Output", "ButtonActionComponent", "_ButtonActionComponent", "constructor", "action", "value", "data", "label", "onClick", "event", "preventDefault", "emit", "propDecorators", "type", "selector", "template", "changeDetection", "OnPush", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/button-action/button-action.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./button-action.component.html?ngResource\";\nimport { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';\nlet ButtonActionComponent = class ButtonActionComponent {\n    constructor() {\n        this.action = new EventEmitter();\n    }\n    set value(action) {\n        this.data = typeof action === 'string' ? { label: action } : action;\n    }\n    onClick(event) {\n        event.preventDefault();\n        this.action.emit();\n    }\n    static { this.propDecorators = {\n        value: [{ type: Input }],\n        action: [{ type: Output }]\n    }; }\n};\nButtonActionComponent = __decorate([\n    Component({\n        selector: 'lib-button-action',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false\n    })\n], ButtonActionComponent);\nexport { ButtonActionComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,SAASC,uBAAuB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAC/F,IAAIC,qBAAqB,IAAAC,sBAAA,GAAG,MAAMD,qBAAqB,CAAC;EACpDE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,IAAIN,YAAY,CAAC,CAAC;EACpC;EACA,IAAIO,KAAKA,CAACD,MAAM,EAAE;IACd,IAAI,CAACE,IAAI,GAAG,OAAOF,MAAM,KAAK,QAAQ,GAAG;MAAEG,KAAK,EAAEH;IAAO,CAAC,GAAGA,MAAM;EACvE;EACAI,OAAOA,CAACC,KAAK,EAAE;IACXA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACN,MAAM,CAACO,IAAI,CAAC,CAAC;EACtB;AAKJ,CAAC,EAJYT,sBAAA,CAAKU,cAAc,GAAG;EAC3BP,KAAK,EAAE,CAAC;IAAEQ,IAAI,EAAEd;EAAM,CAAC,CAAC;EACxBK,MAAM,EAAE,CAAC;IAAES,IAAI,EAAEb;EAAO,CAAC;AAC7B,CAAC,EAAAE,sBAAA,CACJ;AACDD,qBAAqB,GAAGP,UAAU,CAAC,CAC/BG,SAAS,CAAC;EACNiB,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAEpB,oBAAoB;EAC9BqB,eAAe,EAAEpB,uBAAuB,CAACqB,MAAM;EAC/CC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEjB,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}