{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _EventManager, _SharedStylesHost, _DomRendererFactory;\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { isPlatformServer, DOCUMENT, ɵgetDOM as _getDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ɵRuntimeError as _RuntimeError, Inject, Injectable, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, ɵTracingService as _TracingService, RendererStyleFlags2 } from '@angular/core';\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    _defineProperty(this, \"_zone\", void 0);\n    _defineProperty(this, \"_plugins\", void 0);\n    _defineProperty(this, \"_eventNameToPlugin\", new Map());\n    this._zone = _zone;\n    plugins.forEach(plugin => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @param options Options that configure how the event listener is bound.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(element, eventName, handler, options) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler, options);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n  _findPluginFor(eventName) {\n    let plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n    const plugins = this._plugins;\n    plugin = plugins.find(plugin => plugin.supports(eventName));\n    if (!plugin) {\n      throw new _RuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) && `No event manager plugin found for event ${eventName}`);\n    }\n    this._eventNameToPlugin.set(eventName, plugin);\n    return plugin;\n  }\n}\n_EventManager = EventManager;\n_defineProperty(EventManager, \"\\u0275fac\", function _EventManager_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n});\n_defineProperty(EventManager, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _EventManager,\n  factory: _EventManager.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [EVENT_MANAGER_PLUGINS]\n    }]\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n  // TODO: remove (has some usage in G3)\n  constructor(_doc) {\n    _defineProperty(this, \"_doc\", void 0);\n    // Using non-null assertion because it's set by EventManager's constructor\n    _defineProperty(this, \"manager\", void 0);\n    this._doc = _doc;\n  }\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n/**\n * Removes all provided elements from the document.\n * @param elements An array of HTML Elements.\n */\nfunction removeElements(elements) {\n  for (const element of elements) {\n    element.remove();\n  }\n}\n/**\n * Creates a `style` element with the provided inline style content.\n * @param style A string of the inline style content.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLStyleElement instance.\n */\nfunction createStyleElement(style, doc) {\n  const styleElement = doc.createElement('style');\n  styleElement.textContent = style;\n  return styleElement;\n}\n/**\n * Searches a DOM document's head element for style elements with a matching application\n * identifier attribute (`ng-app-id`) to the provide identifier and adds usage records for each.\n * @param doc An HTML DOM document instance.\n * @param appId A string containing an Angular application identifer.\n * @param inline A Map object for tracking inline (defined via `styles` in component decorator) style usage.\n * @param external A Map object for tracking external (defined via `styleUrls` in component decorator) style usage.\n */\nfunction addServerStyles(doc, appId, inline, external) {\n  var _doc$head;\n  const elements = (_doc$head = doc.head) === null || _doc$head === void 0 ? void 0 : _doc$head.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"],link[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"]`);\n  if (elements) {\n    for (const styleElement of elements) {\n      styleElement.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n      if (styleElement instanceof HTMLLinkElement) {\n        // Only use filename from href\n        // The href is build time generated with a unique value to prevent duplicates.\n        external.set(styleElement.href.slice(styleElement.href.lastIndexOf('/') + 1), {\n          usage: 0,\n          elements: [styleElement]\n        });\n      } else if (styleElement.textContent) {\n        inline.set(styleElement.textContent, {\n          usage: 0,\n          elements: [styleElement]\n        });\n      }\n    }\n  }\n}\n/**\n * Creates a `link` element for the provided external style URL.\n * @param url A string of the URL for the stylesheet.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLLinkElement instance.\n */\nfunction createLinkElement(url, doc) {\n  const linkElement = doc.createElement('link');\n  linkElement.setAttribute('rel', 'stylesheet');\n  linkElement.setAttribute('href', url);\n  return linkElement;\n}\nclass SharedStylesHost {\n  constructor(doc, appId, nonce, platformId = {}) {\n    _defineProperty(this, \"doc\", void 0);\n    _defineProperty(this, \"appId\", void 0);\n    _defineProperty(this, \"nonce\", void 0);\n    /**\n     * Provides usage information for active inline style content and associated HTML <style> elements.\n     * Embedded styles typically originate from the `styles` metadata of a rendered component.\n     */\n    _defineProperty(this, \"inline\", new Map());\n    /**\n     * Provides usage information for active external style URLs and the associated HTML <link> elements.\n     * External styles typically originate from the `ɵɵExternalStylesFeature` of a rendered component.\n     */\n    _defineProperty(this, \"external\", new Map());\n    /**\n     * Set of host DOM nodes that will have styles attached.\n     */\n    _defineProperty(this, \"hosts\", new Set());\n    /**\n     * Whether the application code is currently executing on a server.\n     */\n    _defineProperty(this, \"isServer\", void 0);\n    this.doc = doc;\n    this.appId = appId;\n    this.nonce = nonce;\n    this.isServer = isPlatformServer(platformId);\n    addServerStyles(doc, appId, this.inline, this.external);\n    this.hosts.add(doc.head);\n  }\n  /**\n   * Adds embedded styles to the DOM via HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  addStyles(styles, urls) {\n    for (const value of styles) {\n      this.addUsage(value, this.inline, createStyleElement);\n    }\n    urls === null || urls === void 0 || urls.forEach(value => this.addUsage(value, this.external, createLinkElement));\n  }\n  /**\n   * Removes embedded styles from the DOM that were added as HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  removeStyles(styles, urls) {\n    for (const value of styles) {\n      this.removeUsage(value, this.inline);\n    }\n    urls === null || urls === void 0 || urls.forEach(value => this.removeUsage(value, this.external));\n  }\n  addUsage(value, usages, creator) {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n    // If existing, just increment the usage count\n    if (record) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && record.usage === 0) {\n        // A usage count of zero indicates a preexisting server generated style.\n        // This attribute is solely used for debugging purposes of SSR style reuse.\n        record.elements.forEach(element => element.setAttribute('ng-style-reused', ''));\n      }\n      record.usage++;\n    } else {\n      // Otherwise, create an entry to track the elements and add element for each host\n      usages.set(value, {\n        usage: 1,\n        elements: [...this.hosts].map(host => this.addElement(host, creator(value, this.doc)))\n      });\n    }\n  }\n  removeUsage(value, usages) {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n    // If there is a record, reduce the usage count and if no longer used,\n    // remove from DOM and delete usage record.\n    if (record) {\n      record.usage--;\n      if (record.usage <= 0) {\n        removeElements(record.elements);\n        usages.delete(value);\n      }\n    }\n  }\n  ngOnDestroy() {\n    for (const [, {\n      elements\n    }] of [...this.inline, ...this.external]) {\n      removeElements(elements);\n    }\n    this.hosts.clear();\n  }\n  /**\n   * Adds a host node to the set of style hosts and adds all existing style usage to\n   * the newly added host node.\n   *\n   * This is currently only used for Shadow DOM encapsulation mode.\n   */\n  addHost(hostNode) {\n    this.hosts.add(hostNode);\n    // Add existing styles to new host\n    for (const [style, {\n      elements\n    }] of this.inline) {\n      elements.push(this.addElement(hostNode, createStyleElement(style, this.doc)));\n    }\n    for (const [url, {\n      elements\n    }] of this.external) {\n      elements.push(this.addElement(hostNode, createLinkElement(url, this.doc)));\n    }\n  }\n  removeHost(hostNode) {\n    this.hosts.delete(hostNode);\n  }\n  addElement(host, element) {\n    // Add a nonce if present\n    if (this.nonce) {\n      element.setAttribute('nonce', this.nonce);\n    }\n    // Add application identifier when on the server to support client-side reuse\n    if (this.isServer) {\n      element.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n    }\n    // Insert the element into the DOM with the host node as parent\n    return host.appendChild(element);\n  }\n}\n_SharedStylesHost = SharedStylesHost;\n_defineProperty(SharedStylesHost, \"\\u0275fac\", function _SharedStylesHost_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SharedStylesHost)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(APP_ID), i0.ɵɵinject(CSP_NONCE, 8), i0.ɵɵinject(PLATFORM_ID));\n});\n_defineProperty(SharedStylesHost, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _SharedStylesHost,\n  factory: _SharedStylesHost.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], null);\n})();\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/Math/MathML'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst SOURCEMAP_URL_REGEXP = /\\/\\*#\\s*sourceMappingURL=(.+?)\\s*\\*\\//;\nconst PROTOCOL_REGEXP = /^https?:/;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n  providedIn: 'root',\n  factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT\n});\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n  return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\n/**\n * Prepends a baseHref to the `sourceMappingURL` within the provided CSS content.\n * If the `sourceMappingURL` contains an inline (encoded) map, the function skips processing.\n *\n * @note For inline stylesheets, the `sourceMappingURL` is relative to the page's origin\n * and not the provided baseHref. This function is needed as when accessing the page with a URL\n * containing two or more segments.\n * For example, if the baseHref is set to `/`, and you visit a URL like `http://localhost/foo/bar`,\n * the map would be requested from `http://localhost/foo/bar/comp.css.map` instead of what you'd expect,\n * which is `http://localhost/comp.css.map`. This behavior is corrected by modifying the `sourceMappingURL`\n * to ensure external source maps are loaded relative to the baseHref.\n *\n\n * @param baseHref - The base URL to prepend to the `sourceMappingURL`.\n * @param styles - An array of CSS content strings, each potentially containing a `sourceMappingURL`.\n * @returns The updated array of CSS content strings with modified `sourceMappingURL` values,\n * or the original content if no modification is needed.\n */\nfunction addBaseHrefToCssSourceMap(baseHref, styles) {\n  if (!baseHref) {\n    return styles;\n  }\n  const absoluteBaseHrefUrl = new URL(baseHref, 'http://localhost');\n  return styles.map(cssContent => {\n    if (!cssContent.includes('sourceMappingURL=')) {\n      return cssContent;\n    }\n    return cssContent.replace(SOURCEMAP_URL_REGEXP, (_, sourceMapUrl) => {\n      if (sourceMapUrl[0] === '/' || sourceMapUrl.startsWith('data:') || PROTOCOL_REGEXP.test(sourceMapUrl)) {\n        return `/*# sourceMappingURL=${sourceMapUrl} */`;\n      }\n      const {\n        pathname: resolvedSourceMapUrl\n      } = new URL(sourceMapUrl, absoluteBaseHrefUrl);\n      return `/*# sourceMappingURL=${resolvedSourceMapUrl} */`;\n    });\n  });\n}\nclass DomRendererFactory2 {\n  constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null, tracingService = null) {\n    _defineProperty(this, \"eventManager\", void 0);\n    _defineProperty(this, \"sharedStylesHost\", void 0);\n    _defineProperty(this, \"appId\", void 0);\n    _defineProperty(this, \"removeStylesOnCompDestroy\", void 0);\n    _defineProperty(this, \"doc\", void 0);\n    _defineProperty(this, \"platformId\", void 0);\n    _defineProperty(this, \"ngZone\", void 0);\n    _defineProperty(this, \"nonce\", void 0);\n    _defineProperty(this, \"tracingService\", void 0);\n    _defineProperty(this, \"rendererByCompId\", new Map());\n    _defineProperty(this, \"defaultRenderer\", void 0);\n    _defineProperty(this, \"platformIsServer\", void 0);\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    this.doc = doc;\n    this.platformId = platformId;\n    this.ngZone = ngZone;\n    this.nonce = nonce;\n    this.tracingService = tracingService;\n    this.platformIsServer = isPlatformServer(platformId);\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer, this.tracingService);\n  }\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n      // Domino does not support shadow DOM.\n      type = _objectSpread(_objectSpread({}, type), {}, {\n        encapsulation: ViewEncapsulation.Emulated\n      });\n    }\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n    return renderer;\n  }\n  getOrCreateRenderer(element, type) {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n    if (!renderer) {\n      const doc = this.doc;\n      const ngZone = this.ngZone;\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n      const platformIsServer = this.platformIsServer;\n      const tracingService = this.tracingService;\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer, tracingService);\n        default:\n          renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n          break;\n      }\n      rendererByCompId.set(type.id, renderer);\n    }\n    return renderer;\n  }\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  componentReplaced(componentId) {\n    this.rendererByCompId.delete(componentId);\n  }\n}\n_DomRendererFactory = DomRendererFactory2;\n_defineProperty(DomRendererFactory2, \"\\u0275fac\", function _DomRendererFactory_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _DomRendererFactory)(i0.ɵɵinject(EventManager), i0.ɵɵinject(SharedStylesHost), i0.ɵɵinject(APP_ID), i0.ɵɵinject(REMOVE_STYLES_ON_COMPONENT_DESTROY), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(CSP_NONCE), i0.ɵɵinject(_TracingService, 8));\n});\n_defineProperty(DomRendererFactory2, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _DomRendererFactory,\n  factory: _DomRendererFactory.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], () => [{\n    type: EventManager\n  }, {\n    type: SharedStylesHost\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n    }]\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }]\n  }, {\n    type: i0.ɵTracingService,\n    decorators: [{\n      type: Inject,\n      args: [_TracingService]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\nclass DefaultDomRenderer2 {\n  constructor(eventManager, doc, ngZone, platformIsServer, tracingService) {\n    _defineProperty(this, \"eventManager\", void 0);\n    _defineProperty(this, \"doc\", void 0);\n    _defineProperty(this, \"ngZone\", void 0);\n    _defineProperty(this, \"platformIsServer\", void 0);\n    _defineProperty(this, \"tracingService\", void 0);\n    _defineProperty(this, \"data\", Object.create(null));\n    /**\n     * By default this renderer throws when encountering synthetic properties\n     * This can be disabled for example by the AsyncAnimationRendererFactory\n     */\n    _defineProperty(this, \"throwOnSyntheticProps\", true);\n    _defineProperty(this, \"destroyNode\", null);\n    this.eventManager = eventManager;\n    this.doc = doc;\n    this.ngZone = ngZone;\n    this.platformIsServer = platformIsServer;\n    this.tracingService = tracingService;\n  }\n  destroy() {}\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n    return this.doc.createElement(name);\n  }\n  createComment(value) {\n    return this.doc.createComment(value);\n  }\n  createText(value) {\n    return this.doc.createTextNode(value);\n  }\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n  removeChild(_parent, oldChild) {\n    oldChild.remove();\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new _RuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && `The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n  parentNode(node) {\n    return node.parentNode;\n  }\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n  setProperty(el, name, value) {\n    if (el == null) {\n      return;\n    }\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n  listen(target, event, callback, options) {\n    var _this$tracingService;\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      target = _getDOM().getGlobalEventTarget(this.doc, target);\n      if (!target) {\n        throw new _RuntimeError(5102 /* RuntimeErrorCode.UNSUPPORTED_EVENT_TARGET */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unsupported event target ${target} for event ${event}`);\n      }\n    }\n    let wrappedCallback = this.decoratePreventDefault(callback);\n    if ((_this$tracingService = this.tracingService) !== null && _this$tracingService !== void 0 && _this$tracingService.wrapEventListener) {\n      wrappedCallback = this.tracingService.wrapEventListener(target, event, wrappedCallback);\n    }\n    return this.eventManager.addEventListener(target, event, wrappedCallback, options);\n  }\n  decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n    // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n    // unwrap the listener (see below).\n    return event => {\n      // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n      // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n      // debug_node can inspect the listener toString contents for the existence of this special\n      // token. Because the token is a string literal, it is ensured to not be modified by compiled\n      // code.\n      if (event === '__ngUnwrap__') {\n        return eventHandler;\n      }\n      // Run the event handler inside the ngZone because event handlers are not patched\n      // by Zone on the server. This is required only for tests.\n      const allowDefaultBehavior = this.platformIsServer ? this.ngZone.runGuarded(() => eventHandler(event)) : eventHandler(event);\n      if (allowDefaultBehavior === false) {\n        event.preventDefault();\n      }\n      return undefined;\n    };\n  }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new _RuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Make sure \\`provideAnimationsAsync()\\`, \\`provideAnimations()\\` or \\`provideNoopAnimations()\\` call was added to a list of providers used to bootstrap an application.\n  - There is a corresponding animation configuration named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.dev/api/core/Component#animations).`);\n  }\n}\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer, tracingService) {\n    var _component$getExterna;\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    _defineProperty(this, \"sharedStylesHost\", void 0);\n    _defineProperty(this, \"hostEl\", void 0);\n    _defineProperty(this, \"shadowRoot\", void 0);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    let styles = component.styles;\n    if (ngDevMode) {\n      var _getDOM$getBaseHref;\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = (_getDOM$getBaseHref = _getDOM().getBaseHref(doc)) !== null && _getDOM$getBaseHref !== void 0 ? _getDOM$getBaseHref : '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    styles = shimStylesContent(component.id, styles);\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n      if (nonce) {\n        styleEl.setAttribute('nonce', nonce);\n      }\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n    // Apply any external component styles to the shadow root for the component's element.\n    // The ShadowDOM renderer uses an alternative execution path for component styles that\n    // does not use the SharedStylesHost that other encapsulation modes leverage. Much like\n    // the manual addition of embedded styles directly above, any external stylesheets\n    // must be manually added here to ensure ShadowDOM components are correctly styled.\n    // TODO: Consider reworking the DOM Renderers to consolidate style handling.\n    const styleUrls = (_component$getExterna = component.getExternalStyles) === null || _component$getExterna === void 0 ? void 0 : _component$getExterna.call(component);\n    if (styleUrls) {\n      for (const styleUrl of styleUrls) {\n        const linkEl = createLinkElement(styleUrl, doc);\n        if (nonce) {\n          linkEl.setAttribute('nonce', nonce);\n        }\n        this.shadowRoot.appendChild(linkEl);\n      }\n    }\n  }\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  removeChild(_parent, oldChild) {\n    return super.removeChild(null, oldChild);\n  }\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId) {\n    var _component$getExterna2;\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    _defineProperty(this, \"sharedStylesHost\", void 0);\n    _defineProperty(this, \"removeStylesOnCompDestroy\", void 0);\n    _defineProperty(this, \"styles\", void 0);\n    _defineProperty(this, \"styleUrls\", void 0);\n    this.sharedStylesHost = sharedStylesHost;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    let styles = component.styles;\n    if (ngDevMode) {\n      var _getDOM$getBaseHref2;\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = (_getDOM$getBaseHref2 = _getDOM().getBaseHref(doc)) !== null && _getDOM$getBaseHref2 !== void 0 ? _getDOM$getBaseHref2 : '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    this.styles = compId ? shimStylesContent(compId, styles) : styles;\n    this.styleUrls = (_component$getExterna2 = component.getExternalStyles) === null || _component$getExterna2 === void 0 ? void 0 : _component$getExterna2.call(component, compId);\n  }\n  applyStyles() {\n    this.sharedStylesHost.addStyles(this.styles, this.styleUrls);\n  }\n  destroy() {\n    if (!this.removeStylesOnCompDestroy) {\n      return;\n    }\n    this.sharedStylesHost.removeStyles(this.styles, this.styleUrls);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService) {\n    const compId = appId + '-' + component.id;\n    super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId);\n    _defineProperty(this, \"contentAttr\", void 0);\n    _defineProperty(this, \"hostAttr\", void 0);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n  applyToHost(element) {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\nexport { DomRendererFactory2, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, REMOVE_STYLES_ON_COMPONENT_DESTROY, SharedStylesHost };", "map": {"version": 3, "names": ["isPlatformServer", "DOCUMENT", "ɵgetDOM", "_getDOM", "i0", "InjectionToken", "ɵRuntimeError", "_RuntimeError", "Inject", "Injectable", "APP_ID", "CSP_NONCE", "PLATFORM_ID", "Optional", "ViewEncapsulation", "ɵTracingService", "_TracingService", "RendererStyleFlags2", "EVENT_MANAGER_PLUGINS", "ngDevMode", "EventManager", "constructor", "plugins", "_zone", "_defineProperty", "Map", "for<PERSON>ach", "plugin", "manager", "_plugins", "slice", "reverse", "addEventListener", "element", "eventName", "handler", "options", "_findPluginFor", "getZone", "_eventNameToPlugin", "get", "find", "supports", "set", "_EventManager", "_EventManager_Factory", "__ngFactoryType__", "ɵɵinject", "NgZone", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "ɵsetClassMetadata", "type", "undefined", "decorators", "args", "EventManagerPlugin", "_doc", "APP_ID_ATTRIBUTE_NAME", "removeElements", "elements", "remove", "createStyleElement", "style", "doc", "styleElement", "createElement", "textContent", "addServerStyles", "appId", "inline", "external", "_doc$head", "head", "querySelectorAll", "removeAttribute", "HTMLLinkElement", "href", "lastIndexOf", "usage", "createLinkElement", "url", "linkElement", "setAttribute", "SharedStylesHost", "nonce", "platformId", "Set", "isServer", "hosts", "add", "addStyles", "styles", "urls", "value", "addUsage", "removeStyles", "removeUsage", "usages", "creator", "record", "map", "host", "addElement", "delete", "ngOnDestroy", "clear", "addHost", "hostNode", "push", "removeHost", "append<PERSON><PERSON><PERSON>", "_SharedStylesHost", "_SharedStylesHost_Factory", "Document", "NAMESPACE_URIS", "COMPONENT_REGEX", "SOURCEMAP_URL_REGEXP", "PROTOCOL_REGEXP", "COMPONENT_VARIABLE", "HOST_ATTR", "CONTENT_ATTR", "REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT", "REMOVE_STYLES_ON_COMPONENT_DESTROY", "providedIn", "shimContentAttribute", "componentShortId", "replace", "shimHostAttribute", "shim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "compId", "s", "addBaseHrefToCssSourceMap", "baseHref", "absoluteBaseHrefUrl", "URL", "cssContent", "includes", "_", "sourceMapUrl", "startsWith", "test", "pathname", "resolvedSourceMapUrl", "DomRendererFactory2", "eventManager", "sharedStylesHost", "removeStylesOnCompDestroy", "ngZone", "tracingService", "platformIsServer", "defaultRenderer", "DefaultDomRenderer2", "<PERSON><PERSON><PERSON><PERSON>", "encapsulation", "ShadowDom", "_objectSpread", "Emulated", "renderer", "getOr<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmulatedEncapsulationDomRenderer2", "applyToHost", "NoneEncapsulationDomRenderer", "applyStyles", "rendererByCompId", "id", "ShadowDom<PERSON><PERSON><PERSON>", "componentReplaced", "componentId", "_DomRendererFactory", "_DomRendererFactory_Factory", "Object", "create", "destroy", "name", "namespace", "createElementNS", "createComment", "createText", "createTextNode", "parent", "<PERSON><PERSON><PERSON><PERSON>", "targetParent", "isTemplateNode", "content", "insertBefore", "refChild", "<PERSON><PERSON><PERSON><PERSON>", "_parent", "<PERSON><PERSON><PERSON><PERSON>", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "el", "querySelector", "parentNode", "node", "nextS<PERSON>ling", "namespaceUri", "setAttributeNS", "removeAttributeNS", "addClass", "classList", "removeClass", "setStyle", "flags", "DashCase", "Important", "setProperty", "removeStyle", "removeProperty", "throwOnSyntheticProps", "checkNoSyntheticProp", "setValue", "nodeValue", "listen", "target", "event", "callback", "_this$tracingService", "getGlobalEventTarget", "wrappedCallback", "decoratePreventDefault", "wrapEventListener", "<PERSON><PERSON><PERSON><PERSON>", "allowDefaultBehavior", "runGuarded", "preventDefault", "AT_CHARCODE", "charCodeAt", "<PERSON><PERSON><PERSON>", "tagName", "hostEl", "component", "_component$getExterna", "shadowRoot", "attachShadow", "mode", "_getDOM$getBaseHref", "getBaseHref", "styleEl", "document", "styleUrls", "getExternalStyles", "call", "styleUrl", "linkEl", "nodeOrShadowRoot", "_component$getExterna2", "_getDOM$getBaseHref2", "contentAttr", "hostAttr"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/platform-browser/fesm2022/dom_renderer-DGKzginR.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { isPlatformServer, DOCUMENT, ɵgetDOM as _getDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ɵRuntimeError as _RuntimeError, Inject, Injectable, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, ɵTracingService as _TracingService, RendererStyleFlags2 } from '@angular/core';\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n    _zone;\n    _plugins;\n    _eventNameToPlugin = new Map();\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n        this._zone = _zone;\n        plugins.forEach((plugin) => {\n            plugin.manager = this;\n        });\n        this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @param options Options that configure how the event listener is bound.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler, options) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addEventListener(element, eventName, handler, options);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n        return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n        let plugin = this._eventNameToPlugin.get(eventName);\n        if (plugin) {\n            return plugin;\n        }\n        const plugins = this._plugins;\n        plugin = plugins.find((plugin) => plugin.supports(eventName));\n        if (!plugin) {\n            throw new _RuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `No event manager plugin found for event ${eventName}`);\n        }\n        this._eventNameToPlugin.set(eventName, plugin);\n        return plugin;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: EventManager, deps: [{ token: EVENT_MANAGER_PLUGINS }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: EventManager });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: EventManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [EVENT_MANAGER_PLUGINS]\n                }] }, { type: i0.NgZone }] });\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n    _doc;\n    // TODO: remove (has some usage in G3)\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    // Using non-null assertion because it's set by EventManager's constructor\n    manager;\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n/**\n * Removes all provided elements from the document.\n * @param elements An array of HTML Elements.\n */\nfunction removeElements(elements) {\n    for (const element of elements) {\n        element.remove();\n    }\n}\n/**\n * Creates a `style` element with the provided inline style content.\n * @param style A string of the inline style content.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLStyleElement instance.\n */\nfunction createStyleElement(style, doc) {\n    const styleElement = doc.createElement('style');\n    styleElement.textContent = style;\n    return styleElement;\n}\n/**\n * Searches a DOM document's head element for style elements with a matching application\n * identifier attribute (`ng-app-id`) to the provide identifier and adds usage records for each.\n * @param doc An HTML DOM document instance.\n * @param appId A string containing an Angular application identifer.\n * @param inline A Map object for tracking inline (defined via `styles` in component decorator) style usage.\n * @param external A Map object for tracking external (defined via `styleUrls` in component decorator) style usage.\n */\nfunction addServerStyles(doc, appId, inline, external) {\n    const elements = doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"],link[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"]`);\n    if (elements) {\n        for (const styleElement of elements) {\n            styleElement.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n            if (styleElement instanceof HTMLLinkElement) {\n                // Only use filename from href\n                // The href is build time generated with a unique value to prevent duplicates.\n                external.set(styleElement.href.slice(styleElement.href.lastIndexOf('/') + 1), {\n                    usage: 0,\n                    elements: [styleElement],\n                });\n            }\n            else if (styleElement.textContent) {\n                inline.set(styleElement.textContent, { usage: 0, elements: [styleElement] });\n            }\n        }\n    }\n}\n/**\n * Creates a `link` element for the provided external style URL.\n * @param url A string of the URL for the stylesheet.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLLinkElement instance.\n */\nfunction createLinkElement(url, doc) {\n    const linkElement = doc.createElement('link');\n    linkElement.setAttribute('rel', 'stylesheet');\n    linkElement.setAttribute('href', url);\n    return linkElement;\n}\nclass SharedStylesHost {\n    doc;\n    appId;\n    nonce;\n    /**\n     * Provides usage information for active inline style content and associated HTML <style> elements.\n     * Embedded styles typically originate from the `styles` metadata of a rendered component.\n     */\n    inline = new Map();\n    /**\n     * Provides usage information for active external style URLs and the associated HTML <link> elements.\n     * External styles typically originate from the `ɵɵExternalStylesFeature` of a rendered component.\n     */\n    external = new Map();\n    /**\n     * Set of host DOM nodes that will have styles attached.\n     */\n    hosts = new Set();\n    /**\n     * Whether the application code is currently executing on a server.\n     */\n    isServer;\n    constructor(doc, appId, nonce, platformId = {}) {\n        this.doc = doc;\n        this.appId = appId;\n        this.nonce = nonce;\n        this.isServer = isPlatformServer(platformId);\n        addServerStyles(doc, appId, this.inline, this.external);\n        this.hosts.add(doc.head);\n    }\n    /**\n     * Adds embedded styles to the DOM via HTML `style` elements.\n     * @param styles An array of style content strings.\n     */\n    addStyles(styles, urls) {\n        for (const value of styles) {\n            this.addUsage(value, this.inline, createStyleElement);\n        }\n        urls?.forEach((value) => this.addUsage(value, this.external, createLinkElement));\n    }\n    /**\n     * Removes embedded styles from the DOM that were added as HTML `style` elements.\n     * @param styles An array of style content strings.\n     */\n    removeStyles(styles, urls) {\n        for (const value of styles) {\n            this.removeUsage(value, this.inline);\n        }\n        urls?.forEach((value) => this.removeUsage(value, this.external));\n    }\n    addUsage(value, usages, creator) {\n        // Attempt to get any current usage of the value\n        const record = usages.get(value);\n        // If existing, just increment the usage count\n        if (record) {\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) && record.usage === 0) {\n                // A usage count of zero indicates a preexisting server generated style.\n                // This attribute is solely used for debugging purposes of SSR style reuse.\n                record.elements.forEach((element) => element.setAttribute('ng-style-reused', ''));\n            }\n            record.usage++;\n        }\n        else {\n            // Otherwise, create an entry to track the elements and add element for each host\n            usages.set(value, {\n                usage: 1,\n                elements: [...this.hosts].map((host) => this.addElement(host, creator(value, this.doc))),\n            });\n        }\n    }\n    removeUsage(value, usages) {\n        // Attempt to get any current usage of the value\n        const record = usages.get(value);\n        // If there is a record, reduce the usage count and if no longer used,\n        // remove from DOM and delete usage record.\n        if (record) {\n            record.usage--;\n            if (record.usage <= 0) {\n                removeElements(record.elements);\n                usages.delete(value);\n            }\n        }\n    }\n    ngOnDestroy() {\n        for (const [, { elements }] of [...this.inline, ...this.external]) {\n            removeElements(elements);\n        }\n        this.hosts.clear();\n    }\n    /**\n     * Adds a host node to the set of style hosts and adds all existing style usage to\n     * the newly added host node.\n     *\n     * This is currently only used for Shadow DOM encapsulation mode.\n     */\n    addHost(hostNode) {\n        this.hosts.add(hostNode);\n        // Add existing styles to new host\n        for (const [style, { elements }] of this.inline) {\n            elements.push(this.addElement(hostNode, createStyleElement(style, this.doc)));\n        }\n        for (const [url, { elements }] of this.external) {\n            elements.push(this.addElement(hostNode, createLinkElement(url, this.doc)));\n        }\n    }\n    removeHost(hostNode) {\n        this.hosts.delete(hostNode);\n    }\n    addElement(host, element) {\n        // Add a nonce if present\n        if (this.nonce) {\n            element.setAttribute('nonce', this.nonce);\n        }\n        // Add application identifier when on the server to support client-side reuse\n        if (this.isServer) {\n            element.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n        }\n        // Insert the element into the DOM with the host node as parent\n        return host.appendChild(element);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: SharedStylesHost, deps: [{ token: DOCUMENT }, { token: APP_ID }, { token: CSP_NONCE, optional: true }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: SharedStylesHost });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: SharedStylesHost, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }] });\n\nconst NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n    'math': 'http://www.w3.org/1998/Math/MathML',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst SOURCEMAP_URL_REGEXP = /\\/\\*#\\s*sourceMappingURL=(.+?)\\s*\\*\\//;\nconst PROTOCOL_REGEXP = /^https?:/;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n    providedIn: 'root',\n    factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT,\n});\nfunction shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n    return styles.map((s) => s.replace(COMPONENT_REGEX, compId));\n}\n/**\n * Prepends a baseHref to the `sourceMappingURL` within the provided CSS content.\n * If the `sourceMappingURL` contains an inline (encoded) map, the function skips processing.\n *\n * @note For inline stylesheets, the `sourceMappingURL` is relative to the page's origin\n * and not the provided baseHref. This function is needed as when accessing the page with a URL\n * containing two or more segments.\n * For example, if the baseHref is set to `/`, and you visit a URL like `http://localhost/foo/bar`,\n * the map would be requested from `http://localhost/foo/bar/comp.css.map` instead of what you'd expect,\n * which is `http://localhost/comp.css.map`. This behavior is corrected by modifying the `sourceMappingURL`\n * to ensure external source maps are loaded relative to the baseHref.\n *\n\n * @param baseHref - The base URL to prepend to the `sourceMappingURL`.\n * @param styles - An array of CSS content strings, each potentially containing a `sourceMappingURL`.\n * @returns The updated array of CSS content strings with modified `sourceMappingURL` values,\n * or the original content if no modification is needed.\n */\nfunction addBaseHrefToCssSourceMap(baseHref, styles) {\n    if (!baseHref) {\n        return styles;\n    }\n    const absoluteBaseHrefUrl = new URL(baseHref, 'http://localhost');\n    return styles.map((cssContent) => {\n        if (!cssContent.includes('sourceMappingURL=')) {\n            return cssContent;\n        }\n        return cssContent.replace(SOURCEMAP_URL_REGEXP, (_, sourceMapUrl) => {\n            if (sourceMapUrl[0] === '/' ||\n                sourceMapUrl.startsWith('data:') ||\n                PROTOCOL_REGEXP.test(sourceMapUrl)) {\n                return `/*# sourceMappingURL=${sourceMapUrl} */`;\n            }\n            const { pathname: resolvedSourceMapUrl } = new URL(sourceMapUrl, absoluteBaseHrefUrl);\n            return `/*# sourceMappingURL=${resolvedSourceMapUrl} */`;\n        });\n    });\n}\nclass DomRendererFactory2 {\n    eventManager;\n    sharedStylesHost;\n    appId;\n    removeStylesOnCompDestroy;\n    doc;\n    platformId;\n    ngZone;\n    nonce;\n    tracingService;\n    rendererByCompId = new Map();\n    defaultRenderer;\n    platformIsServer;\n    constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null, tracingService = null) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        this.doc = doc;\n        this.platformId = platformId;\n        this.ngZone = ngZone;\n        this.nonce = nonce;\n        this.tracingService = tracingService;\n        this.platformIsServer = isPlatformServer(platformId);\n        this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer, this.tracingService);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n            // Domino does not support shadow DOM.\n            type = { ...type, encapsulation: ViewEncapsulation.Emulated };\n        }\n        const renderer = this.getOrCreateRenderer(element, type);\n        // Renderers have different logic due to different encapsulation behaviours.\n        // Ex: for emulated, an attribute is added to the element.\n        if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n            renderer.applyToHost(element);\n        }\n        else if (renderer instanceof NoneEncapsulationDomRenderer) {\n            renderer.applyStyles();\n        }\n        return renderer;\n    }\n    getOrCreateRenderer(element, type) {\n        const rendererByCompId = this.rendererByCompId;\n        let renderer = rendererByCompId.get(type.id);\n        if (!renderer) {\n            const doc = this.doc;\n            const ngZone = this.ngZone;\n            const eventManager = this.eventManager;\n            const sharedStylesHost = this.sharedStylesHost;\n            const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n            const platformIsServer = this.platformIsServer;\n            const tracingService = this.tracingService;\n            switch (type.encapsulation) {\n                case ViewEncapsulation.Emulated:\n                    renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n                    break;\n                case ViewEncapsulation.ShadowDom:\n                    return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer, tracingService);\n                default:\n                    renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n                    break;\n            }\n            rendererByCompId.set(type.id, renderer);\n        }\n        return renderer;\n    }\n    ngOnDestroy() {\n        this.rendererByCompId.clear();\n    }\n    /**\n     * Used during HMR to clear any cached data about a component.\n     * @param componentId ID of the component that is being replaced.\n     */\n    componentReplaced(componentId) {\n        this.rendererByCompId.delete(componentId);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: EventManager }, { token: SharedStylesHost }, { token: APP_ID }, { token: REMOVE_STYLES_ON_COMPONENT_DESTROY }, { token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.NgZone }, { token: CSP_NONCE }, { token: _TracingService, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DomRendererFactory2 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: EventManager }, { type: SharedStylesHost }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }] }, { type: i0.ɵTracingService, decorators: [{\n                    type: Inject,\n                    args: [_TracingService]\n                }, {\n                    type: Optional\n                }] }] });\nclass DefaultDomRenderer2 {\n    eventManager;\n    doc;\n    ngZone;\n    platformIsServer;\n    tracingService;\n    data = Object.create(null);\n    /**\n     * By default this renderer throws when encountering synthetic properties\n     * This can be disabled for example by the AsyncAnimationRendererFactory\n     */\n    throwOnSyntheticProps = true;\n    constructor(eventManager, doc, ngZone, platformIsServer, tracingService) {\n        this.eventManager = eventManager;\n        this.doc = doc;\n        this.ngZone = ngZone;\n        this.platformIsServer = platformIsServer;\n        this.tracingService = tracingService;\n    }\n    destroy() { }\n    destroyNode = null;\n    createElement(name, namespace) {\n        if (namespace) {\n            // TODO: `|| namespace` was added in\n            // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n            // support how Ivy passed around the namespace URI rather than short name at the time. It did\n            // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n            // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n            // namespaces should be and make it consistent.\n            // Related issues:\n            // https://github.com/angular/angular/issues/44028\n            // https://github.com/angular/angular/issues/44883\n            return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return this.doc.createElement(name);\n    }\n    createComment(value) {\n        return this.doc.createComment(value);\n    }\n    createText(value) {\n        return this.doc.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        const targetParent = isTemplateNode(parent) ? parent.content : parent;\n        targetParent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            const targetParent = isTemplateNode(parent) ? parent.content : parent;\n            targetParent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(_parent, oldChild) {\n        oldChild.remove();\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n        if (!el) {\n            throw new _RuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            // removeProperty has no effect when used on camelCased properties.\n            el.style.removeProperty(style);\n        }\n        else {\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        if (el == null) {\n            return;\n        }\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback, options) {\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            target = _getDOM().getGlobalEventTarget(this.doc, target);\n            if (!target) {\n                throw new _RuntimeError(5102 /* RuntimeErrorCode.UNSUPPORTED_EVENT_TARGET */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `Unsupported event target ${target} for event ${event}`);\n            }\n        }\n        let wrappedCallback = this.decoratePreventDefault(callback);\n        if (this.tracingService?.wrapEventListener) {\n            wrappedCallback = this.tracingService.wrapEventListener(target, event, wrappedCallback);\n        }\n        return this.eventManager.addEventListener(target, event, wrappedCallback, options);\n    }\n    decoratePreventDefault(eventHandler) {\n        // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n        // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n        // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n        // unwrap the listener (see below).\n        return (event) => {\n            // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n            // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n            // debug_node can inspect the listener toString contents for the existence of this special\n            // token. Because the token is a string literal, it is ensured to not be modified by compiled\n            // code.\n            if (event === '__ngUnwrap__') {\n                return eventHandler;\n            }\n            // Run the event handler inside the ngZone because event handlers are not patched\n            // by Zone on the server. This is required only for tests.\n            const allowDefaultBehavior = this.platformIsServer\n                ? this.ngZone.runGuarded(() => eventHandler(event))\n                : eventHandler(event);\n            if (allowDefaultBehavior === false) {\n                event.preventDefault();\n            }\n            return undefined;\n        };\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new _RuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Make sure \\`provideAnimationsAsync()\\`, \\`provideAnimations()\\` or \\`provideNoopAnimations()\\` call was added to a list of providers used to bootstrap an application.\n  - There is a corresponding animation configuration named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.dev/api/core/Component#animations).`);\n    }\n}\nfunction isTemplateNode(node) {\n    return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    sharedStylesHost;\n    hostEl;\n    shadowRoot;\n    constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer, tracingService) {\n        super(eventManager, doc, ngZone, platformIsServer, tracingService);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        let styles = component.styles;\n        if (ngDevMode) {\n            // We only do this in development, as for production users should not add CSS sourcemaps to components.\n            const baseHref = _getDOM().getBaseHref(doc) ?? '';\n            styles = addBaseHrefToCssSourceMap(baseHref, styles);\n        }\n        styles = shimStylesContent(component.id, styles);\n        for (const style of styles) {\n            const styleEl = document.createElement('style');\n            if (nonce) {\n                styleEl.setAttribute('nonce', nonce);\n            }\n            styleEl.textContent = style;\n            this.shadowRoot.appendChild(styleEl);\n        }\n        // Apply any external component styles to the shadow root for the component's element.\n        // The ShadowDOM renderer uses an alternative execution path for component styles that\n        // does not use the SharedStylesHost that other encapsulation modes leverage. Much like\n        // the manual addition of embedded styles directly above, any external stylesheets\n        // must be manually added here to ensure ShadowDOM components are correctly styled.\n        // TODO: Consider reworking the DOM Renderers to consolidate style handling.\n        const styleUrls = component.getExternalStyles?.();\n        if (styleUrls) {\n            for (const styleUrl of styleUrls) {\n                const linkEl = createLinkElement(styleUrl, doc);\n                if (nonce) {\n                    linkEl.setAttribute('nonce', nonce);\n                }\n                this.shadowRoot.appendChild(linkEl);\n            }\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(_parent, oldChild) {\n        return super.removeChild(null, oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n    sharedStylesHost;\n    removeStylesOnCompDestroy;\n    styles;\n    styleUrls;\n    constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId) {\n        super(eventManager, doc, ngZone, platformIsServer, tracingService);\n        this.sharedStylesHost = sharedStylesHost;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        let styles = component.styles;\n        if (ngDevMode) {\n            // We only do this in development, as for production users should not add CSS sourcemaps to components.\n            const baseHref = _getDOM().getBaseHref(doc) ?? '';\n            styles = addBaseHrefToCssSourceMap(baseHref, styles);\n        }\n        this.styles = compId ? shimStylesContent(compId, styles) : styles;\n        this.styleUrls = component.getExternalStyles?.(compId);\n    }\n    applyStyles() {\n        this.sharedStylesHost.addStyles(this.styles, this.styleUrls);\n    }\n    destroy() {\n        if (!this.removeStylesOnCompDestroy) {\n            return;\n        }\n        this.sharedStylesHost.removeStyles(this.styles, this.styleUrls);\n    }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n    contentAttr;\n    hostAttr;\n    constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService) {\n        const compId = appId + '-' + component.id;\n        super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId);\n        this.contentAttr = shimContentAttribute(compId);\n        this.hostAttr = shimHostAttribute(compId);\n    }\n    applyToHost(element) {\n        this.applyStyles();\n        this.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\n\nexport { DomRendererFactory2, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, REMOVE_STYLES_ON_COMPONENT_DESTROY, SharedStylesHost };\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,gBAAgB,EAAEC,QAAQ,EAAEC,OAAO,IAAIC,OAAO,QAAQ,iBAAiB;AAChF,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,aAAa,IAAIC,aAAa,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,eAAe,IAAIC,eAAe,EAAEC,mBAAmB,QAAQ,eAAe;;AAExN;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,IAAIb,cAAc,CAACc,SAAS,GAAG,qBAAqB,GAAG,EAAE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EAIf;AACJ;AACA;EACIC,WAAWA,CAACC,OAAO,EAAEC,KAAK,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA,6BAJP,IAAIC,GAAG,CAAC,CAAC;IAK1B,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClBD,OAAO,CAACI,OAAO,CAAEC,MAAM,IAAK;MACxBA,MAAM,CAACC,OAAO,GAAG,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,GAAGP,OAAO,CAACQ,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,gBAAgBA,CAACC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;IACnD,MAAMT,MAAM,GAAG,IAAI,CAACU,cAAc,CAACH,SAAS,CAAC;IAC7C,OAAOP,MAAM,CAACK,gBAAgB,CAACC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACxE;EACA;AACJ;AACA;EACIE,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACf,KAAK;EACrB;EACA;EACAc,cAAcA,CAACH,SAAS,EAAE;IACtB,IAAIP,MAAM,GAAG,IAAI,CAACY,kBAAkB,CAACC,GAAG,CAACN,SAAS,CAAC;IACnD,IAAIP,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,MAAML,OAAO,GAAG,IAAI,CAACO,QAAQ;IAC7BF,MAAM,GAAGL,OAAO,CAACmB,IAAI,CAAEd,MAAM,IAAKA,MAAM,CAACe,QAAQ,CAACR,SAAS,CAAC,CAAC;IAC7D,IAAI,CAACP,MAAM,EAAE;MACT,MAAM,IAAIpB,aAAa,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAOY,SAAS,KAAK,WAAW,IAAIA,SAAS,KACnH,2CAA2Ce,SAAS,EAAE,CAAC;IAC/D;IACA,IAAI,CAACK,kBAAkB,CAACI,GAAG,CAACT,SAAS,EAAEP,MAAM,CAAC;IAC9C,OAAOA,MAAM;EACjB;AAGJ;AAACiB,aAAA,GAnDKxB,YAAY;AAAAI,eAAA,CAAZJ,YAAY,wBAAAyB,sBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAiDsF1B,aAAY,EAGlChB,EAAE,CAAA2C,QAAA,CAHkD7B,qBAAqB,GAGzEd,EAAE,CAAA2C,QAAA,CAHoF3C,EAAE,CAAC4C,MAAM;AAAA;AAAAxB,eAAA,CAjD3KJ,YAAY,+BAoDgEhB,EAAE,CAAA6C,kBAAA;EAAAC,KAAA,EAFwB9B,aAAY;EAAA+B,OAAA,EAAZ/B,aAAY,CAAAgC;AAAA;AAExH;EAAA,QAAAjC,SAAA,oBAAAA,SAAA,KAAkFf,EAAE,CAAAiD,iBAAA,CAAQjC,YAAY,EAAc,CAAC;IAC3GkC,IAAI,EAAE7C;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE6C,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CF,IAAI,EAAE9C,MAAM;MACZiD,IAAI,EAAE,CAACvC,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEoC,IAAI,EAAElD,EAAE,CAAC4C;EAAO,CAAC,CAAC;AAAA;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,kBAAkB,CAAC;EAErB;EACArC,WAAWA,CAACsC,IAAI,EAAE;IAAAnC,eAAA;IAGlB;IAAAA,eAAA;IAFI,IAAI,CAACmC,IAAI,GAAGA,IAAI;EACpB;AAGJ;;AAEA;AACA,MAAMC,qBAAqB,GAAG,WAAW;AACzC;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,QAAQ,EAAE;EAC9B,KAAK,MAAM7B,OAAO,IAAI6B,QAAQ,EAAE;IAC5B7B,OAAO,CAAC8B,MAAM,CAAC,CAAC;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACpC,MAAMC,YAAY,GAAGD,GAAG,CAACE,aAAa,CAAC,OAAO,CAAC;EAC/CD,YAAY,CAACE,WAAW,GAAGJ,KAAK;EAChC,OAAOE,YAAY;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAACJ,GAAG,EAAEK,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAAA,IAAAC,SAAA;EACnD,MAAMZ,QAAQ,IAAAY,SAAA,GAAGR,GAAG,CAACS,IAAI,cAAAD,SAAA,uBAARA,SAAA,CAAUE,gBAAgB,CAAC,SAAShB,qBAAqB,KAAKW,KAAK,WAAWX,qBAAqB,KAAKW,KAAK,IAAI,CAAC;EACnI,IAAIT,QAAQ,EAAE;IACV,KAAK,MAAMK,YAAY,IAAIL,QAAQ,EAAE;MACjCK,YAAY,CAACU,eAAe,CAACjB,qBAAqB,CAAC;MACnD,IAAIO,YAAY,YAAYW,eAAe,EAAE;QACzC;QACA;QACAL,QAAQ,CAAC9B,GAAG,CAACwB,YAAY,CAACY,IAAI,CAACjD,KAAK,CAACqC,YAAY,CAACY,IAAI,CAACC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;UAC1EC,KAAK,EAAE,CAAC;UACRnB,QAAQ,EAAE,CAACK,YAAY;QAC3B,CAAC,CAAC;MACN,CAAC,MACI,IAAIA,YAAY,CAACE,WAAW,EAAE;QAC/BG,MAAM,CAAC7B,GAAG,CAACwB,YAAY,CAACE,WAAW,EAAE;UAAEY,KAAK,EAAE,CAAC;UAAEnB,QAAQ,EAAE,CAACK,YAAY;QAAE,CAAC,CAAC;MAChF;IACJ;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,iBAAiBA,CAACC,GAAG,EAAEjB,GAAG,EAAE;EACjC,MAAMkB,WAAW,GAAGlB,GAAG,CAACE,aAAa,CAAC,MAAM,CAAC;EAC7CgB,WAAW,CAACC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC;EAC7CD,WAAW,CAACC,YAAY,CAAC,MAAM,EAAEF,GAAG,CAAC;EACrC,OAAOC,WAAW;AACtB;AACA,MAAME,gBAAgB,CAAC;EAsBnBjE,WAAWA,CAAC6C,GAAG,EAAEK,KAAK,EAAEgB,KAAK,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;IAAAhE,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAlBhD;AACJ;AACA;AACA;IAHIA,eAAA,iBAIS,IAAIC,GAAG,CAAC,CAAC;IAClB;AACJ;AACA;AACA;IAHID,eAAA,mBAIW,IAAIC,GAAG,CAAC,CAAC;IACpB;AACJ;AACA;IAFID,eAAA,gBAGQ,IAAIiE,GAAG,CAAC,CAAC;IACjB;AACJ;AACA;IAFIjE,eAAA;IAKI,IAAI,CAAC0C,GAAG,GAAGA,GAAG;IACd,IAAI,CAACK,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACgB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,QAAQ,GAAG1F,gBAAgB,CAACwF,UAAU,CAAC;IAC5ClB,eAAe,CAACJ,GAAG,EAAEK,KAAK,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC;IACvD,IAAI,CAACkB,KAAK,CAACC,GAAG,CAAC1B,GAAG,CAACS,IAAI,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACIkB,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACpB,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;MACxB,IAAI,CAACG,QAAQ,CAACD,KAAK,EAAE,IAAI,CAACxB,MAAM,EAAER,kBAAkB,CAAC;IACzD;IACA+B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAErE,OAAO,CAAEsE,KAAK,IAAK,IAAI,CAACC,QAAQ,CAACD,KAAK,EAAE,IAAI,CAACvB,QAAQ,EAAES,iBAAiB,CAAC,CAAC;EACpF;EACA;AACJ;AACA;AACA;EACIgB,YAAYA,CAACJ,MAAM,EAAEC,IAAI,EAAE;IACvB,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;MACxB,IAAI,CAACK,WAAW,CAACH,KAAK,EAAE,IAAI,CAACxB,MAAM,CAAC;IACxC;IACAuB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAErE,OAAO,CAAEsE,KAAK,IAAK,IAAI,CAACG,WAAW,CAACH,KAAK,EAAE,IAAI,CAACvB,QAAQ,CAAC,CAAC;EACpE;EACAwB,QAAQA,CAACD,KAAK,EAAEI,MAAM,EAAEC,OAAO,EAAE;IAC7B;IACA,MAAMC,MAAM,GAAGF,MAAM,CAAC5D,GAAG,CAACwD,KAAK,CAAC;IAChC;IACA,IAAIM,MAAM,EAAE;MACR,IAAI,CAAC,OAAOnF,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKmF,MAAM,CAACrB,KAAK,KAAK,CAAC,EAAE;QACvE;QACA;QACAqB,MAAM,CAACxC,QAAQ,CAACpC,OAAO,CAAEO,OAAO,IAAKA,OAAO,CAACoD,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;MACrF;MACAiB,MAAM,CAACrB,KAAK,EAAE;IAClB,CAAC,MACI;MACD;MACAmB,MAAM,CAACzD,GAAG,CAACqD,KAAK,EAAE;QACdf,KAAK,EAAE,CAAC;QACRnB,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC6B,KAAK,CAAC,CAACY,GAAG,CAAEC,IAAI,IAAK,IAAI,CAACC,UAAU,CAACD,IAAI,EAAEH,OAAO,CAACL,KAAK,EAAE,IAAI,CAAC9B,GAAG,CAAC,CAAC;MAC3F,CAAC,CAAC;IACN;EACJ;EACAiC,WAAWA,CAACH,KAAK,EAAEI,MAAM,EAAE;IACvB;IACA,MAAME,MAAM,GAAGF,MAAM,CAAC5D,GAAG,CAACwD,KAAK,CAAC;IAChC;IACA;IACA,IAAIM,MAAM,EAAE;MACRA,MAAM,CAACrB,KAAK,EAAE;MACd,IAAIqB,MAAM,CAACrB,KAAK,IAAI,CAAC,EAAE;QACnBpB,cAAc,CAACyC,MAAM,CAACxC,QAAQ,CAAC;QAC/BsC,MAAM,CAACM,MAAM,CAACV,KAAK,CAAC;MACxB;IACJ;EACJ;EACAW,WAAWA,CAAA,EAAG;IACV,KAAK,MAAM,GAAG;MAAE7C;IAAS,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAACU,MAAM,EAAE,GAAG,IAAI,CAACC,QAAQ,CAAC,EAAE;MAC/DZ,cAAc,CAACC,QAAQ,CAAC;IAC5B;IACA,IAAI,CAAC6B,KAAK,CAACiB,KAAK,CAAC,CAAC;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAI,CAACnB,KAAK,CAACC,GAAG,CAACkB,QAAQ,CAAC;IACxB;IACA,KAAK,MAAM,CAAC7C,KAAK,EAAE;MAAEH;IAAS,CAAC,CAAC,IAAI,IAAI,CAACU,MAAM,EAAE;MAC7CV,QAAQ,CAACiD,IAAI,CAAC,IAAI,CAACN,UAAU,CAACK,QAAQ,EAAE9C,kBAAkB,CAACC,KAAK,EAAE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IACjF;IACA,KAAK,MAAM,CAACiB,GAAG,EAAE;MAAErB;IAAS,CAAC,CAAC,IAAI,IAAI,CAACW,QAAQ,EAAE;MAC7CX,QAAQ,CAACiD,IAAI,CAAC,IAAI,CAACN,UAAU,CAACK,QAAQ,EAAE5B,iBAAiB,CAACC,GAAG,EAAE,IAAI,CAACjB,GAAG,CAAC,CAAC,CAAC;IAC9E;EACJ;EACA8C,UAAUA,CAACF,QAAQ,EAAE;IACjB,IAAI,CAACnB,KAAK,CAACe,MAAM,CAACI,QAAQ,CAAC;EAC/B;EACAL,UAAUA,CAACD,IAAI,EAAEvE,OAAO,EAAE;IACtB;IACA,IAAI,IAAI,CAACsD,KAAK,EAAE;MACZtD,OAAO,CAACoD,YAAY,CAAC,OAAO,EAAE,IAAI,CAACE,KAAK,CAAC;IAC7C;IACA;IACA,IAAI,IAAI,CAACG,QAAQ,EAAE;MACfzD,OAAO,CAACoD,YAAY,CAACzB,qBAAqB,EAAE,IAAI,CAACW,KAAK,CAAC;IAC3D;IACA;IACA,OAAOiC,IAAI,CAACS,WAAW,CAAChF,OAAO,CAAC;EACpC;AAGJ;AAACiF,iBAAA,GA1HK5B,gBAAgB;AAAA9D,eAAA,CAAhB8D,gBAAgB,wBAAA6B,0BAAArE,iBAAA;EAAA,YAAAA,iBAAA,IAwHkFwC,iBAAgB,EA7MtClF,EAAE,CAAA2C,QAAA,CA6MsD9C,QAAQ,GA7MhEG,EAAE,CAAA2C,QAAA,CA6M2ErC,MAAM,GA7MnFN,EAAE,CAAA2C,QAAA,CA6M8FpC,SAAS,MA7MzGP,EAAE,CAAA2C,QAAA,CA6MoInC,WAAW;AAAA;AAAAY,eAAA,CAxH7N8D,gBAAgB,+BArF4DlF,EAAE,CAAA6C,kBAAA;EAAAC,KAAA,EA8MwBoC,iBAAgB;EAAAnC,OAAA,EAAhBmC,iBAAgB,CAAAlC;AAAA;AAE5H;EAAA,QAAAjC,SAAA,oBAAAA,SAAA,KAhNkFf,EAAE,CAAAiD,iBAAA,CAgNQiC,gBAAgB,EAAc,CAAC;IAC/GhC,IAAI,EAAE7C;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE6C,IAAI,EAAE8D,QAAQ;IAAE5D,UAAU,EAAE,CAAC;MAC9CF,IAAI,EAAE9C,MAAM;MACZiD,IAAI,EAAE,CAACxD,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEqD,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAE9C,MAAM;MACZiD,IAAI,EAAE,CAAC/C,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAE4C,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAE9C,MAAM;MACZiD,IAAI,EAAE,CAAC9C,SAAS;IACpB,CAAC,EAAE;MACC2C,IAAI,EAAEzC;IACV,CAAC;EAAE,CAAC,EAAE;IAAEyC,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAE9C,MAAM;MACZiD,IAAI,EAAE,CAAC7C,WAAW;IACtB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMyG,cAAc,GAAG;EACnB,KAAK,EAAE,4BAA4B;EACnC,OAAO,EAAE,8BAA8B;EACvC,OAAO,EAAE,8BAA8B;EACvC,KAAK,EAAE,sCAAsC;EAC7C,OAAO,EAAE,+BAA+B;EACxC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,eAAe,GAAG,SAAS;AACjC,MAAMC,oBAAoB,GAAG,uCAAuC;AACpE,MAAMC,eAAe,GAAG,UAAU;AAClC,MAAMC,kBAAkB,GAAG,QAAQ;AACnC,MAAMC,SAAS,GAAG,WAAWD,kBAAkB,EAAE;AACjD,MAAME,YAAY,GAAG,cAAcF,kBAAkB,EAAE;AACvD;AACA;AACA;AACA,MAAMG,0CAA0C,GAAG,IAAI;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kCAAkC,GAAG,IAAIxH,cAAc,CAACc,SAAS,GAAG,2BAA2B,GAAG,EAAE,EAAE;EACxG2G,UAAU,EAAE,MAAM;EAClB3E,OAAO,EAAEA,CAAA,KAAMyE;AACnB,CAAC,CAAC;AACF,SAASG,oBAAoBA,CAACC,gBAAgB,EAAE;EAC5C,OAAOL,YAAY,CAACM,OAAO,CAACX,eAAe,EAAEU,gBAAgB,CAAC;AAClE;AACA,SAASE,iBAAiBA,CAACF,gBAAgB,EAAE;EACzC,OAAON,SAAS,CAACO,OAAO,CAACX,eAAe,EAAEU,gBAAgB,CAAC;AAC/D;AACA,SAASG,iBAAiBA,CAACC,MAAM,EAAEtC,MAAM,EAAE;EACvC,OAAOA,MAAM,CAACS,GAAG,CAAE8B,CAAC,IAAKA,CAAC,CAACJ,OAAO,CAACX,eAAe,EAAEc,MAAM,CAAC,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,yBAAyBA,CAACC,QAAQ,EAAEzC,MAAM,EAAE;EACjD,IAAI,CAACyC,QAAQ,EAAE;IACX,OAAOzC,MAAM;EACjB;EACA,MAAM0C,mBAAmB,GAAG,IAAIC,GAAG,CAACF,QAAQ,EAAE,kBAAkB,CAAC;EACjE,OAAOzC,MAAM,CAACS,GAAG,CAAEmC,UAAU,IAAK;IAC9B,IAAI,CAACA,UAAU,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MAC3C,OAAOD,UAAU;IACrB;IACA,OAAOA,UAAU,CAACT,OAAO,CAACV,oBAAoB,EAAE,CAACqB,CAAC,EAAEC,YAAY,KAAK;MACjE,IAAIA,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,IACvBA,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC,IAChCtB,eAAe,CAACuB,IAAI,CAACF,YAAY,CAAC,EAAE;QACpC,OAAO,wBAAwBA,YAAY,KAAK;MACpD;MACA,MAAM;QAAEG,QAAQ,EAAEC;MAAqB,CAAC,GAAG,IAAIR,GAAG,CAACI,YAAY,EAAEL,mBAAmB,CAAC;MACrF,OAAO,wBAAwBS,oBAAoB,KAAK;IAC5D,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,MAAMC,mBAAmB,CAAC;EAatB7H,WAAWA,CAAC8H,YAAY,EAAEC,gBAAgB,EAAE7E,KAAK,EAAE8E,yBAAyB,EAAEnF,GAAG,EAAEsB,UAAU,EAAE8D,MAAM,EAAE/D,KAAK,GAAG,IAAI,EAAEgE,cAAc,GAAG,IAAI,EAAE;IAAA/H,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,2BAHzH,IAAIC,GAAG,CAAC,CAAC;IAAAD,eAAA;IAAAA,eAAA;IAIxB,IAAI,CAAC2H,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC7E,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC8E,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACnF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACsB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC8D,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC/D,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACgE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,gBAAgB,GAAGxJ,gBAAgB,CAACwF,UAAU,CAAC;IACpD,IAAI,CAACiE,eAAe,GAAG,IAAIC,mBAAmB,CAACP,YAAY,EAAEjF,GAAG,EAAEoF,MAAM,EAAE,IAAI,CAACE,gBAAgB,EAAE,IAAI,CAACD,cAAc,CAAC;EACzH;EACAI,cAAcA,CAAC1H,OAAO,EAAEqB,IAAI,EAAE;IAC1B,IAAI,CAACrB,OAAO,IAAI,CAACqB,IAAI,EAAE;MACnB,OAAO,IAAI,CAACmG,eAAe;IAC/B;IACA,IAAI,IAAI,CAACD,gBAAgB,IAAIlG,IAAI,CAACsG,aAAa,KAAK9I,iBAAiB,CAAC+I,SAAS,EAAE;MAC7E;MACAvG,IAAI,GAAAwG,aAAA,CAAAA,aAAA,KAAQxG,IAAI;QAAEsG,aAAa,EAAE9I,iBAAiB,CAACiJ;MAAQ,EAAE;IACjE;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CAAChI,OAAO,EAAEqB,IAAI,CAAC;IACxD;IACA;IACA,IAAI0G,QAAQ,YAAYE,iCAAiC,EAAE;MACvDF,QAAQ,CAACG,WAAW,CAAClI,OAAO,CAAC;IACjC,CAAC,MACI,IAAI+H,QAAQ,YAAYI,4BAA4B,EAAE;MACvDJ,QAAQ,CAACK,WAAW,CAAC,CAAC;IAC1B;IACA,OAAOL,QAAQ;EACnB;EACAC,mBAAmBA,CAAChI,OAAO,EAAEqB,IAAI,EAAE;IAC/B,MAAMgH,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAIN,QAAQ,GAAGM,gBAAgB,CAAC9H,GAAG,CAACc,IAAI,CAACiH,EAAE,CAAC;IAC5C,IAAI,CAACP,QAAQ,EAAE;MACX,MAAM9F,GAAG,GAAG,IAAI,CAACA,GAAG;MACpB,MAAMoF,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,MAAMH,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,MAAMC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,MAAMC,yBAAyB,GAAG,IAAI,CAACA,yBAAyB;MAChE,MAAMG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,MAAMD,cAAc,GAAG,IAAI,CAACA,cAAc;MAC1C,QAAQjG,IAAI,CAACsG,aAAa;QACtB,KAAK9I,iBAAiB,CAACiJ,QAAQ;UAC3BC,QAAQ,GAAG,IAAIE,iCAAiC,CAACf,YAAY,EAAEC,gBAAgB,EAAE9F,IAAI,EAAE,IAAI,CAACiB,KAAK,EAAE8E,yBAAyB,EAAEnF,GAAG,EAAEoF,MAAM,EAAEE,gBAAgB,EAAED,cAAc,CAAC;UAC5K;QACJ,KAAKzI,iBAAiB,CAAC+I,SAAS;UAC5B,OAAO,IAAIW,iBAAiB,CAACrB,YAAY,EAAEC,gBAAgB,EAAEnH,OAAO,EAAEqB,IAAI,EAAEY,GAAG,EAAEoF,MAAM,EAAE,IAAI,CAAC/D,KAAK,EAAEiE,gBAAgB,EAAED,cAAc,CAAC;QAC1I;UACIS,QAAQ,GAAG,IAAII,4BAA4B,CAACjB,YAAY,EAAEC,gBAAgB,EAAE9F,IAAI,EAAE+F,yBAAyB,EAAEnF,GAAG,EAAEoF,MAAM,EAAEE,gBAAgB,EAAED,cAAc,CAAC;UAC3J;MACR;MACAe,gBAAgB,CAAC3H,GAAG,CAACW,IAAI,CAACiH,EAAE,EAAEP,QAAQ,CAAC;IAC3C;IACA,OAAOA,QAAQ;EACnB;EACArD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2D,gBAAgB,CAAC1D,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;EACI6D,iBAAiBA,CAACC,WAAW,EAAE;IAC3B,IAAI,CAACJ,gBAAgB,CAAC5D,MAAM,CAACgE,WAAW,CAAC;EAC7C;AAGJ;AAACC,mBAAA,GAlFKzB,mBAAmB;AAAA1H,eAAA,CAAnB0H,mBAAmB,wBAAA0B,4BAAA9H,iBAAA;EAAA,YAAAA,iBAAA,IAgF+EoG,mBAAmB,EA9XzC9I,EAAE,CAAA2C,QAAA,CA8XyD3B,YAAY,GA9XvEhB,EAAE,CAAA2C,QAAA,CA8XkFuC,gBAAgB,GA9XpGlF,EAAE,CAAA2C,QAAA,CA8X+GrC,MAAM,GA9XvHN,EAAE,CAAA2C,QAAA,CA8XkI8E,kCAAkC,GA9XtKzH,EAAE,CAAA2C,QAAA,CA8XiL9C,QAAQ,GA9X3LG,EAAE,CAAA2C,QAAA,CA8XsMnC,WAAW,GA9XnNR,EAAE,CAAA2C,QAAA,CA8X8N3C,EAAE,CAAC4C,MAAM,GA9XzO5C,EAAE,CAAA2C,QAAA,CA8XoPpC,SAAS,GA9X/PP,EAAE,CAAA2C,QAAA,CA8X0Q/B,eAAe;AAAA;AAAAQ,eAAA,CAhFvW0H,mBAAmB,+BA9SyD9I,EAAE,CAAA6C,kBAAA;EAAAC,KAAA,EA+XwBgG,mBAAmB;EAAA/F,OAAA,EAAnB+F,mBAAmB,CAAA9F;AAAA;AAE/H;EAAA,QAAAjC,SAAA,oBAAAA,SAAA,KAjYkFf,EAAE,CAAAiD,iBAAA,CAiYQ6F,mBAAmB,EAAc,CAAC;IAClH5F,IAAI,EAAE7C;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE6C,IAAI,EAAElC;EAAa,CAAC,EAAE;IAAEkC,IAAI,EAAEgC;EAAiB,CAAC,EAAE;IAAEhC,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACnGF,IAAI,EAAE9C,MAAM;MACZiD,IAAI,EAAE,CAAC/C,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAE4C,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAE9C,MAAM;MACZiD,IAAI,EAAE,CAACoE,kCAAkC;IAC7C,CAAC;EAAE,CAAC,EAAE;IAAEvE,IAAI,EAAE8D,QAAQ;IAAE5D,UAAU,EAAE,CAAC;MACjCF,IAAI,EAAE9C,MAAM;MACZiD,IAAI,EAAE,CAACxD,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEqD,IAAI,EAAEuH,MAAM;IAAErH,UAAU,EAAE,CAAC;MAC/BF,IAAI,EAAE9C,MAAM;MACZiD,IAAI,EAAE,CAAC7C,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAE0C,IAAI,EAAElD,EAAE,CAAC4C;EAAO,CAAC,EAAE;IAAEM,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvDF,IAAI,EAAE9C,MAAM;MACZiD,IAAI,EAAE,CAAC9C,SAAS;IACpB,CAAC;EAAE,CAAC,EAAE;IAAE2C,IAAI,EAAElD,EAAE,CAACW,eAAe;IAAEyC,UAAU,EAAE,CAAC;MAC3CF,IAAI,EAAE9C,MAAM;MACZiD,IAAI,EAAE,CAACzC,eAAe;IAC1B,CAAC,EAAE;MACCsC,IAAI,EAAEzC;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,MAAM6I,mBAAmB,CAAC;EAYtBrI,WAAWA,CAAC8H,YAAY,EAAEjF,GAAG,EAAEoF,MAAM,EAAEE,gBAAgB,EAAED,cAAc,EAAE;IAAA/H,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,eANlEqJ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC1B;AACJ;AACA;AACA;IAHItJ,eAAA,gCAIwB,IAAI;IAAAA,eAAA,sBASd,IAAI;IAPd,IAAI,CAAC2H,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACjF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACoF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACD,cAAc,GAAGA,cAAc;EACxC;EACAwB,OAAOA,CAAA,EAAG,CAAE;EAEZ3G,aAAaA,CAAC4G,IAAI,EAAEC,SAAS,EAAE;IAC3B,IAAIA,SAAS,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,IAAI,CAAC/G,GAAG,CAACgH,eAAe,CAAC7D,cAAc,CAAC4D,SAAS,CAAC,IAAIA,SAAS,EAAED,IAAI,CAAC;IACjF;IACA,OAAO,IAAI,CAAC9G,GAAG,CAACE,aAAa,CAAC4G,IAAI,CAAC;EACvC;EACAG,aAAaA,CAACnF,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC9B,GAAG,CAACiH,aAAa,CAACnF,KAAK,CAAC;EACxC;EACAoF,UAAUA,CAACpF,KAAK,EAAE;IACd,OAAO,IAAI,CAAC9B,GAAG,CAACmH,cAAc,CAACrF,KAAK,CAAC;EACzC;EACAiB,WAAWA,CAACqE,MAAM,EAAEC,QAAQ,EAAE;IAC1B,MAAMC,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;IACrEE,YAAY,CAACvE,WAAW,CAACsE,QAAQ,CAAC;EACtC;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,IAAIN,MAAM,EAAE;MACR,MAAME,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;MACrEE,YAAY,CAACG,YAAY,CAACJ,QAAQ,EAAEK,QAAQ,CAAC;IACjD;EACJ;EACAC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC3BA,QAAQ,CAAChI,MAAM,CAAC,CAAC;EACrB;EACAiI,iBAAiBA,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,IAAIC,EAAE,GAAG,OAAOF,cAAc,KAAK,QAAQ,GAAG,IAAI,CAAC/H,GAAG,CAACkI,aAAa,CAACH,cAAc,CAAC,GAAGA,cAAc;IACrG,IAAI,CAACE,EAAE,EAAE;MACL,MAAM,IAAI5L,aAAa,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAOY,SAAS,KAAK,WAAW,IAAIA,SAAS,KACpH,iBAAiB8K,cAAc,8BAA8B,CAAC;IACtE;IACA,IAAI,CAACC,eAAe,EAAE;MAClBC,EAAE,CAAC9H,WAAW,GAAG,EAAE;IACvB;IACA,OAAO8H,EAAE;EACb;EACAE,UAAUA,CAACC,IAAI,EAAE;IACb,OAAOA,IAAI,CAACD,UAAU;EAC1B;EACAE,WAAWA,CAACD,IAAI,EAAE;IACd,OAAOA,IAAI,CAACC,WAAW;EAC3B;EACAlH,YAAYA,CAAC8G,EAAE,EAAEnB,IAAI,EAAEhF,KAAK,EAAEiF,SAAS,EAAE;IACrC,IAAIA,SAAS,EAAE;MACXD,IAAI,GAAGC,SAAS,GAAG,GAAG,GAAGD,IAAI;MAC7B,MAAMwB,YAAY,GAAGnF,cAAc,CAAC4D,SAAS,CAAC;MAC9C,IAAIuB,YAAY,EAAE;QACdL,EAAE,CAACM,cAAc,CAACD,YAAY,EAAExB,IAAI,EAAEhF,KAAK,CAAC;MAChD,CAAC,MACI;QACDmG,EAAE,CAAC9G,YAAY,CAAC2F,IAAI,EAAEhF,KAAK,CAAC;MAChC;IACJ,CAAC,MACI;MACDmG,EAAE,CAAC9G,YAAY,CAAC2F,IAAI,EAAEhF,KAAK,CAAC;IAChC;EACJ;EACAnB,eAAeA,CAACsH,EAAE,EAAEnB,IAAI,EAAEC,SAAS,EAAE;IACjC,IAAIA,SAAS,EAAE;MACX,MAAMuB,YAAY,GAAGnF,cAAc,CAAC4D,SAAS,CAAC;MAC9C,IAAIuB,YAAY,EAAE;QACdL,EAAE,CAACO,iBAAiB,CAACF,YAAY,EAAExB,IAAI,CAAC;MAC5C,CAAC,MACI;QACDmB,EAAE,CAACtH,eAAe,CAAC,GAAGoG,SAAS,IAAID,IAAI,EAAE,CAAC;MAC9C;IACJ,CAAC,MACI;MACDmB,EAAE,CAACtH,eAAe,CAACmG,IAAI,CAAC;IAC5B;EACJ;EACA2B,QAAQA,CAACR,EAAE,EAAEnB,IAAI,EAAE;IACfmB,EAAE,CAACS,SAAS,CAAChH,GAAG,CAACoF,IAAI,CAAC;EAC1B;EACA6B,WAAWA,CAACV,EAAE,EAAEnB,IAAI,EAAE;IAClBmB,EAAE,CAACS,SAAS,CAAC7I,MAAM,CAACiH,IAAI,CAAC;EAC7B;EACA8B,QAAQA,CAACX,EAAE,EAAElI,KAAK,EAAE+B,KAAK,EAAE+G,KAAK,EAAE;IAC9B,IAAIA,KAAK,IAAI9L,mBAAmB,CAAC+L,QAAQ,GAAG/L,mBAAmB,CAACgM,SAAS,CAAC,EAAE;MACxEd,EAAE,CAAClI,KAAK,CAACiJ,WAAW,CAACjJ,KAAK,EAAE+B,KAAK,EAAE+G,KAAK,GAAG9L,mBAAmB,CAACgM,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;IAChG,CAAC,MACI;MACDd,EAAE,CAAClI,KAAK,CAACA,KAAK,CAAC,GAAG+B,KAAK;IAC3B;EACJ;EACAmH,WAAWA,CAAChB,EAAE,EAAElI,KAAK,EAAE8I,KAAK,EAAE;IAC1B,IAAIA,KAAK,GAAG9L,mBAAmB,CAAC+L,QAAQ,EAAE;MACtC;MACAb,EAAE,CAAClI,KAAK,CAACmJ,cAAc,CAACnJ,KAAK,CAAC;IAClC,CAAC,MACI;MACDkI,EAAE,CAAClI,KAAK,CAACA,KAAK,CAAC,GAAG,EAAE;IACxB;EACJ;EACAiJ,WAAWA,CAACf,EAAE,EAAEnB,IAAI,EAAEhF,KAAK,EAAE;IACzB,IAAImG,EAAE,IAAI,IAAI,EAAE;MACZ;IACJ;IACA,CAAC,OAAOhL,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1C,IAAI,CAACkM,qBAAqB,IAC1BC,oBAAoB,CAACtC,IAAI,EAAE,UAAU,CAAC;IAC1CmB,EAAE,CAACnB,IAAI,CAAC,GAAGhF,KAAK;EACpB;EACAuH,QAAQA,CAACjB,IAAI,EAAEtG,KAAK,EAAE;IAClBsG,IAAI,CAACkB,SAAS,GAAGxH,KAAK;EAC1B;EACAyH,MAAMA,CAACC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAExL,OAAO,EAAE;IAAA,IAAAyL,oBAAA;IACrC,CAAC,OAAO1M,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1C,IAAI,CAACkM,qBAAqB,IAC1BC,oBAAoB,CAACK,KAAK,EAAE,UAAU,CAAC;IAC3C,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;MAC5BA,MAAM,GAAGvN,OAAO,CAAC,CAAC,CAAC2N,oBAAoB,CAAC,IAAI,CAAC5J,GAAG,EAAEwJ,MAAM,CAAC;MACzD,IAAI,CAACA,MAAM,EAAE;QACT,MAAM,IAAInN,aAAa,CAAC,IAAI,CAAC,iDAAiD,CAAC,OAAOY,SAAS,KAAK,WAAW,IAAIA,SAAS,KACxH,4BAA4BuM,MAAM,cAAcC,KAAK,EAAE,CAAC;MAChE;IACJ;IACA,IAAII,eAAe,GAAG,IAAI,CAACC,sBAAsB,CAACJ,QAAQ,CAAC;IAC3D,KAAAC,oBAAA,GAAI,IAAI,CAACtE,cAAc,cAAAsE,oBAAA,eAAnBA,oBAAA,CAAqBI,iBAAiB,EAAE;MACxCF,eAAe,GAAG,IAAI,CAACxE,cAAc,CAAC0E,iBAAiB,CAACP,MAAM,EAAEC,KAAK,EAAEI,eAAe,CAAC;IAC3F;IACA,OAAO,IAAI,CAAC5E,YAAY,CAACnH,gBAAgB,CAAC0L,MAAM,EAAEC,KAAK,EAAEI,eAAe,EAAE3L,OAAO,CAAC;EACtF;EACA4L,sBAAsBA,CAACE,YAAY,EAAE;IACjC;IACA;IACA;IACA;IACA,OAAQP,KAAK,IAAK;MACd;MACA;MACA;MACA;MACA;MACA,IAAIA,KAAK,KAAK,cAAc,EAAE;QAC1B,OAAOO,YAAY;MACvB;MACA;MACA;MACA,MAAMC,oBAAoB,GAAG,IAAI,CAAC3E,gBAAgB,GAC5C,IAAI,CAACF,MAAM,CAAC8E,UAAU,CAAC,MAAMF,YAAY,CAACP,KAAK,CAAC,CAAC,GACjDO,YAAY,CAACP,KAAK,CAAC;MACzB,IAAIQ,oBAAoB,KAAK,KAAK,EAAE;QAChCR,KAAK,CAACU,cAAc,CAAC,CAAC;MAC1B;MACA,OAAO9K,SAAS;IACpB,CAAC;EACL;AACJ;AACA,MAAM+K,WAAW,GAAG,CAAC,MAAM,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;AAC/C,SAASjB,oBAAoBA,CAACtC,IAAI,EAAEwD,QAAQ,EAAE;EAC1C,IAAIxD,IAAI,CAACuD,UAAU,CAAC,CAAC,CAAC,KAAKD,WAAW,EAAE;IACpC,MAAM,IAAI/N,aAAa,CAAC,IAAI,CAAC,sDAAsD,wBAAwBiO,QAAQ,IAAIxD,IAAI;AACnI;AACA,+DAA+DA,IAAI,iIAAiI,CAAC;EACjM;AACJ;AACA,SAASS,cAAcA,CAACa,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACmC,OAAO,KAAK,UAAU,IAAInC,IAAI,CAACZ,OAAO,KAAKnI,SAAS;AACpE;AACA,MAAMiH,iBAAiB,SAASd,mBAAmB,CAAC;EAIhDrI,WAAWA,CAAC8H,YAAY,EAAEC,gBAAgB,EAAEsF,MAAM,EAAEC,SAAS,EAAEzK,GAAG,EAAEoF,MAAM,EAAE/D,KAAK,EAAEiE,gBAAgB,EAAED,cAAc,EAAE;IAAA,IAAAqF,qBAAA;IACjH,KAAK,CAACzF,YAAY,EAAEjF,GAAG,EAAEoF,MAAM,EAAEE,gBAAgB,EAAED,cAAc,CAAC;IAAC/H,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACnE,IAAI,CAAC4H,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACsF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,UAAU,GAAGH,MAAM,CAACI,YAAY,CAAC;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC;IACvD,IAAI,CAAC3F,gBAAgB,CAACvC,OAAO,CAAC,IAAI,CAACgI,UAAU,CAAC;IAC9C,IAAI/I,MAAM,GAAG6I,SAAS,CAAC7I,MAAM;IAC7B,IAAI3E,SAAS,EAAE;MAAA,IAAA6N,mBAAA;MACX;MACA,MAAMzG,QAAQ,IAAAyG,mBAAA,GAAG7O,OAAO,CAAC,CAAC,CAAC8O,WAAW,CAAC/K,GAAG,CAAC,cAAA8K,mBAAA,cAAAA,mBAAA,GAAI,EAAE;MACjDlJ,MAAM,GAAGwC,yBAAyB,CAACC,QAAQ,EAAEzC,MAAM,CAAC;IACxD;IACAA,MAAM,GAAGqC,iBAAiB,CAACwG,SAAS,CAACpE,EAAE,EAAEzE,MAAM,CAAC;IAChD,KAAK,MAAM7B,KAAK,IAAI6B,MAAM,EAAE;MACxB,MAAMoJ,OAAO,GAAGC,QAAQ,CAAC/K,aAAa,CAAC,OAAO,CAAC;MAC/C,IAAImB,KAAK,EAAE;QACP2J,OAAO,CAAC7J,YAAY,CAAC,OAAO,EAAEE,KAAK,CAAC;MACxC;MACA2J,OAAO,CAAC7K,WAAW,GAAGJ,KAAK;MAC3B,IAAI,CAAC4K,UAAU,CAAC5H,WAAW,CAACiI,OAAO,CAAC;IACxC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAME,SAAS,IAAAR,qBAAA,GAAGD,SAAS,CAACU,iBAAiB,cAAAT,qBAAA,uBAA3BA,qBAAA,CAAAU,IAAA,CAAAX,SAA8B,CAAC;IACjD,IAAIS,SAAS,EAAE;MACX,KAAK,MAAMG,QAAQ,IAAIH,SAAS,EAAE;QAC9B,MAAMI,MAAM,GAAGtK,iBAAiB,CAACqK,QAAQ,EAAErL,GAAG,CAAC;QAC/C,IAAIqB,KAAK,EAAE;UACPiK,MAAM,CAACnK,YAAY,CAAC,OAAO,EAAEE,KAAK,CAAC;QACvC;QACA,IAAI,CAACsJ,UAAU,CAAC5H,WAAW,CAACuI,MAAM,CAAC;MACvC;IACJ;EACJ;EACAC,gBAAgBA,CAACnD,IAAI,EAAE;IACnB,OAAOA,IAAI,KAAK,IAAI,CAACoC,MAAM,GAAG,IAAI,CAACG,UAAU,GAAGvC,IAAI;EACxD;EACArF,WAAWA,CAACqE,MAAM,EAAEC,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAACtE,WAAW,CAAC,IAAI,CAACwI,gBAAgB,CAACnE,MAAM,CAAC,EAAEC,QAAQ,CAAC;EACrE;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,OAAO,KAAK,CAACD,YAAY,CAAC,IAAI,CAAC8D,gBAAgB,CAACnE,MAAM,CAAC,EAAEC,QAAQ,EAAEK,QAAQ,CAAC;EAChF;EACAC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC3B,OAAO,KAAK,CAACF,WAAW,CAAC,IAAI,EAAEE,QAAQ,CAAC;EAC5C;EACAM,UAAUA,CAACC,IAAI,EAAE;IACb,OAAO,IAAI,CAACmD,gBAAgB,CAAC,KAAK,CAACpD,UAAU,CAAC,IAAI,CAACoD,gBAAgB,CAACnD,IAAI,CAAC,CAAC,CAAC;EAC/E;EACAvB,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC3B,gBAAgB,CAACpC,UAAU,CAAC,IAAI,CAAC6H,UAAU,CAAC;EACrD;AACJ;AACA,MAAMzE,4BAA4B,SAASV,mBAAmB,CAAC;EAK3DrI,WAAWA,CAAC8H,YAAY,EAAEC,gBAAgB,EAAEuF,SAAS,EAAEtF,yBAAyB,EAAEnF,GAAG,EAAEoF,MAAM,EAAEE,gBAAgB,EAAED,cAAc,EAAEnB,MAAM,EAAE;IAAA,IAAAsH,sBAAA;IACrI,KAAK,CAACvG,YAAY,EAAEjF,GAAG,EAAEoF,MAAM,EAAEE,gBAAgB,EAAED,cAAc,CAAC;IAAC/H,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACnE,IAAI,CAAC4H,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAIvD,MAAM,GAAG6I,SAAS,CAAC7I,MAAM;IAC7B,IAAI3E,SAAS,EAAE;MAAA,IAAAwO,oBAAA;MACX;MACA,MAAMpH,QAAQ,IAAAoH,oBAAA,GAAGxP,OAAO,CAAC,CAAC,CAAC8O,WAAW,CAAC/K,GAAG,CAAC,cAAAyL,oBAAA,cAAAA,oBAAA,GAAI,EAAE;MACjD7J,MAAM,GAAGwC,yBAAyB,CAACC,QAAQ,EAAEzC,MAAM,CAAC;IACxD;IACA,IAAI,CAACA,MAAM,GAAGsC,MAAM,GAAGD,iBAAiB,CAACC,MAAM,EAAEtC,MAAM,CAAC,GAAGA,MAAM;IACjE,IAAI,CAACsJ,SAAS,IAAAM,sBAAA,GAAGf,SAAS,CAACU,iBAAiB,cAAAK,sBAAA,uBAA3BA,sBAAA,CAAAJ,IAAA,CAAAX,SAAS,EAAqBvG,MAAM,CAAC;EAC1D;EACAiC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjB,gBAAgB,CAACvD,SAAS,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACsJ,SAAS,CAAC;EAChE;EACArE,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAAC1B,yBAAyB,EAAE;MACjC;IACJ;IACA,IAAI,CAACD,gBAAgB,CAAClD,YAAY,CAAC,IAAI,CAACJ,MAAM,EAAE,IAAI,CAACsJ,SAAS,CAAC;EACnE;AACJ;AACA,MAAMlF,iCAAiC,SAASE,4BAA4B,CAAC;EAGzE/I,WAAWA,CAAC8H,YAAY,EAAEC,gBAAgB,EAAEuF,SAAS,EAAEpK,KAAK,EAAE8E,yBAAyB,EAAEnF,GAAG,EAAEoF,MAAM,EAAEE,gBAAgB,EAAED,cAAc,EAAE;IACpI,MAAMnB,MAAM,GAAG7D,KAAK,GAAG,GAAG,GAAGoK,SAAS,CAACpE,EAAE;IACzC,KAAK,CAACpB,YAAY,EAAEC,gBAAgB,EAAEuF,SAAS,EAAEtF,yBAAyB,EAAEnF,GAAG,EAAEoF,MAAM,EAAEE,gBAAgB,EAAED,cAAc,EAAEnB,MAAM,CAAC;IAAC5G,eAAA;IAAAA,eAAA;IACnI,IAAI,CAACoO,WAAW,GAAG7H,oBAAoB,CAACK,MAAM,CAAC;IAC/C,IAAI,CAACyH,QAAQ,GAAG3H,iBAAiB,CAACE,MAAM,CAAC;EAC7C;EACA+B,WAAWA,CAAClI,OAAO,EAAE;IACjB,IAAI,CAACoI,WAAW,CAAC,CAAC;IAClB,IAAI,CAAChF,YAAY,CAACpD,OAAO,EAAE,IAAI,CAAC4N,QAAQ,EAAE,EAAE,CAAC;EACjD;EACAzL,aAAaA,CAACkH,MAAM,EAAEN,IAAI,EAAE;IACxB,MAAMmB,EAAE,GAAG,KAAK,CAAC/H,aAAa,CAACkH,MAAM,EAAEN,IAAI,CAAC;IAC5C,KAAK,CAAC3F,YAAY,CAAC8G,EAAE,EAAE,IAAI,CAACyD,WAAW,EAAE,EAAE,CAAC;IAC5C,OAAOzD,EAAE;EACb;AACJ;AAEA,SAASjD,mBAAmB,EAAEhI,qBAAqB,EAAEE,YAAY,EAAEsC,kBAAkB,EAAEmE,kCAAkC,EAAEvC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}