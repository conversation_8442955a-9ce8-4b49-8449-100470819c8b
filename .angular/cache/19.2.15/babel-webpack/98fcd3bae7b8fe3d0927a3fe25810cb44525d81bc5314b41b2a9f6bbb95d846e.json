{"ast": null, "code": "var _SwuiSnackbarComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-snackbar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-snackbar.component.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_SNACK_BAR_DATA, MatSnackBarRef } from '@angular/material/snack-bar';\nlet SwuiSnackbarComponent = (_SwuiSnackbarComponent = class SwuiSnackbarComponent {\n  constructor(snackbar, data) {\n    this.snackbar = snackbar;\n    this.data = data;\n  }\n  ngOnDestroy() {\n    this.close();\n  }\n  close() {\n    if (this.snackbar) {\n      this.snackbar.dismiss();\n    }\n  }\n}, _SwuiSnackbarComponent.ctorParameters = () => [{\n  type: MatSnackBarRef\n}, {\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [MAT_SNACK_BAR_DATA]\n  }]\n}], _SwuiSnackbarComponent);\nSwuiSnackbarComponent = __decorate([Component({\n  selector: 'lib-swui-snackbar',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSnackbarComponent);\nexport { SwuiSnackbarComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "MAT_SNACK_BAR_DATA", "MatSnackBarRef", "SwuiSnackbarComponent", "_SwuiSnackbarComponent", "constructor", "snackbar", "data", "ngOnDestroy", "close", "dismiss", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-notifications/swui-snackbar/swui-snackbar.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-snackbar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-snackbar.component.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_SNACK_BAR_DATA, MatSnackBarRef } from '@angular/material/snack-bar';\nlet SwuiSnackbarComponent = class SwuiSnackbarComponent {\n    constructor(snackbar, data) {\n        this.snackbar = snackbar;\n        this.data = data;\n    }\n    ngOnDestroy() {\n        this.close();\n    }\n    close() {\n        if (this.snackbar) {\n            this.snackbar.dismiss();\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: MatSnackBarRef },\n        { type: undefined, decorators: [{ type: Inject, args: [MAT_SNACK_BAR_DATA,] }] }\n    ]; }\n};\nSwuiSnackbarComponent = __decorate([\n    Component({\n        selector: 'lib-swui-snackbar',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiSnackbarComponent);\nexport { SwuiSnackbarComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,6BAA6B;AAChF,IAAIC,qBAAqB,IAAAC,sBAAA,GAAG,MAAMD,qBAAqB,CAAC;EACpDE,WAAWA,CAACC,QAAQ,EAAEC,IAAI,EAAE;IACxB,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACH,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACI,OAAO,CAAC,CAAC;IAC3B;EACJ;AAKJ,CAAC,EAJYN,sBAAA,CAAKO,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEV;AAAe,CAAC,EACxB;EAAEU,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEZ,MAAM;IAAEe,IAAI,EAAE,CAACd,kBAAkB;EAAG,CAAC;AAAE,CAAC,CACnF,EAAAG,sBAAA,CACJ;AACDD,qBAAqB,GAAGP,UAAU,CAAC,CAC/BG,SAAS,CAAC;EACNiB,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAEpB,oBAAoB;EAC9BqB,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACrB,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEK,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}