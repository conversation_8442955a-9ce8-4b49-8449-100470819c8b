{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { SwuiInputSequenceMapComponent } from './swui-input-sequence-map.component';\ndescribe('SwuiInputSequenceMapComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      declarations: [SwuiInputSequenceMapComponent],\n      imports: [ReactiveFormsModule, MatFormFieldModule, MatSelectModule, MatButtonModule, MatIconModule, NoopAnimationsModule]\n    }).compileComponents();\n    fixture = TestBed.createComponent(SwuiInputSequenceMapComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should initialize with empty form array when no value', () => {\n    component.writeValue({});\n    expect(component.formArray.length).toBe(1);\n  });\n  it('should initialize with existing values', () => {\n    var _component$getSourceC, _component$getTargetC, _component$getSourceC2, _component$getTargetC2;\n    const initialValue = {\n      'USD': 'US Dollar',\n      'EUR': 'Euro'\n    };\n    component.writeValue(initialValue);\n    expect(component.formArray.length).toBe(2);\n    expect((_component$getSourceC = component.getSourceControl(0)) === null || _component$getSourceC === void 0 ? void 0 : _component$getSourceC.value).toBe('USD');\n    expect((_component$getTargetC = component.getTargetControl(0)) === null || _component$getTargetC === void 0 ? void 0 : _component$getTargetC.value).toBe('US Dollar');\n    expect((_component$getSourceC2 = component.getSourceControl(1)) === null || _component$getSourceC2 === void 0 ? void 0 : _component$getSourceC2.value).toBe('EUR');\n    expect((_component$getTargetC2 = component.getTargetControl(1)) === null || _component$getTargetC2 === void 0 ? void 0 : _component$getTargetC2.value).toBe('Euro');\n  });\n  it('should add pair', () => {\n    component.writeValue({});\n    const initialLength = component.formArray.length;\n    component.addPair('USD', 'US Dollar');\n    expect(component.formArray.length).toBe(initialLength + 1);\n  });\n  it('should remove pair', () => {\n    component.writeValue({});\n    component.addPair('USD', 'US Dollar');\n    component.addPair('EUR', 'Euro');\n    const initialLength = component.formArray.length;\n    component.removePair(0);\n    expect(component.formArray.length).toBe(initialLength - 1);\n  });\n  it('should not remove last pair', () => {\n    component.writeValue({});\n    expect(component.formArray.length).toBe(1);\n    component.removePair(0);\n    expect(component.formArray.length).toBe(1); // Should still have one pair\n  });\n  it('should call onChange when form value changes', () => {\n    const onChangeSpy = jasmine.createSpy('onChange');\n    component.registerOnChange(onChangeSpy);\n    component.writeValue({});\n    component.addPair('USD', 'US Dollar');\n    // Trigger form change\n    component.getSourceControl(0).setValue('EUR');\n    expect(onChangeSpy).toHaveBeenCalled();\n  });\n  it('should disable form when setDisabledState is called', () => {\n    component.writeValue({});\n    component.setDisabledState(true);\n    expect(component.readonly).toBe(true);\n    expect(component.formArray.disabled).toBe(true);\n  });\n  it('should enable form when setDisabledState is called with false', () => {\n    component.writeValue({});\n    component.setDisabledState(false);\n    expect(component.readonly).toBe(false);\n    expect(component.formArray.enabled).toBe(true);\n  });\n});", "map": {"version": 3, "names": ["TestBed", "ReactiveFormsModule", "MatFormFieldModule", "MatSelectModule", "MatButtonModule", "MatIconModule", "NoopAnimationsModule", "SwuiInputSequenceMapComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "declarations", "imports", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy", "writeValue", "formArray", "length", "toBe", "_component$getSourceC", "_component$getTargetC", "_component$getSourceC2", "_component$getTargetC2", "initialValue", "getSourceControl", "value", "getTargetControl", "initialLength", "addPair", "removePair", "onChangeSpy", "jasmine", "createSpy", "registerOnChange", "setValue", "toHaveBeenCalled", "setDisabledState", "readonly", "disabled", "enabled"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-input-sequence-map/swui-input-sequence-map.component.spec.ts"], "sourcesContent": ["import { ComponentFix<PERSON>, TestBed } from '@angular/core/testing';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\n\nimport { SwuiInputSequenceMapComponent } from './swui-input-sequence-map.component';\n\ndescribe('SwuiInputSequenceMapComponent', () => {\n  let component: SwuiInputSequenceMapComponent;\n  let fixture: ComponentFixture<SwuiInputSequenceMapComponent>;\n\n  beforeEach(async () => {\n    await TestBed.configureTestingModule({\n      declarations: [SwuiInputSequenceMapComponent],\n      imports: [\n        ReactiveFormsModule,\n        MatFormFieldModule,\n        MatSelectModule,\n        MatButtonModule,\n        MatIconModule,\n        NoopAnimationsModule\n      ]\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(SwuiInputSequenceMapComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should initialize with empty form array when no value', () => {\n    component.writeValue({});\n    expect(component.formArray.length).toBe(1);\n  });\n\n  it('should initialize with existing values', () => {\n    const initialValue = {\n      'USD': 'US Dollar',\n      'EUR': 'Euro'\n    };\n    component.writeValue(initialValue);\n    expect(component.formArray.length).toBe(2);\n    expect(component.getSourceControl(0)?.value).toBe('USD');\n    expect(component.getTargetControl(0)?.value).toBe('US Dollar');\n    expect(component.getSourceControl(1)?.value).toBe('EUR');\n    expect(component.getTargetControl(1)?.value).toBe('Euro');\n  });\n\n  it('should add pair', () => {\n    component.writeValue({});\n    const initialLength = component.formArray.length;\n    component.addPair('USD', 'US Dollar');\n    expect(component.formArray.length).toBe(initialLength + 1);\n  });\n\n  it('should remove pair', () => {\n    component.writeValue({});\n    component.addPair('USD', 'US Dollar');\n    component.addPair('EUR', 'Euro');\n    const initialLength = component.formArray.length;\n    component.removePair(0);\n    expect(component.formArray.length).toBe(initialLength - 1);\n  });\n\n  it('should not remove last pair', () => {\n    component.writeValue({});\n    expect(component.formArray.length).toBe(1);\n    component.removePair(0);\n    expect(component.formArray.length).toBe(1); // Should still have one pair\n  });\n\n  it('should call onChange when form value changes', () => {\n    const onChangeSpy = jasmine.createSpy('onChange');\n    component.registerOnChange(onChangeSpy);\n\n    component.writeValue({});\n    component.addPair('USD', 'US Dollar');\n\n    // Trigger form change\n    component.getSourceControl(0).setValue('EUR');\n\n    expect(onChangeSpy).toHaveBeenCalled();\n  });\n\n  it('should disable form when setDisabledState is called', () => {\n    component.writeValue({});\n    component.setDisabledState(true);\n\n    expect(component.readonly).toBe(true);\n    expect(component.formArray.disabled).toBe(true);\n  });\n\n  it('should enable form when setDisabledState is called with false', () => {\n    component.writeValue({});\n    component.setDisabledState(false);\n\n    expect(component.readonly).toBe(false);\n    expect(component.formArray.enabled).toBe(true);\n  });\n});\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,oBAAoB,QAAQ,sCAAsC;AAE3E,SAASC,6BAA6B,QAAQ,qCAAqC;AAEnFC,QAAQ,CAAC,+BAA+B,EAAE,MAAK;EAC7C,IAAIC,SAAwC;EAC5C,IAAIC,OAAwD;EAE5DC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMZ,OAAO,CAACa,sBAAsB,CAAC;MACnCC,YAAY,EAAE,CAACP,6BAA6B,CAAC;MAC7CQ,OAAO,EAAE,CACPd,mBAAmB,EACnBC,kBAAkB,EAClBC,eAAe,EACfC,eAAe,EACfC,aAAa,EACbC,oBAAoB;KAEvB,CAAC,CAACU,iBAAiB,EAAE;IAEtBN,OAAO,GAAGV,OAAO,CAACiB,eAAe,CAACV,6BAA6B,CAAC;IAChEE,SAAS,GAAGC,OAAO,CAACQ,iBAAiB;IACrCR,OAAO,CAACS,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACZ,SAAS,CAAC,CAACa,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,uDAAuD,EAAE,MAAK;IAC/DX,SAAS,CAACc,UAAU,CAAC,EAAE,CAAC;IACxBF,MAAM,CAACZ,SAAS,CAACe,SAAS,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC;EAEFN,EAAE,CAAC,wCAAwC,EAAE,MAAK;IAAA,IAAAO,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAChD,MAAMC,YAAY,GAAG;MACnB,KAAK,EAAE,WAAW;MAClB,KAAK,EAAE;KACR;IACDtB,SAAS,CAACc,UAAU,CAACQ,YAAY,CAAC;IAClCV,MAAM,CAACZ,SAAS,CAACe,SAAS,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IAC1CL,MAAM,EAAAM,qBAAA,GAAClB,SAAS,CAACuB,gBAAgB,CAAC,CAAC,CAAC,cAAAL,qBAAA,uBAA7BA,qBAAA,CAA+BM,KAAK,CAAC,CAACP,IAAI,CAAC,KAAK,CAAC;IACxDL,MAAM,EAAAO,qBAAA,GAACnB,SAAS,CAACyB,gBAAgB,CAAC,CAAC,CAAC,cAAAN,qBAAA,uBAA7BA,qBAAA,CAA+BK,KAAK,CAAC,CAACP,IAAI,CAAC,WAAW,CAAC;IAC9DL,MAAM,EAAAQ,sBAAA,GAACpB,SAAS,CAACuB,gBAAgB,CAAC,CAAC,CAAC,cAAAH,sBAAA,uBAA7BA,sBAAA,CAA+BI,KAAK,CAAC,CAACP,IAAI,CAAC,KAAK,CAAC;IACxDL,MAAM,EAAAS,sBAAA,GAACrB,SAAS,CAACyB,gBAAgB,CAAC,CAAC,CAAC,cAAAJ,sBAAA,uBAA7BA,sBAAA,CAA+BG,KAAK,CAAC,CAACP,IAAI,CAAC,MAAM,CAAC;EAC3D,CAAC,CAAC;EAEFN,EAAE,CAAC,iBAAiB,EAAE,MAAK;IACzBX,SAAS,CAACc,UAAU,CAAC,EAAE,CAAC;IACxB,MAAMY,aAAa,GAAG1B,SAAS,CAACe,SAAS,CAACC,MAAM;IAChDhB,SAAS,CAAC2B,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC;IACrCf,MAAM,CAACZ,SAAS,CAACe,SAAS,CAACC,MAAM,CAAC,CAACC,IAAI,CAACS,aAAa,GAAG,CAAC,CAAC;EAC5D,CAAC,CAAC;EAEFf,EAAE,CAAC,oBAAoB,EAAE,MAAK;IAC5BX,SAAS,CAACc,UAAU,CAAC,EAAE,CAAC;IACxBd,SAAS,CAAC2B,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC;IACrC3B,SAAS,CAAC2B,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;IAChC,MAAMD,aAAa,GAAG1B,SAAS,CAACe,SAAS,CAACC,MAAM;IAChDhB,SAAS,CAAC4B,UAAU,CAAC,CAAC,CAAC;IACvBhB,MAAM,CAACZ,SAAS,CAACe,SAAS,CAACC,MAAM,CAAC,CAACC,IAAI,CAACS,aAAa,GAAG,CAAC,CAAC;EAC5D,CAAC,CAAC;EAEFf,EAAE,CAAC,6BAA6B,EAAE,MAAK;IACrCX,SAAS,CAACc,UAAU,CAAC,EAAE,CAAC;IACxBF,MAAM,CAACZ,SAAS,CAACe,SAAS,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IAC1CjB,SAAS,CAAC4B,UAAU,CAAC,CAAC,CAAC;IACvBhB,MAAM,CAACZ,SAAS,CAACe,SAAS,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC,CAAC;EAEFN,EAAE,CAAC,8CAA8C,EAAE,MAAK;IACtD,MAAMkB,WAAW,GAAGC,OAAO,CAACC,SAAS,CAAC,UAAU,CAAC;IACjD/B,SAAS,CAACgC,gBAAgB,CAACH,WAAW,CAAC;IAEvC7B,SAAS,CAACc,UAAU,CAAC,EAAE,CAAC;IACxBd,SAAS,CAAC2B,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC;IAErC;IACA3B,SAAS,CAACuB,gBAAgB,CAAC,CAAC,CAAC,CAACU,QAAQ,CAAC,KAAK,CAAC;IAE7CrB,MAAM,CAACiB,WAAW,CAAC,CAACK,gBAAgB,EAAE;EACxC,CAAC,CAAC;EAEFvB,EAAE,CAAC,qDAAqD,EAAE,MAAK;IAC7DX,SAAS,CAACc,UAAU,CAAC,EAAE,CAAC;IACxBd,SAAS,CAACmC,gBAAgB,CAAC,IAAI,CAAC;IAEhCvB,MAAM,CAACZ,SAAS,CAACoC,QAAQ,CAAC,CAACnB,IAAI,CAAC,IAAI,CAAC;IACrCL,MAAM,CAACZ,SAAS,CAACe,SAAS,CAACsB,QAAQ,CAAC,CAACpB,IAAI,CAAC,IAAI,CAAC;EACjD,CAAC,CAAC;EAEFN,EAAE,CAAC,+DAA+D,EAAE,MAAK;IACvEX,SAAS,CAACc,UAAU,CAAC,EAAE,CAAC;IACxBd,SAAS,CAACmC,gBAAgB,CAAC,KAAK,CAAC;IAEjCvB,MAAM,CAACZ,SAAS,CAACoC,QAAQ,CAAC,CAACnB,IAAI,CAAC,KAAK,CAAC;IACtCL,MAAM,CAACZ,SAAS,CAACe,SAAS,CAACuB,OAAO,CAAC,CAACrB,IAAI,CAAC,IAAI,CAAC;EAChD,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}