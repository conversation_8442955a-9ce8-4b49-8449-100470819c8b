{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiNumericRangeComponent } from './swui-numeric-range.component';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiControlMessagesModule } from '../swui-control-messages/swui-control-messages.module';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nlet SwuiNumericRangeModule = class SwuiNumericRangeModule {};\nSwuiNumericRangeModule = __decorate([NgModule({\n  imports: [CommonModule, BrowserAnimationsModule, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatIconModule, MatButtonModule, SwuiControlMessagesModule],\n  declarations: [SwuiNumericRangeComponent],\n  exports: [SwuiNumericRangeComponent]\n})], SwuiNumericRangeModule);\nexport { SwuiNumericRangeModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "SwuiNumericRangeComponent", "BrowserAnimationsModule", "ReactiveFormsModule", "SwuiControlMessagesModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatButtonModule", "SwuiNumericRangeModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-numeric-range/swui-numeric-range.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiNumericRangeComponent } from './swui-numeric-range.component';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiControlMessagesModule } from '../swui-control-messages/swui-control-messages.module';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nlet SwuiNumericRangeModule = class SwuiNumericRangeModule {\n};\nSwuiNumericRangeModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            BrowserAnimationsModule,\n            ReactiveFormsModule,\n            MatFormFieldModule,\n            MatInputModule,\n            MatIconModule,\n            MatButtonModule,\n            SwuiControlMessagesModule\n        ],\n        declarations: [SwuiNumericRangeComponent],\n        exports: [SwuiNumericRangeComponent]\n    })\n], SwuiNumericRangeModule);\nexport { SwuiNumericRangeModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,yBAAyB,QAAQ,uDAAuD;AACjG,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,IAAIC,sBAAsB,GAAG,MAAMA,sBAAsB,CAAC,EACzD;AACDA,sBAAsB,GAAGX,UAAU,CAAC,CAChCC,QAAQ,CAAC;EACLW,OAAO,EAAE,CACLV,YAAY,EACZE,uBAAuB,EACvBC,mBAAmB,EACnBE,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbE,eAAe,EACfJ,yBAAyB,CAC5B;EACDO,YAAY,EAAE,CAACV,yBAAyB,CAAC;EACzCW,OAAO,EAAE,CAACX,yBAAyB;AACvC,CAAC,CAAC,CACL,EAAEQ,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}