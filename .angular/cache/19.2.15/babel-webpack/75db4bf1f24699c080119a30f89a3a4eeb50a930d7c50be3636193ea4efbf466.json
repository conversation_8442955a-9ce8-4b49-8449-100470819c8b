{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiAutoselectComponent } from './swui-autoselect.component';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nexport const MOULES_AUTOSELECT = [MatAutocompleteModule, MatInputModule, MatButtonModule, ReactiveFormsModule];\nlet SwuiAutoselectModule = class SwuiAutoselectModule {};\nSwuiAutoselectModule = __decorate([NgModule({\n  declarations: [SwuiAutoselectComponent],\n  exports: [SwuiAutoselectComponent],\n  imports: [CommonModule, ...MOULES_AUTOSELECT]\n})], SwuiAutoselectModule);\nexport { SwuiAutoselectModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "ReactiveFormsModule", "SwuiAutoselectComponent", "MatAutocompleteModule", "MatInputModule", "MatButtonModule", "MOULES_AUTOSELECT", "SwuiAutoselectModule", "__decorate", "declarations", "exports", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-autoselect/swui-autoselect.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiAutoselectComponent } from './swui-autoselect.component';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\n\n\nexport const MOULES_AUTOSELECT = [\n  MatAutocompleteModule,\n  MatInputModule,\n  MatButtonModule,\n  ReactiveFormsModule,\n];\n\n@NgModule({\n  declarations: [SwuiAutoselectComponent],\n  exports: [SwuiAutoselectComponent],\n  imports: [\n    CommonModule,\n    ...MOULES_AUTOSELECT,\n  ]\n})\nexport class SwuiAutoselectModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAG1D,OAAO,MAAMC,iBAAiB,GAAG,CAC/BH,qBAAqB,EACrBC,cAAc,EACdC,eAAe,EACfJ,mBAAmB,CACpB;AAUM,IAAMM,oBAAoB,GAA1B,MAAMA,oBAAoB,GAChC;AADYA,oBAAoB,GAAAC,UAAA,EARhCT,QAAQ,CAAC;EACRU,YAAY,EAAE,CAACP,uBAAuB,CAAC;EACvCQ,OAAO,EAAE,CAACR,uBAAuB,CAAC;EAClCS,OAAO,EAAE,CACPX,YAAY,EACZ,GAAGM,iBAAiB;CAEvB,CAAC,C,EACWC,oBAAoB,CAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}