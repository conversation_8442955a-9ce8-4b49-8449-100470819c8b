{"ast": null, "code": "var moment = module.exports = require(\"./moment-timezone\");\nmoment.tz.load(require('./data/packed/latest.json'));", "map": {"version": 3, "names": ["moment", "module", "exports", "require", "tz", "load"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/moment-timezone/index.js"], "sourcesContent": ["var moment = module.exports = require(\"./moment-timezone\");\nmoment.tz.load(require('./data/packed/latest.json'));\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAC1DH,MAAM,CAACI,EAAE,CAACC,IAAI,CAACF,OAAO,CAAC,2BAA2B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}