{"ast": null, "code": "var _SwuiMultiselectComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-multiselect.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-multiselect.component.scss?ngResource\";\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { TranslateService } from '@ngx-translate/core';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nconst CONTROL_NAME = 'lib-swui-multiselect';\nlet nextUniqueId = 0;\nlet SwuiMultiselectComponent = (_SwuiMultiselectComponent = class SwuiMultiselectComponent extends SwuiMatFormFieldControl {\n  set data(val) {\n    this._data = val && Array.isArray(val) ? val : [];\n  }\n  get data() {\n    return this._data;\n  }\n  get value() {\n    return this._value;\n  }\n  set value(val) {\n    this._value = val && Array.isArray(val) ? val : [];\n    this.setSelectValue(this._value);\n  }\n  get empty() {\n    return !this.value || !this.value.length;\n  }\n  get shouldLabelFloat() {\n    return this.selectControl.value;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, translate) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.translate = translate;\n    this.searchPlaceholder = 'COMPONENTS.MULTISELECT.search';\n    this.title = '';\n    this.showSearch = false;\n    this.disableAllOption = false;\n    this.selectControl = new UntypedFormControl();\n    this.controlType = CONTROL_NAME;\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._data = [];\n    this._value = [];\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.elRef && this.selectRef && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n      this.selectRef.openMenu();\n    }\n  }\n  writeValue(val) {\n    this._value = val && Array.isArray(val) ? val : [];\n    this.setSelectValue(this._value);\n  }\n  onCancel() {\n    if (this.selectRef) {\n      this.selectRef.closeMenu();\n    }\n  }\n  onApply(data) {\n    this.setSelectValue(data);\n    this._value = data;\n    this.onChange(data);\n    if (this.selectRef) {\n      this.selectRef.closeMenu();\n    }\n  }\n  onDisabledState(value) {\n    value ? this.selectControl.disable() : this.selectControl.enable();\n  }\n  isErrorState() {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n  setSelectValue(data) {\n    const multiMessage = this.translate.instant('COMPONENTS.MULTISELECT.multiple');\n    const singleItem = this.data.find(el => el.id === data[0]);\n    const selectValue = data.length > 1 ? `(${multiMessage})` : singleItem ? singleItem.text : '';\n    this.selectControl.setValue(selectValue);\n  }\n}, _SwuiMultiselectComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}, {\n  type: TranslateService\n}], _SwuiMultiselectComponent.propDecorators = {\n  searchPlaceholder: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  showSearch: [{\n    type: Input\n  }],\n  disableAllOption: [{\n    type: Input\n  }],\n  data: [{\n    type: Input\n  }],\n  value: [{\n    type: Input\n  }],\n  input: [{\n    type: ViewChild,\n    args: ['input']\n  }],\n  selectRef: [{\n    type: ViewChild,\n    args: [MatMenuTrigger, {\n      static: true\n    }]\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiMultiselectComponent);\nSwuiMultiselectComponent = __decorate([Component({\n  selector: 'lib-swui-multiselect',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiMultiselectComponent\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiMultiselectComponent);\nexport { SwuiMultiselectComponent };", "map": {"version": 3, "names": ["Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormControl", "FormGroupDirective", "NgControl", "FocusMonitor", "TranslateService", "MatMenuTrigger", "MatFormFieldControl", "SwuiMatFormFieldControl", "ErrorStateMatcher", "CONTROL_NAME", "nextUniqueId", "SwuiMultiselectComponent", "_SwuiMultiselectComponent", "data", "val", "_data", "Array", "isArray", "value", "_value", "setSelectValue", "empty", "length", "shouldLabelFloat", "selectControl", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "translate", "searchPlaceholder", "title", "showSearch", "disableAllOption", "controlType", "id", "onContainerClick", "event", "stopPropagation", "selectRef", "target", "tagName", "toLowerCase", "disabled", "nativeElement", "focus", "openMenu", "writeValue", "onCancel", "closeMenu", "onApply", "onChange", "onDisabledState", "disable", "enable", "isErrorState", "input", "errorState", "multiMessage", "instant", "singleItem", "find", "el", "selectValue", "text", "setValue", "type", "args", "static", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "providers", "provide", "useExisting", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-multiselect/swui-multiselect.component.ts"], "sourcesContent": ["import { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { TranslateService } from '@ngx-translate/core';\n\nimport { SwuiSelectOption } from '../swui-select/swui-select.interface';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\n\n\nconst CONTROL_NAME = 'lib-swui-multiselect';\nlet nextUniqueId = 0;\n\n@Component({\n    selector: 'lib-swui-multiselect',\n    templateUrl: './swui-multiselect.component.html',\n    styleUrls: ['./swui-multiselect.component.scss'],\n    providers: [{ provide: MatFormFieldControl, useExisting: SwuiMultiselectComponent }],\n    standalone: false\n})\nexport class SwuiMultiselectComponent extends SwuiMatFormFieldControl<string[] | undefined> {\n  @Input() searchPlaceholder = 'COMPONENTS.MULTISELECT.search';\n  @Input() title = '';\n  @Input() showSearch = false;\n  @Input() disableAllOption = false;\n\n  @Input()\n  set data( val: SwuiSelectOption[] ) {\n    this._data = val && Array.isArray(val) ? val : [];\n  }\n\n  get data(): SwuiSelectOption[] {\n    return this._data;\n  }\n\n  @Input()\n  get value(): string[] {\n    return this._value;\n  }\n\n  set value( val: string[] ) {\n    this._value = val && Array.isArray(val) ? val : [];\n    this.setSelectValue(this._value);\n  }\n\n  get empty() {\n    return !this.value || !this.value.length;\n  }\n\n  readonly selectControl = new UntypedFormControl();\n  readonly controlType = CONTROL_NAME;\n\n  @ViewChild('input') input?: MatInput;\n  @ViewChild(MatMenuTrigger, { static: true }) selectRef?: MatMenuTrigger;\n  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;\n\n  private _data: SwuiSelectOption[] = [];\n  private _value: string[] = [];\n\n  @HostBinding('class.floating')\n  get shouldLabelFloat() {\n    return this.selectControl.value;\n  }\n\n  constructor( fm: FocusMonitor,\n               elRef: ElementRef<HTMLElement>,\n               @Optional() @Self() ngControl: NgControl,\n               @Optional() parentFormGroup: FormGroupDirective,\n               errorStateMatcher: ErrorStateMatcher,\n               private readonly translate: TranslateService ) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n  }\n\n  onContainerClick( event: Event ): void {\n    event.stopPropagation();\n    if (this.elRef && this.selectRef && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n      this.selectRef.openMenu();\n    }\n  }\n\n  writeValue( val: string[] | undefined ): void {\n    this._value = val && Array.isArray(val) ? val : [];\n    this.setSelectValue(this._value);\n  }\n\n  onCancel() {\n    if (this.selectRef) {\n      this.selectRef.closeMenu();\n    }\n  }\n\n  onApply( data: string[] ) {\n    this.setSelectValue(data);\n    this._value = data;\n    this.onChange(data);\n    if (this.selectRef) {\n      this.selectRef.closeMenu();\n    }\n  }\n\n  protected onDisabledState( value: boolean ) {\n    value ? this.selectControl.disable() : this.selectControl.enable();\n  }\n\n  protected isErrorState(): boolean {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n\n  private setSelectValue( data: string[] ) {\n    const multiMessage = this.translate.instant('COMPONENTS.MULTISELECT.multiple');\n    const singleItem = this.data.find(( el: SwuiSelectOption ) => el.id === data[0]);\n    const selectValue = data.length > 1 ? `(${multiMessage})` : (singleItem ? singleItem.text : '');\n    this.selectControl.setValue(selectValue);\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AACpG,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAClF,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,gBAAgB,QAAQ,qBAAqB;AAGtD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,8BAA8B;AAElE,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAG1D,MAAMC,YAAY,GAAG,sBAAsB;AAC3C,IAAIC,YAAY,GAAG,CAAC;AASb,IAAMC,wBAAwB,IAAAC,yBAAA,GAA9B,MAAMD,wBAAyB,SAAQJ,uBAA6C;MAOrFM,IAAIA,CAAEC,GAAuB;IAC/B,IAAI,CAACC,KAAK,GAAGD,GAAG,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;EACnD;EAEA,IAAID,IAAIA,CAAA;IACN,OAAO,IAAI,CAACE,KAAK;EACnB;MAGIG,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,MAAM;EACpB;EAEA,IAAID,KAAKA,CAAEJ,GAAa;IACtB,IAAI,CAACK,MAAM,GAAGL,GAAG,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;IAClD,IAAI,CAACM,cAAc,CAAC,IAAI,CAACD,MAAM,CAAC;EAClC;EAEA,IAAIE,KAAKA,CAAA;IACP,OAAO,CAAC,IAAI,CAACH,KAAK,IAAI,CAAC,IAAI,CAACA,KAAK,CAACI,MAAM;EAC1C;MAaIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,aAAa,CAACN,KAAK;EACjC;EAEAO,YAAaC,EAAgB,EAChBC,KAA8B,EACVC,SAAoB,EAC5BC,eAAmC,EAC/CC,iBAAoC,EACnBC,SAA2B;IACvD,KAAK,CAACL,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IADnC,KAAAC,SAAS,GAATA,SAAS;IAhD9B,KAAAC,iBAAiB,GAAG,+BAA+B;IACnD,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAG,KAAK;IAyBxB,KAAAX,aAAa,GAAG,IAAIxB,kBAAkB,EAAE;IACxC,KAAAoC,WAAW,GAAG3B,YAAY;IAIX,KAAA4B,EAAE,GAAG,GAAG5B,YAAY,IAAIC,YAAY,EAAE,EAAE;IAExD,KAAAK,KAAK,GAAuB,EAAE;IAC9B,KAAAI,MAAM,GAAa,EAAE;EAc7B;EAEAmB,gBAAgBA,CAAEC,KAAY;IAC5BA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,IAAI,CAACb,KAAK,IAAI,IAAI,CAACc,SAAS,IAAKF,KAAK,CAACG,MAAkB,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MACjH,IAAI,CAAClB,KAAK,CAACmB,aAAa,CAACC,KAAK,EAAE;MAChC,IAAI,CAACN,SAAS,CAACO,QAAQ,EAAE;IAC3B;EACF;EAEAC,UAAUA,CAAEnC,GAAyB;IACnC,IAAI,CAACK,MAAM,GAAGL,GAAG,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;IAClD,IAAI,CAACM,cAAc,CAAC,IAAI,CAACD,MAAM,CAAC;EAClC;EAEA+B,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACU,SAAS,EAAE;IAC5B;EACF;EAEAC,OAAOA,CAAEvC,IAAc;IACrB,IAAI,CAACO,cAAc,CAACP,IAAI,CAAC;IACzB,IAAI,CAACM,MAAM,GAAGN,IAAI;IAClB,IAAI,CAACwC,QAAQ,CAACxC,IAAI,CAAC;IACnB,IAAI,IAAI,CAAC4B,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACU,SAAS,EAAE;IAC5B;EACF;EAEUG,eAAeA,CAAEpC,KAAc;IACvCA,KAAK,GAAG,IAAI,CAACM,aAAa,CAAC+B,OAAO,EAAE,GAAG,IAAI,CAAC/B,aAAa,CAACgC,MAAM,EAAE;EACpE;EAEUC,YAAYA,CAAA;IACpB,IAAI,IAAI,CAACC,KAAK,EAAE;MACd,OAAO,IAAI,CAACA,KAAK,CAACC,UAAU;IAC9B;IACA,OAAO,KAAK;EACd;EAEQvC,cAAcA,CAAEP,IAAc;IACpC,MAAM+C,YAAY,GAAG,IAAI,CAAC7B,SAAS,CAAC8B,OAAO,CAAC,iCAAiC,CAAC;IAC9E,MAAMC,UAAU,GAAG,IAAI,CAACjD,IAAI,CAACkD,IAAI,CAAGC,EAAoB,IAAMA,EAAE,CAAC3B,EAAE,KAAKxB,IAAI,CAAC,CAAC,CAAC,CAAC;IAChF,MAAMoD,WAAW,GAAGpD,IAAI,CAACS,MAAM,GAAG,CAAC,GAAG,IAAIsC,YAAY,GAAG,GAAIE,UAAU,GAAGA,UAAU,CAACI,IAAI,GAAG,EAAG;IAC/F,IAAI,CAAC1C,aAAa,CAAC2C,QAAQ,CAACF,WAAW,CAAC;EAC1C;;;;;;;;UAnDcpE;EAAQ;IAAAuE,IAAA,EAAItE;EAAI;AAAA,G;;;UAChBD;EAAQ;AAAA,G;;;;;;UA9CrBD;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UASLA;EAAK;;UAiBLG,SAAS;IAAAsE,IAAA,GAAC,OAAO;EAAA;;UACjBtE,SAAS;IAAAsE,IAAA,GAAChE,cAAc,EAAE;MAAEiE,MAAM,EAAE;IAAI,CAAE;EAAA;;UAC1C3E;EAAW;;UAKXA,WAAW;IAAA0E,IAAA,GAAC,gBAAgB;EAAA;;AAvClB1D,wBAAwB,GAAA4D,UAAA,EAPpC9E,SAAS,CAAC;EACP+E,QAAQ,EAAE,sBAAsB;EAChCC,QAAA,EAAAC,oBAAgD;EAEhDC,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAEtE,mBAAmB;IAAEuE,WAAW,EAAElE;EAAwB,CAAE,CAAC;EACpFmE,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWnE,wBAAwB,CAkGpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}