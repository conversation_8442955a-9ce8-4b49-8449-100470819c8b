{"ast": null, "code": "var SwDexieModule_1;\nimport { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { DEXI_CONFIG, SwDexieService } from './sw-dexie.service';\nlet SwDexieModule = SwDexieModule_1 = class SwDexieModule {\n  static forRoot(hubName) {\n    return {\n      ngModule: SwDexieModule_1,\n      providers: [{\n        provide: DEXI_CONFIG,\n        useValue: hubName\n      }, SwDexieService]\n    };\n  }\n};\nSwDexieModule = SwDexieModule_1 = __decorate([NgModule()], SwDexieModule);\nexport { SwDexieModule };", "map": {"version": 3, "names": ["NgModule", "DEXI_CONFIG", "SwDexieService", "SwDexieModule", "SwDexieModule_1", "forRoot", "hubName", "ngModule", "providers", "provide", "useValue", "__decorate"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-dexie/sw-dexie.module.ts"], "sourcesContent": ["import { ModuleWithProviders, NgModule } from '@angular/core';\nimport { DEXI_CONFIG, SwDexieService } from './sw-dexie.service';\n\n@NgModule()\nexport class SwDexieModule {\n  static forRoot( hubName: string ): ModuleWithProviders<SwDexieModule> {\n    return {\n      ngModule: SwDexieModule,\n      providers: [\n        { provide: DEXI_CONFIG, useValue: hubName },\n        SwDexieService,\n      ]\n    };\n  }\n}\n"], "mappings": ";;AAAA,SAA8BA,QAAQ,QAAQ,eAAe;AAC7D,SAASC,WAAW,EAAEC,cAAc,QAAQ,oBAAoB;AAGzD,IAAMC,aAAa,GAAAC,eAAA,GAAnB,MAAMD,aAAa;EACxB,OAAOE,OAAOA,CAAEC,OAAe;IAC7B,OAAO;MACLC,QAAQ,EAAEH,eAAa;MACvBI,SAAS,EAAE,CACT;QAAEC,OAAO,EAAER,WAAW;QAAES,QAAQ,EAAEJ;MAAO,CAAE,EAC3CJ,cAAc;KAEjB;EACH;CACD;AAVYC,aAAa,GAAAC,eAAA,GAAAO,UAAA,EADzBX,QAAQ,EAAE,C,EACEG,aAAa,CAUzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}