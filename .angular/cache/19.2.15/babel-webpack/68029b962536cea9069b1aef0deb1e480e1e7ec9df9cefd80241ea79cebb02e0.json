{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { SwuiPagePanelComponent } from './swui-page-panel.component';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { RouterModule } from '@angular/router';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { ActionConfirmDialogComponent } from './action-confirm-dialog/action-confirm-dialog.component';\nlet SwuiPagePanelModule = class SwuiPagePanelModule {};\nSwuiPagePanelModule = __decorate([NgModule({\n  imports: [CommonModule, RouterModule, TranslateModule.forChild(), MatCardModule, MatButtonModule, MatIconModule, MatMenuModule, MatDialogModule],\n  exports: [SwuiPagePanelComponent],\n  declarations: [SwuiPagePanelComponent, ActionConfirmDialogComponent],\n  providers: []\n})], SwuiPagePanelModule);\nexport { SwuiPagePanelModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "SwuiPagePanelComponent", "CommonModule", "TranslateModule", "MatCardModule", "MatButtonModule", "MatIconModule", "RouterModule", "MatMenuModule", "MatDialogModule", "ActionConfirmDialogComponent", "SwuiPagePanelModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports", "declarations", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-page-panel/swui-page-panel.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { SwuiPagePanelComponent } from './swui-page-panel.component';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { RouterModule } from '@angular/router';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { ActionConfirmDialogComponent } from './action-confirm-dialog/action-confirm-dialog.component';\nlet SwuiPagePanelModule = class SwuiPagePanelModule {\n};\nSwuiPagePanelModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            RouterModule,\n            TranslateModule.forChild(),\n            MatCardModule,\n            MatButtonModule,\n            MatIconModule,\n            MatMenuModule,\n            MatDialogModule,\n        ],\n        exports: [\n            SwuiPagePanelComponent,\n        ],\n        declarations: [\n            SwuiPagePanelComponent,\n            ActionConfirmDialogComponent,\n        ],\n        providers: [],\n    })\n], SwuiPagePanelModule);\nexport { SwuiPagePanelModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,IAAIC,mBAAmB,GAAG,MAAMA,mBAAmB,CAAC,EACnD;AACDA,mBAAmB,GAAGZ,UAAU,CAAC,CAC7BC,QAAQ,CAAC;EACLY,OAAO,EAAE,CACLV,YAAY,EACZK,YAAY,EACZJ,eAAe,CAACU,QAAQ,CAAC,CAAC,EAC1BT,aAAa,EACbC,eAAe,EACfC,aAAa,EACbE,aAAa,EACbC,eAAe,CAClB;EACDK,OAAO,EAAE,CACLb,sBAAsB,CACzB;EACDc,YAAY,EAAE,CACVd,sBAAsB,EACtBS,4BAA4B,CAC/B;EACDM,SAAS,EAAE;AACf,CAAC,CAAC,CACL,EAAEL,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}