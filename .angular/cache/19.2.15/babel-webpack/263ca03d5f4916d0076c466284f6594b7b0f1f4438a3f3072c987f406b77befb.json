{"ast": null, "code": "var _SwuiSettingsDialogComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-settings-dialog.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-settings-dialog.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup } from '@angular/forms';\nimport { isEqual } from 'lodash';\nimport * as moment from 'moment';\nimport 'moment-timezone';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { DEFAULT_SETTINGS, SettingsService } from '../../services/settings/settings.service';\nconst TIMEZONES = moment.tz.names().filter(name => name.indexOf('Etc') === -1).map(name => {\n  const zone = moment.tz(name);\n  return {\n    text: `${zone.format('Z')} ${name}`,\n    zone: zone.utcOffset(),\n    id: name\n  };\n}).sort((a, b) => {\n  const zoneDiff = b.zone - a.zone;\n  if (zoneDiff !== 0) {\n    return zoneDiff;\n  }\n  if (a.text > b.text) {\n    return 1;\n  }\n  if (a.text < b.text) {\n    return -1;\n  }\n  return 0;\n});\nconst TIME_FORMATS = ['HH:mm:ss', 'HH:mm', 'HH.mm.ss', 'HH.mm'].map(format => ({\n  text: moment.utc().format(format),\n  id: format\n}));\nconst DATE_FORMATS = ['DD.MM.YYYY', 'DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY/MM/DD', 'YY/MM/DD'].map(format => ({\n  text: moment.utc().format(format),\n  id: format\n}));\nconst EXAMPLE_NUMBER = 1234567.89;\nconst CURRENCY_FORMATS = ['de-DE', 'zh-CN', 'ru-RU'].map(format => {\n  const toLocale = lang => EXAMPLE_NUMBER.toLocaleString(lang, {\n    minimumFractionDigits: 2\n  });\n  const text = toLocale(format);\n  const primary = text === toLocale(window.navigator.language);\n  return {\n    id: primary ? window.navigator.language : format,\n    text,\n    primary\n  };\n});\nlet SwuiSettingsDialogComponent = (_SwuiSettingsDialogComponent = class SwuiSettingsDialogComponent {\n  constructor(service) {\n    this.service = service;\n    this.destroyed = new Subject();\n    this.form = new UntypedFormGroup({\n      timezoneName: new UntypedFormControl(''),\n      pageSize: new UntypedFormControl(''),\n      dateFormat: new UntypedFormControl(''),\n      timeFormat: new UntypedFormControl(''),\n      currencyFormat: new UntypedFormControl('')\n    });\n  }\n  ngOnInit() {\n    this.service.appSettings$.pipe(takeUntil(this.destroyed)).subscribe(settings => {\n      this.setValue(settings);\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed.next(undefined);\n    this.destroyed.complete();\n  }\n  get timezoneOptions() {\n    return TIMEZONES;\n  }\n  get pageSizeOptions() {\n    return [10, 20, 30, 50, 100];\n  }\n  get dateFormatOptions() {\n    return DATE_FORMATS;\n  }\n  get timeFormatOptions() {\n    return TIME_FORMATS;\n  }\n  get currencyFormatOptions() {\n    return CURRENCY_FORMATS;\n  }\n  get hasDefaultValues() {\n    return isEqual(this.form.value, DEFAULT_SETTINGS);\n  }\n  onReset(event) {\n    event.preventDefault();\n    this.setValue(DEFAULT_SETTINGS);\n    this.form.markAsDirty();\n  }\n  setValue(settings) {\n    this.form.setValue({\n      timezoneName: settings.timezoneName,\n      pageSize: settings.pageSize,\n      dateFormat: settings.dateFormat,\n      timeFormat: settings.timeFormat,\n      currencyFormat: settings.currencyFormat\n    }, {\n      emitEvent: false\n    });\n  }\n}, _SwuiSettingsDialogComponent.ctorParameters = () => [{\n  type: SettingsService\n}], _SwuiSettingsDialogComponent);\nSwuiSettingsDialogComponent = __decorate([Component({\n  selector: 'lib-swui-settings-dialog',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSettingsDialogComponent);\nexport { SwuiSettingsDialogComponent };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "Component", "UntypedFormControl", "UntypedFormGroup", "isEqual", "moment", "Subject", "takeUntil", "DEFAULT_SETTINGS", "SettingsService", "TIMEZONES", "tz", "names", "filter", "name", "indexOf", "map", "zone", "text", "format", "utcOffset", "id", "sort", "a", "b", "zoneDiff", "TIME_FORMATS", "utc", "DATE_FORMATS", "EXAMPLE_NUMBER", "CURRENCY_FORMATS", "toLocale", "lang", "toLocaleString", "minimumFractionDigits", "primary", "window", "navigator", "language", "SwuiSettingsDialogComponent", "_SwuiSettingsDialogComponent", "constructor", "service", "destroyed", "form", "timezoneName", "pageSize", "dateFormat", "timeFormat", "currencyFormat", "ngOnInit", "appSettings$", "pipe", "subscribe", "settings", "setValue", "ngOnDestroy", "next", "undefined", "complete", "timezoneOptions", "pageSizeOptions", "dateFormatOptions", "timeFormatOptions", "currencyFormatOptions", "hasDefault<PERSON><PERSON>ues", "value", "onReset", "event", "preventDefault", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emitEvent", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "changeDetection", "OnPush", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/settings-dialog/swui-settings-dialog.component.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup } from '@angular/forms';\nimport { isEqual } from 'lodash';\nimport * as moment from 'moment';\nimport 'moment-timezone';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AppSettings } from '../../services/settings/app-settings';\nimport { DEFAULT_SETTINGS, SettingsService } from '../../services/settings/settings.service';\n\nconst TIMEZONES = moment.tz.names()\n  .filter(name => name.indexOf('Etc') === -1)\n  .map(name => {\n    const zone = moment.tz(name);\n    return {\n      text: `${zone.format('Z')} ${name}`,\n      zone: zone.utcOffset(),\n      id: name\n    };\n  })\n  .sort(( a, b ) => {\n    const zoneDiff = b.zone - a.zone;\n    if (zoneDiff !== 0) {\n      return zoneDiff;\n    }\n    if (a.text > b.text) {\n      return 1;\n    }\n    if (a.text < b.text) {\n      return -1;\n    }\n    return 0;\n  });\n\nconst TIME_FORMATS = ['HH:mm:ss', 'HH:mm', 'HH.mm.ss', 'HH.mm'].map(format => ({\n  text: moment.utc().format(format),\n  id: format\n}));\n\nconst DATE_FORMATS = ['DD.MM.YYYY', 'DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY/MM/DD', 'YY/MM/DD'].map(format => ({\n  text: moment.utc().format(format),\n  id: format\n}));\n\nconst EXAMPLE_NUMBER = 1234567.89;\n\nconst CURRENCY_FORMATS = ['de-DE', 'zh-CN', 'ru-RU'].map(format => {\n  const toLocale = ( lang: string ) => EXAMPLE_NUMBER.toLocaleString(lang, { minimumFractionDigits: 2 });\n  const text = toLocale(format);\n  const primary = text === toLocale(window.navigator.language);\n  return {\n    id: primary ? window.navigator.language : format,\n    text,\n    primary\n  };\n});\n\n@Component({\n    selector: 'lib-swui-settings-dialog',\n    templateUrl: './swui-settings-dialog.component.html',\n    styleUrls: ['./swui-settings-dialog.component.scss'],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    standalone: false\n})\nexport class SwuiSettingsDialogComponent implements OnInit, OnDestroy {\n  readonly form: UntypedFormGroup;\n  private readonly destroyed = new Subject<void>();\n\n  constructor( private readonly service: SettingsService ) {\n    this.form = new UntypedFormGroup({\n      timezoneName: new UntypedFormControl(''),\n      pageSize: new UntypedFormControl(''),\n      dateFormat: new UntypedFormControl(''),\n      timeFormat: new UntypedFormControl(''),\n      currencyFormat: new UntypedFormControl('')\n    });\n  }\n\n  ngOnInit() {\n    this.service.appSettings$.pipe(\n      takeUntil(this.destroyed)\n    ).subscribe(settings => {\n      this.setValue(settings);\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed.next(undefined);\n    this.destroyed.complete();\n  }\n\n  get timezoneOptions() {\n    return TIMEZONES;\n  }\n\n  get pageSizeOptions() {\n    return [10, 20, 30, 50, 100];\n  }\n\n  get dateFormatOptions() {\n    return DATE_FORMATS;\n  }\n\n  get timeFormatOptions() {\n    return TIME_FORMATS;\n  }\n\n  get currencyFormatOptions() {\n    return CURRENCY_FORMATS;\n  }\n\n  get hasDefaultValues() {\n    return isEqual(this.form.value, DEFAULT_SETTINGS);\n  }\n\n  onReset( event: MouseEvent ) {\n    event.preventDefault();\n    this.setValue(DEFAULT_SETTINGS);\n    this.form.markAsDirty();\n  }\n\n  private setValue( settings: AppSettings ) {\n    this.form.setValue({\n      timezoneName: settings.timezoneName,\n      pageSize: settings.pageSize,\n      dateFormat: settings.dateFormat,\n      timeFormat: settings.timeFormat,\n      currencyFormat: settings.currencyFormat\n    }, { emitEvent: false });\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,uBAAuB,EAAEC,SAAS,QAA2B,eAAe;AACrF,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,gBAAgB;AACrE,SAASC,OAAO,QAAQ,QAAQ;AAChC,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,OAAO,iBAAiB;AACxB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,gBAAgB,EAAEC,eAAe,QAAQ,0CAA0C;AAE5F,MAAMC,SAAS,GAAGL,MAAM,CAACM,EAAE,CAACC,KAAK,EAAE,CAChCC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAC1CC,GAAG,CAACF,IAAI,IAAG;EACV,MAAMG,IAAI,GAAGZ,MAAM,CAACM,EAAE,CAACG,IAAI,CAAC;EAC5B,OAAO;IACLI,IAAI,EAAE,GAAGD,IAAI,CAACE,MAAM,CAAC,GAAG,CAAC,IAAIL,IAAI,EAAE;IACnCG,IAAI,EAAEA,IAAI,CAACG,SAAS,EAAE;IACtBC,EAAE,EAAEP;GACL;AACH,CAAC,CAAC,CACDQ,IAAI,CAAC,CAAEC,CAAC,EAAEC,CAAC,KAAK;EACf,MAAMC,QAAQ,GAAGD,CAAC,CAACP,IAAI,GAAGM,CAAC,CAACN,IAAI;EAChC,IAAIQ,QAAQ,KAAK,CAAC,EAAE;IAClB,OAAOA,QAAQ;EACjB;EACA,IAAIF,CAAC,CAACL,IAAI,GAAGM,CAAC,CAACN,IAAI,EAAE;IACnB,OAAO,CAAC;EACV;EACA,IAAIK,CAAC,CAACL,IAAI,GAAGM,CAAC,CAACN,IAAI,EAAE;IACnB,OAAO,CAAC,CAAC;EACX;EACA,OAAO,CAAC;AACV,CAAC,CAAC;AAEJ,MAAMQ,YAAY,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAACV,GAAG,CAACG,MAAM,KAAK;EAC7ED,IAAI,EAAEb,MAAM,CAACsB,GAAG,EAAE,CAACR,MAAM,CAACA,MAAM,CAAC;EACjCE,EAAE,EAAEF;CACL,CAAC,CAAC;AAEH,MAAMS,YAAY,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,CAAC,CAACZ,GAAG,CAACG,MAAM,KAAK;EACvGD,IAAI,EAAEb,MAAM,CAACsB,GAAG,EAAE,CAACR,MAAM,CAACA,MAAM,CAAC;EACjCE,EAAE,EAAEF;CACL,CAAC,CAAC;AAEH,MAAMU,cAAc,GAAG,UAAU;AAEjC,MAAMC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAACd,GAAG,CAACG,MAAM,IAAG;EAChE,MAAMY,QAAQ,GAAKC,IAAY,IAAMH,cAAc,CAACI,cAAc,CAACD,IAAI,EAAE;IAAEE,qBAAqB,EAAE;EAAC,CAAE,CAAC;EACtG,MAAMhB,IAAI,GAAGa,QAAQ,CAACZ,MAAM,CAAC;EAC7B,MAAMgB,OAAO,GAAGjB,IAAI,KAAKa,QAAQ,CAACK,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAC;EAC5D,OAAO;IACLjB,EAAE,EAAEc,OAAO,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,GAAGnB,MAAM;IAChDD,IAAI;IACJiB;GACD;AACH,CAAC,CAAC;AASK,IAAMI,2BAA2B,IAAAC,4BAAA,GAAjC,MAAMD,2BAA2B;EAItCE,YAA8BC,OAAwB;IAAxB,KAAAA,OAAO,GAAPA,OAAO;IAFpB,KAAAC,SAAS,GAAG,IAAIrC,OAAO,EAAQ;IAG9C,IAAI,CAACsC,IAAI,GAAG,IAAIzC,gBAAgB,CAAC;MAC/B0C,YAAY,EAAE,IAAI3C,kBAAkB,CAAC,EAAE,CAAC;MACxC4C,QAAQ,EAAE,IAAI5C,kBAAkB,CAAC,EAAE,CAAC;MACpC6C,UAAU,EAAE,IAAI7C,kBAAkB,CAAC,EAAE,CAAC;MACtC8C,UAAU,EAAE,IAAI9C,kBAAkB,CAAC,EAAE,CAAC;MACtC+C,cAAc,EAAE,IAAI/C,kBAAkB,CAAC,EAAE;KAC1C,CAAC;EACJ;EAEAgD,QAAQA,CAAA;IACN,IAAI,CAACR,OAAO,CAACS,YAAY,CAACC,IAAI,CAC5B7C,SAAS,CAAC,IAAI,CAACoC,SAAS,CAAC,CAC1B,CAACU,SAAS,CAACC,QAAQ,IAAG;MACrB,IAAI,CAACC,QAAQ,CAACD,QAAQ,CAAC;IACzB,CAAC,CAAC;EACJ;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACb,SAAS,CAACc,IAAI,CAACC,SAAS,CAAC;IAC9B,IAAI,CAACf,SAAS,CAACgB,QAAQ,EAAE;EAC3B;EAEA,IAAIC,eAAeA,CAAA;IACjB,OAAOlD,SAAS;EAClB;EAEA,IAAImD,eAAeA,CAAA;IACjB,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC9B;EAEA,IAAIC,iBAAiBA,CAAA;IACnB,OAAOlC,YAAY;EACrB;EAEA,IAAImC,iBAAiBA,CAAA;IACnB,OAAOrC,YAAY;EACrB;EAEA,IAAIsC,qBAAqBA,CAAA;IACvB,OAAOlC,gBAAgB;EACzB;EAEA,IAAImC,gBAAgBA,CAAA;IAClB,OAAO7D,OAAO,CAAC,IAAI,CAACwC,IAAI,CAACsB,KAAK,EAAE1D,gBAAgB,CAAC;EACnD;EAEA2D,OAAOA,CAAEC,KAAiB;IACxBA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACd,QAAQ,CAAC/C,gBAAgB,CAAC;IAC/B,IAAI,CAACoC,IAAI,CAAC0B,WAAW,EAAE;EACzB;EAEQf,QAAQA,CAAED,QAAqB;IACrC,IAAI,CAACV,IAAI,CAACW,QAAQ,CAAC;MACjBV,YAAY,EAAES,QAAQ,CAACT,YAAY;MACnCC,QAAQ,EAAEQ,QAAQ,CAACR,QAAQ;MAC3BC,UAAU,EAAEO,QAAQ,CAACP,UAAU;MAC/BC,UAAU,EAAEM,QAAQ,CAACN,UAAU;MAC/BC,cAAc,EAAEK,QAAQ,CAACL;KAC1B,EAAE;MAAEsB,SAAS,EAAE;IAAK,CAAE,CAAC;EAC1B;;;;AAjEWhC,2BAA2B,GAAAiC,UAAA,EAPvCvE,SAAS,CAAC;EACPwE,QAAQ,EAAE,0BAA0B;EACpCC,QAAA,EAAAC,oBAAoD;EAEpDC,eAAe,EAAE5E,uBAAuB,CAAC6E,MAAM;EAC/CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWvC,2BAA2B,CAkEvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}