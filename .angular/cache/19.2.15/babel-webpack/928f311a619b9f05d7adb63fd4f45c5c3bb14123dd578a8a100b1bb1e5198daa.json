{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _PlatformLocation, _BrowserPlatformLocation, _LocationStrategy, _PathLocationStrategy, _Location;\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injectable, Optional, Inject, ɵɵinject as __inject } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { DOCUMENT } from './dom_tokens-rA0ACyx7.mjs';\nlet _DOM = null;\nfunction getDOM() {\n  return _DOM;\n}\nfunction setRootDomAdapter(adapter) {\n  _DOM !== null && _DOM !== void 0 ? _DOM : _DOM = adapter;\n}\n/**\n * Provides DOM operations in an environment-agnostic way.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass DomAdapter {}\n\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * `PlatformLocation` encapsulates all calls to DOM APIs, which allows the Router to be\n * platform-agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that Angular supports. For example, `@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `@angular/platform-server` provides\n * one suitable for use with server-side rendering.\n *\n * The `PlatformLocation` class is used directly by all implementations of {@link LocationStrategy}\n * when they need to interact with the DOM APIs like pushState, popState, etc.\n *\n * {@link LocationStrategy} in turn is used by the {@link Location} service which is used directly\n * by the {@link /api/router/Router Router} in order to navigate between routes. Since all interactions between\n * {@link /api/router/Router Router} /\n * {@link Location} / {@link LocationStrategy} and DOM APIs flow through the `PlatformLocation`\n * class, they are all platform-agnostic.\n *\n * @publicApi\n */\nclass PlatformLocation {\n  historyGo(relativePosition) {\n    throw new Error(ngDevMode ? 'Not implemented' : '');\n  }\n}\n_PlatformLocation = PlatformLocation;\n_defineProperty(PlatformLocation, \"\\u0275fac\", function _PlatformLocation_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _PlatformLocation)();\n});\n_defineProperty(PlatformLocation, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _PlatformLocation,\n  factory: () => (() => inject(BrowserPlatformLocation))(),\n  providedIn: 'platform'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformLocation, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform',\n      useFactory: () => inject(BrowserPlatformLocation)\n    }]\n  }], null, null);\n})();\n/**\n * @description\n * Indicates when a location is initialized.\n *\n * @publicApi\n */\nconst LOCATION_INITIALIZED = new InjectionToken(ngDevMode ? 'Location Initialized' : '');\n/**\n * `PlatformLocation` encapsulates all of the direct calls to platform APIs.\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * @publicApi\n */\nclass BrowserPlatformLocation extends PlatformLocation {\n  constructor() {\n    super();\n    _defineProperty(this, \"_location\", void 0);\n    _defineProperty(this, \"_history\", void 0);\n    _defineProperty(this, \"_doc\", inject(DOCUMENT));\n    this._location = window.location;\n    this._history = window.history;\n  }\n  getBaseHrefFromDOM() {\n    return getDOM().getBaseHref(this._doc);\n  }\n  onPopState(fn) {\n    const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n    window.addEventListener('popstate', fn, false);\n    return () => window.removeEventListener('popstate', fn);\n  }\n  onHashChange(fn) {\n    const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n    window.addEventListener('hashchange', fn, false);\n    return () => window.removeEventListener('hashchange', fn);\n  }\n  get href() {\n    return this._location.href;\n  }\n  get protocol() {\n    return this._location.protocol;\n  }\n  get hostname() {\n    return this._location.hostname;\n  }\n  get port() {\n    return this._location.port;\n  }\n  get pathname() {\n    return this._location.pathname;\n  }\n  get search() {\n    return this._location.search;\n  }\n  get hash() {\n    return this._location.hash;\n  }\n  set pathname(newPath) {\n    this._location.pathname = newPath;\n  }\n  pushState(state, title, url) {\n    this._history.pushState(state, title, url);\n  }\n  replaceState(state, title, url) {\n    this._history.replaceState(state, title, url);\n  }\n  forward() {\n    this._history.forward();\n  }\n  back() {\n    this._history.back();\n  }\n  historyGo(relativePosition = 0) {\n    this._history.go(relativePosition);\n  }\n  getState() {\n    return this._history.state;\n  }\n}\n_BrowserPlatformLocation = BrowserPlatformLocation;\n_defineProperty(BrowserPlatformLocation, \"\\u0275fac\", function _BrowserPlatformLocation_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BrowserPlatformLocation)();\n});\n_defineProperty(BrowserPlatformLocation, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _BrowserPlatformLocation,\n  factory: () => (() => new _BrowserPlatformLocation())(),\n  providedIn: 'platform'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserPlatformLocation, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform',\n      useFactory: () => new BrowserPlatformLocation()\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Joins two parts of a URL with a slash if needed.\n *\n * @param start  URL string\n * @param end    URL string\n *\n *\n * @returns The joined URL string.\n */\nfunction joinWithSlash(start, end) {\n  // If `start` is an empty string, return `end` as the result.\n  if (!start) return end;\n  // If `end` is an empty string, return `start` as the result.\n  if (!end) return start;\n  // If `start` ends with a slash, remove the leading slash from `end`.\n  if (start.endsWith('/')) {\n    return end.startsWith('/') ? start + end.slice(1) : start + end;\n  }\n  // If `start` doesn't end with a slash, add one if `end` doesn't start with a slash.\n  return end.startsWith('/') ? start + end : `${start}/${end}`;\n}\n/**\n * Removes a trailing slash from a URL string if needed.\n * Looks for the first occurrence of either `#`, `?`, or the end of the\n * line as `/` characters and removes the trailing slash if one exists.\n *\n * @param url URL string.\n *\n * @returns The URL string, modified if needed.\n */\nfunction stripTrailingSlash(url) {\n  // Find the index of the first occurrence of `#`, `?`, or the end of the string.\n  // This marks the start of the query string, fragment, or the end of the URL path.\n  const pathEndIdx = url.search(/#|\\?|$/);\n  // Check if the character before `pathEndIdx` is a trailing slash.\n  // If it is, remove the trailing slash and return the modified URL.\n  // Otherwise, return the URL as is.\n  return url[pathEndIdx - 1] === '/' ? url.slice(0, pathEndIdx - 1) + url.slice(pathEndIdx) : url;\n}\n/**\n * Normalizes URL parameters by prepending with `?` if needed.\n *\n * @param  params String of URL parameters.\n *\n * @returns The normalized URL parameters string.\n */\nfunction normalizeQueryParams(params) {\n  return params && params[0] !== '?' ? `?${params}` : params;\n}\n\n/**\n * Enables the `Location` service to read route state from the browser's URL.\n * Angular provides two strategies:\n * `HashLocationStrategy` and `PathLocationStrategy`.\n *\n * Applications should use the `Router` or `Location` services to\n * interact with application route state.\n *\n * For instance, `HashLocationStrategy` produces URLs like\n * <code class=\"no-auto-link\">http://example.com/#/foo</code>,\n * and `PathLocationStrategy` produces\n * <code class=\"no-auto-link\">http://example.com/foo</code> as an equivalent URL.\n *\n * See these two classes for more.\n *\n * @publicApi\n */\nclass LocationStrategy {\n  historyGo(relativePosition) {\n    throw new Error(ngDevMode ? 'Not implemented' : '');\n  }\n}\n_LocationStrategy = LocationStrategy;\n_defineProperty(LocationStrategy, \"\\u0275fac\", function _LocationStrategy_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _LocationStrategy)();\n});\n_defineProperty(LocationStrategy, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _LocationStrategy,\n  factory: () => (() => inject(PathLocationStrategy))(),\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LocationStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => inject(PathLocationStrategy)\n    }]\n  }], null, null);\n})();\n/**\n * A predefined DI token for the base href\n * to be used with the `PathLocationStrategy`.\n * The base href is the URL prefix that should be preserved when generating\n * and recognizing URLs.\n *\n * @usageNotes\n *\n * The following example shows how to use this token to configure the root app injector\n * with a base href value, so that the DI framework can supply the dependency anywhere in the app.\n *\n * ```ts\n * import {NgModule} from '@angular/core';\n * import {APP_BASE_HREF} from '@angular/common';\n *\n * @NgModule({\n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n *\n * @publicApi\n */\nconst APP_BASE_HREF = new InjectionToken(ngDevMode ? 'appBaseHref' : '');\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n *\n * If you're using `PathLocationStrategy`, you may provide a {@link APP_BASE_HREF}\n * or add a `<base href>` element to the document to override the default.\n *\n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app/'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`. To ensure all relative URIs resolve correctly,\n * the `<base href>` and/or `APP_BASE_HREF` should end with a `/`.\n *\n * Similarly, if you add `<base href='/my/app/'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n *\n * Note that when using `PathLocationStrategy`, neither the query nor\n * the fragment in the `<base href>` will be preserved, as outlined\n * by the [RFC](https://tools.ietf.org/html/rfc3986#section-5.2.2).\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\nclass PathLocationStrategy extends LocationStrategy {\n  constructor(_platformLocation, href) {\n    var _ref, _ref2, _inject$location;\n    super();\n    _defineProperty(this, \"_platformLocation\", void 0);\n    _defineProperty(this, \"_baseHref\", void 0);\n    _defineProperty(this, \"_removeListenerFns\", []);\n    this._platformLocation = _platformLocation;\n    this._baseHref = (_ref = (_ref2 = href !== null && href !== void 0 ? href : this._platformLocation.getBaseHrefFromDOM()) !== null && _ref2 !== void 0 ? _ref2 : (_inject$location = inject(DOCUMENT).location) === null || _inject$location === void 0 ? void 0 : _inject$location.origin) !== null && _ref !== void 0 ? _ref : '';\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    while (this._removeListenerFns.length) {\n      this._removeListenerFns.pop()();\n    }\n  }\n  onPopState(fn) {\n    this._removeListenerFns.push(this._platformLocation.onPopState(fn), this._platformLocation.onHashChange(fn));\n  }\n  getBaseHref() {\n    return this._baseHref;\n  }\n  prepareExternalUrl(internal) {\n    return joinWithSlash(this._baseHref, internal);\n  }\n  path(includeHash = false) {\n    const pathname = this._platformLocation.pathname + normalizeQueryParams(this._platformLocation.search);\n    const hash = this._platformLocation.hash;\n    return hash && includeHash ? `${pathname}${hash}` : pathname;\n  }\n  pushState(state, title, url, queryParams) {\n    const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n    this._platformLocation.pushState(state, title, externalUrl);\n  }\n  replaceState(state, title, url, queryParams) {\n    const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n    this._platformLocation.replaceState(state, title, externalUrl);\n  }\n  forward() {\n    this._platformLocation.forward();\n  }\n  back() {\n    this._platformLocation.back();\n  }\n  getState() {\n    return this._platformLocation.getState();\n  }\n  historyGo(relativePosition = 0) {\n    var _this$_platformLocati, _this$_platformLocati2;\n    (_this$_platformLocati = (_this$_platformLocati2 = this._platformLocation).historyGo) === null || _this$_platformLocati === void 0 || _this$_platformLocati.call(_this$_platformLocati2, relativePosition);\n  }\n}\n_PathLocationStrategy = PathLocationStrategy;\n_defineProperty(PathLocationStrategy, \"\\u0275fac\", function _PathLocationStrategy_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _PathLocationStrategy)(i0.ɵɵinject(PlatformLocation), i0.ɵɵinject(APP_BASE_HREF, 8));\n});\n_defineProperty(PathLocationStrategy, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _PathLocationStrategy,\n  factory: _PathLocationStrategy.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PathLocationStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: PlatformLocation\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [APP_BASE_HREF]\n    }]\n  }], null);\n})();\n\n/**\n * @description\n *\n * A service that applications can use to interact with a browser's URL.\n *\n * Depending on the `LocationStrategy` used, `Location` persists\n * to the URL's path or the URL's hash segment.\n *\n * @usageNotes\n *\n * It's better to use the `Router.navigate()` service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n *\n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\nclass Location {\n  constructor(locationStrategy) {\n    /** @internal */\n    _defineProperty(this, \"_subject\", new Subject());\n    /** @internal */\n    _defineProperty(this, \"_basePath\", void 0);\n    /** @internal */\n    _defineProperty(this, \"_locationStrategy\", void 0);\n    /** @internal */\n    _defineProperty(this, \"_urlChangeListeners\", []);\n    /** @internal */\n    _defineProperty(this, \"_urlChangeSubscription\", null);\n    this._locationStrategy = locationStrategy;\n    const baseHref = this._locationStrategy.getBaseHref();\n    // Note: This class's interaction with base HREF does not fully follow the rules\n    // outlined in the spec https://www.freesoft.org/CIE/RFC/1808/18.htm.\n    // Instead of trying to fix individual bugs with more and more code, we should\n    // investigate using the URL constructor and providing the base as a second\n    // argument.\n    // https://developer.mozilla.org/en-US/docs/Web/API/URL/URL#parameters\n    this._basePath = _stripOrigin(stripTrailingSlash(_stripIndexHtml(baseHref)));\n    this._locationStrategy.onPopState(ev => {\n      this._subject.next({\n        'url': this.path(true),\n        'pop': true,\n        'state': ev.state,\n        'type': ev.type\n      });\n    });\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    var _this$_urlChangeSubsc;\n    (_this$_urlChangeSubsc = this._urlChangeSubscription) === null || _this$_urlChangeSubsc === void 0 || _this$_urlChangeSubsc.unsubscribe();\n    this._urlChangeListeners = [];\n  }\n  /**\n   * Normalizes the URL path for this location.\n   *\n   * @param includeHash True to include an anchor fragment in the path.\n   *\n   * @returns The normalized URL path.\n   */\n  // TODO: vsavkin. Remove the boolean flag and always include hash once the deprecated router is\n  // removed.\n  path(includeHash = false) {\n    return this.normalize(this._locationStrategy.path(includeHash));\n  }\n  /**\n   * Reports the current state of the location history.\n   * @returns The current value of the `history.state` object.\n   */\n  getState() {\n    return this._locationStrategy.getState();\n  }\n  /**\n   * Normalizes the given path and compares to the current normalized path.\n   *\n   * @param path The given URL path.\n   * @param query Query parameters.\n   *\n   * @returns True if the given URL path is equal to the current normalized path, false\n   * otherwise.\n   */\n  isCurrentPathEqualTo(path, query = '') {\n    return this.path() == this.normalize(path + normalizeQueryParams(query));\n  }\n  /**\n   * Normalizes a URL path by stripping any trailing slashes.\n   *\n   * @param url String representing a URL.\n   *\n   * @returns The normalized URL string.\n   */\n  normalize(url) {\n    return Location.stripTrailingSlash(_stripBasePath(this._basePath, _stripIndexHtml(url)));\n  }\n  /**\n   * Normalizes an external URL path.\n   * If the given URL doesn't begin with a leading slash (`'/'`), adds one\n   * before normalizing. Adds a hash if `HashLocationStrategy` is\n   * in use, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n   *\n   * @param url String representing a URL.\n   *\n   * @returns  A normalized platform-specific URL.\n   */\n  prepareExternalUrl(url) {\n    if (url && url[0] !== '/') {\n      url = '/' + url;\n    }\n    return this._locationStrategy.prepareExternalUrl(url);\n  }\n  // TODO: rename this method to pushState\n  /**\n   * Changes the browser's URL to a normalized version of a given URL, and pushes a\n   * new item onto the platform's history.\n   *\n   * @param path  URL path to normalize.\n   * @param query Query parameters.\n   * @param state Location history state.\n   *\n   */\n  go(path, query = '', state = null) {\n    this._locationStrategy.pushState(state, '', path, query);\n    this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n  }\n  /**\n   * Changes the browser's URL to a normalized version of the given URL, and replaces\n   * the top item on the platform's history stack.\n   *\n   * @param path  URL path to normalize.\n   * @param query Query parameters.\n   * @param state Location history state.\n   */\n  replaceState(path, query = '', state = null) {\n    this._locationStrategy.replaceState(state, '', path, query);\n    this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n  }\n  /**\n   * Navigates forward in the platform's history.\n   */\n  forward() {\n    this._locationStrategy.forward();\n  }\n  /**\n   * Navigates back in the platform's history.\n   */\n  back() {\n    this._locationStrategy.back();\n  }\n  /**\n   * Navigate to a specific page from session history, identified by its relative position to the\n   * current page.\n   *\n   * @param relativePosition  Position of the target page in the history relative to the current\n   *     page.\n   * A negative value moves backwards, a positive value moves forwards, e.g. `location.historyGo(2)`\n   * moves forward two pages and `location.historyGo(-2)` moves back two pages. When we try to go\n   * beyond what's stored in the history session, we stay in the current page. Same behaviour occurs\n   * when `relativePosition` equals 0.\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/History_API#Moving_to_a_specific_point_in_history\n   */\n  historyGo(relativePosition = 0) {\n    var _this$_locationStrate, _this$_locationStrate2;\n    (_this$_locationStrate = (_this$_locationStrate2 = this._locationStrategy).historyGo) === null || _this$_locationStrate === void 0 || _this$_locationStrate.call(_this$_locationStrate2, relativePosition);\n  }\n  /**\n   * Registers a URL change listener. Use to catch updates performed by the Angular\n   * framework that are not detectible through \"popstate\" or \"hashchange\" events.\n   *\n   * @param fn The change handler function, which take a URL and a location history state.\n   * @returns A function that, when executed, unregisters a URL change listener.\n   */\n  onUrlChange(fn) {\n    var _this$_urlChangeSubsc2;\n    this._urlChangeListeners.push(fn);\n    (_this$_urlChangeSubsc2 = this._urlChangeSubscription) !== null && _this$_urlChangeSubsc2 !== void 0 ? _this$_urlChangeSubsc2 : this._urlChangeSubscription = this.subscribe(v => {\n      this._notifyUrlChangeListeners(v.url, v.state);\n    });\n    return () => {\n      const fnIndex = this._urlChangeListeners.indexOf(fn);\n      this._urlChangeListeners.splice(fnIndex, 1);\n      if (this._urlChangeListeners.length === 0) {\n        var _this$_urlChangeSubsc3;\n        (_this$_urlChangeSubsc3 = this._urlChangeSubscription) === null || _this$_urlChangeSubsc3 === void 0 || _this$_urlChangeSubsc3.unsubscribe();\n        this._urlChangeSubscription = null;\n      }\n    };\n  }\n  /** @internal */\n  _notifyUrlChangeListeners(url = '', state) {\n    this._urlChangeListeners.forEach(fn => fn(url, state));\n  }\n  /**\n   * Subscribes to the platform's `popState` events.\n   *\n   * Note: `Location.go()` does not trigger the `popState` event in the browser. Use\n   * `Location.onUrlChange()` to subscribe to URL changes instead.\n   *\n   * @param value Event that is triggered when the state history changes.\n   * @param exception The exception to throw.\n   *\n   * @see [onpopstate](https://developer.mozilla.org/en-US/docs/Web/API/WindowEventHandlers/onpopstate)\n   *\n   * @returns Subscribed events.\n   */\n  subscribe(onNext, onThrow, onReturn) {\n    return this._subject.subscribe({\n      next: onNext,\n      error: onThrow !== null && onThrow !== void 0 ? onThrow : undefined,\n      complete: onReturn !== null && onReturn !== void 0 ? onReturn : undefined\n    });\n  }\n  /**\n   * Normalizes URL parameters by prepending with `?` if needed.\n   *\n   * @param  params String of URL parameters.\n   *\n   * @returns The normalized URL parameters string.\n   */\n}\n_Location = Location;\n_defineProperty(Location, \"normalizeQueryParams\", normalizeQueryParams);\n/**\n * Joins two parts of a URL with a slash if needed.\n *\n * @param start  URL string\n * @param end    URL string\n *\n *\n * @returns The joined URL string.\n */\n_defineProperty(Location, \"joinWithSlash\", joinWithSlash);\n/**\n * Removes a trailing slash from a URL string if needed.\n * Looks for the first occurrence of either `#`, `?`, or the end of the\n * line as `/` characters and removes the trailing slash if one exists.\n *\n * @param url URL string.\n *\n * @returns The URL string, modified if needed.\n */\n_defineProperty(Location, \"stripTrailingSlash\", stripTrailingSlash);\n_defineProperty(Location, \"\\u0275fac\", function _Location_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _Location)(i0.ɵɵinject(LocationStrategy));\n});\n_defineProperty(Location, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _Location,\n  factory: () => createLocation(),\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Location, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      // See #23917\n      useFactory: createLocation\n    }]\n  }], () => [{\n    type: LocationStrategy\n  }], null);\n})();\nfunction createLocation() {\n  return new Location(__inject(LocationStrategy));\n}\nfunction _stripBasePath(basePath, url) {\n  if (!basePath || !url.startsWith(basePath)) {\n    return url;\n  }\n  const strippedUrl = url.substring(basePath.length);\n  if (strippedUrl === '' || ['/', ';', '?', '#'].includes(strippedUrl[0])) {\n    return strippedUrl;\n  }\n  return url;\n}\nfunction _stripIndexHtml(url) {\n  return url.replace(/\\/index.html$/, '');\n}\nfunction _stripOrigin(baseHref) {\n  // DO NOT REFACTOR! Previously, this check looked like this:\n  // `/^(https?:)?\\/\\//.test(baseHref)`, but that resulted in\n  // syntactically incorrect code after Closure Compiler minification.\n  // This was likely caused by a bug in Closure Compiler, but\n  // for now, the check is rewritten to use `new RegExp` instead.\n  const isAbsoluteUrl = new RegExp('^(https?:)?//').test(baseHref);\n  if (isAbsoluteUrl) {\n    const [, pathname] = baseHref.split(/\\/\\/[^\\/]+/);\n    return pathname;\n  }\n  return baseHref;\n}\nexport { APP_BASE_HREF, BrowserPlatformLocation, DomAdapter, LOCATION_INITIALIZED, Location, LocationStrategy, PathLocationStrategy, PlatformLocation, getDOM, joinWithSlash, normalizeQueryParams, setRootDomAdapter };\n//# sourceMappingURL=location-Dq4mJT-A.mjs.map", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "Injectable", "Optional", "Inject", "ɵɵinject", "__inject", "Subject", "DOCUMENT", "_DOM", "getDOM", "setRootDomAdapter", "adapter", "DomAdapter", "PlatformLocation", "historyGo", "relativePosition", "Error", "ngDevMode", "_PlatformLocation", "_defineProperty", "_PlatformLocation_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "BrowserPlatformLocation", "providedIn", "ɵsetClassMetadata", "type", "args", "useFactory", "LOCATION_INITIALIZED", "constructor", "_location", "window", "location", "_history", "history", "getBaseHrefFromDOM", "getBaseHref", "_doc", "onPopState", "fn", "getGlobalEventTarget", "addEventListener", "removeEventListener", "onHashChange", "href", "protocol", "hostname", "port", "pathname", "search", "hash", "newPath", "pushState", "state", "title", "url", "replaceState", "forward", "back", "go", "getState", "_BrowserPlatformLocation", "_BrowserPlatformLocation_Factory", "joinWithSlash", "start", "end", "endsWith", "startsWith", "slice", "stripTrailingSlash", "pathEndIdx", "normalizeQueryParams", "params", "LocationStrategy", "_LocationStrategy", "_LocationStrategy_Factory", "PathLocationStrategy", "APP_BASE_HREF", "_platformLocation", "_ref", "_ref2", "_inject$location", "_baseHref", "origin", "ngOnDestroy", "_removeListenerFns", "length", "pop", "push", "prepareExternalUrl", "internal", "path", "includeHash", "queryParams", "externalUrl", "_this$_platformLocati", "_this$_platformLocati2", "call", "_PathLocationStrategy", "_PathLocationStrategy_Factory", "ɵfac", "undefined", "decorators", "Location", "locationStrategy", "_locationStrategy", "baseHref", "_basePath", "_strip<PERSON><PERSON><PERSON>", "_stripIndexHtml", "ev", "_subject", "next", "_this$_urlChangeSubsc", "_urlChangeSubscription", "unsubscribe", "_urlChangeListeners", "normalize", "isCurrentPathEqualTo", "query", "_stripBasePath", "_notifyUrlChangeListeners", "_this$_locationStrate", "_this$_locationStrate2", "onUrlChange", "_this$_urlChangeSubsc2", "subscribe", "v", "fnIndex", "indexOf", "splice", "_this$_urlChangeSubsc3", "for<PERSON>ach", "onNext", "onThrow", "onReturn", "error", "complete", "_Location", "_Location_Factory", "createLocation", "basePath", "strippedUrl", "substring", "includes", "replace", "isAbsoluteUrl", "RegExp", "test", "split"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/common/fesm2022/location-Dq4mJT-A.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injectable, Optional, Inject, ɵɵinject as __inject } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { DOCUMENT } from './dom_tokens-rA0ACyx7.mjs';\n\nlet _DOM = null;\nfunction getDOM() {\n    return _DOM;\n}\nfunction setRootDomAdapter(adapter) {\n    _DOM ??= adapter;\n}\n/**\n * Provides DOM operations in an environment-agnostic way.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass DomAdapter {\n}\n\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * `PlatformLocation` encapsulates all calls to DOM APIs, which allows the Router to be\n * platform-agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that Angular supports. For example, `@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `@angular/platform-server` provides\n * one suitable for use with server-side rendering.\n *\n * The `PlatformLocation` class is used directly by all implementations of {@link LocationStrategy}\n * when they need to interact with the DOM APIs like pushState, popState, etc.\n *\n * {@link LocationStrategy} in turn is used by the {@link Location} service which is used directly\n * by the {@link /api/router/Router Router} in order to navigate between routes. Since all interactions between\n * {@link /api/router/Router Router} /\n * {@link Location} / {@link LocationStrategy} and DOM APIs flow through the `PlatformLocation`\n * class, they are all platform-agnostic.\n *\n * @publicApi\n */\nclass PlatformLocation {\n    historyGo(relativePosition) {\n        throw new Error(ngDevMode ? 'Not implemented' : '');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PlatformLocation, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PlatformLocation, providedIn: 'platform', useFactory: () => inject(BrowserPlatformLocation) });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PlatformLocation, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'platform', useFactory: () => inject(BrowserPlatformLocation) }]\n        }] });\n/**\n * @description\n * Indicates when a location is initialized.\n *\n * @publicApi\n */\nconst LOCATION_INITIALIZED = new InjectionToken(ngDevMode ? 'Location Initialized' : '');\n/**\n * `PlatformLocation` encapsulates all of the direct calls to platform APIs.\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * @publicApi\n */\nclass BrowserPlatformLocation extends PlatformLocation {\n    _location;\n    _history;\n    _doc = inject(DOCUMENT);\n    constructor() {\n        super();\n        this._location = window.location;\n        this._history = window.history;\n    }\n    getBaseHrefFromDOM() {\n        return getDOM().getBaseHref(this._doc);\n    }\n    onPopState(fn) {\n        const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n        window.addEventListener('popstate', fn, false);\n        return () => window.removeEventListener('popstate', fn);\n    }\n    onHashChange(fn) {\n        const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n        window.addEventListener('hashchange', fn, false);\n        return () => window.removeEventListener('hashchange', fn);\n    }\n    get href() {\n        return this._location.href;\n    }\n    get protocol() {\n        return this._location.protocol;\n    }\n    get hostname() {\n        return this._location.hostname;\n    }\n    get port() {\n        return this._location.port;\n    }\n    get pathname() {\n        return this._location.pathname;\n    }\n    get search() {\n        return this._location.search;\n    }\n    get hash() {\n        return this._location.hash;\n    }\n    set pathname(newPath) {\n        this._location.pathname = newPath;\n    }\n    pushState(state, title, url) {\n        this._history.pushState(state, title, url);\n    }\n    replaceState(state, title, url) {\n        this._history.replaceState(state, title, url);\n    }\n    forward() {\n        this._history.forward();\n    }\n    back() {\n        this._history.back();\n    }\n    historyGo(relativePosition = 0) {\n        this._history.go(relativePosition);\n    }\n    getState() {\n        return this._history.state;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserPlatformLocation, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserPlatformLocation, providedIn: 'platform', useFactory: () => new BrowserPlatformLocation() });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserPlatformLocation, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'platform',\n                    useFactory: () => new BrowserPlatformLocation(),\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Joins two parts of a URL with a slash if needed.\n *\n * @param start  URL string\n * @param end    URL string\n *\n *\n * @returns The joined URL string.\n */\nfunction joinWithSlash(start, end) {\n    // If `start` is an empty string, return `end` as the result.\n    if (!start)\n        return end;\n    // If `end` is an empty string, return `start` as the result.\n    if (!end)\n        return start;\n    // If `start` ends with a slash, remove the leading slash from `end`.\n    if (start.endsWith('/')) {\n        return end.startsWith('/') ? start + end.slice(1) : start + end;\n    }\n    // If `start` doesn't end with a slash, add one if `end` doesn't start with a slash.\n    return end.startsWith('/') ? start + end : `${start}/${end}`;\n}\n/**\n * Removes a trailing slash from a URL string if needed.\n * Looks for the first occurrence of either `#`, `?`, or the end of the\n * line as `/` characters and removes the trailing slash if one exists.\n *\n * @param url URL string.\n *\n * @returns The URL string, modified if needed.\n */\nfunction stripTrailingSlash(url) {\n    // Find the index of the first occurrence of `#`, `?`, or the end of the string.\n    // This marks the start of the query string, fragment, or the end of the URL path.\n    const pathEndIdx = url.search(/#|\\?|$/);\n    // Check if the character before `pathEndIdx` is a trailing slash.\n    // If it is, remove the trailing slash and return the modified URL.\n    // Otherwise, return the URL as is.\n    return url[pathEndIdx - 1] === '/' ? url.slice(0, pathEndIdx - 1) + url.slice(pathEndIdx) : url;\n}\n/**\n * Normalizes URL parameters by prepending with `?` if needed.\n *\n * @param  params String of URL parameters.\n *\n * @returns The normalized URL parameters string.\n */\nfunction normalizeQueryParams(params) {\n    return params && params[0] !== '?' ? `?${params}` : params;\n}\n\n/**\n * Enables the `Location` service to read route state from the browser's URL.\n * Angular provides two strategies:\n * `HashLocationStrategy` and `PathLocationStrategy`.\n *\n * Applications should use the `Router` or `Location` services to\n * interact with application route state.\n *\n * For instance, `HashLocationStrategy` produces URLs like\n * <code class=\"no-auto-link\">http://example.com/#/foo</code>,\n * and `PathLocationStrategy` produces\n * <code class=\"no-auto-link\">http://example.com/foo</code> as an equivalent URL.\n *\n * See these two classes for more.\n *\n * @publicApi\n */\nclass LocationStrategy {\n    historyGo(relativePosition) {\n        throw new Error(ngDevMode ? 'Not implemented' : '');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: LocationStrategy, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: LocationStrategy, providedIn: 'root', useFactory: () => inject(PathLocationStrategy) });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: LocationStrategy, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: () => inject(PathLocationStrategy) }]\n        }] });\n/**\n * A predefined DI token for the base href\n * to be used with the `PathLocationStrategy`.\n * The base href is the URL prefix that should be preserved when generating\n * and recognizing URLs.\n *\n * @usageNotes\n *\n * The following example shows how to use this token to configure the root app injector\n * with a base href value, so that the DI framework can supply the dependency anywhere in the app.\n *\n * ```ts\n * import {NgModule} from '@angular/core';\n * import {APP_BASE_HREF} from '@angular/common';\n *\n * @NgModule({\n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n *\n * @publicApi\n */\nconst APP_BASE_HREF = new InjectionToken(ngDevMode ? 'appBaseHref' : '');\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n *\n * If you're using `PathLocationStrategy`, you may provide a {@link APP_BASE_HREF}\n * or add a `<base href>` element to the document to override the default.\n *\n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app/'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`. To ensure all relative URIs resolve correctly,\n * the `<base href>` and/or `APP_BASE_HREF` should end with a `/`.\n *\n * Similarly, if you add `<base href='/my/app/'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n *\n * Note that when using `PathLocationStrategy`, neither the query nor\n * the fragment in the `<base href>` will be preserved, as outlined\n * by the [RFC](https://tools.ietf.org/html/rfc3986#section-5.2.2).\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\nclass PathLocationStrategy extends LocationStrategy {\n    _platformLocation;\n    _baseHref;\n    _removeListenerFns = [];\n    constructor(_platformLocation, href) {\n        super();\n        this._platformLocation = _platformLocation;\n        this._baseHref =\n            href ??\n                this._platformLocation.getBaseHrefFromDOM() ??\n                inject(DOCUMENT).location?.origin ??\n                '';\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        while (this._removeListenerFns.length) {\n            this._removeListenerFns.pop()();\n        }\n    }\n    onPopState(fn) {\n        this._removeListenerFns.push(this._platformLocation.onPopState(fn), this._platformLocation.onHashChange(fn));\n    }\n    getBaseHref() {\n        return this._baseHref;\n    }\n    prepareExternalUrl(internal) {\n        return joinWithSlash(this._baseHref, internal);\n    }\n    path(includeHash = false) {\n        const pathname = this._platformLocation.pathname + normalizeQueryParams(this._platformLocation.search);\n        const hash = this._platformLocation.hash;\n        return hash && includeHash ? `${pathname}${hash}` : pathname;\n    }\n    pushState(state, title, url, queryParams) {\n        const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n        this._platformLocation.pushState(state, title, externalUrl);\n    }\n    replaceState(state, title, url, queryParams) {\n        const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n        this._platformLocation.replaceState(state, title, externalUrl);\n    }\n    forward() {\n        this._platformLocation.forward();\n    }\n    back() {\n        this._platformLocation.back();\n    }\n    getState() {\n        return this._platformLocation.getState();\n    }\n    historyGo(relativePosition = 0) {\n        this._platformLocation.historyGo?.(relativePosition);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PathLocationStrategy, deps: [{ token: PlatformLocation }, { token: APP_BASE_HREF, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PathLocationStrategy, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PathLocationStrategy, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: PlatformLocation }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [APP_BASE_HREF]\n                }] }] });\n\n/**\n * @description\n *\n * A service that applications can use to interact with a browser's URL.\n *\n * Depending on the `LocationStrategy` used, `Location` persists\n * to the URL's path or the URL's hash segment.\n *\n * @usageNotes\n *\n * It's better to use the `Router.navigate()` service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n *\n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\nclass Location {\n    /** @internal */\n    _subject = new Subject();\n    /** @internal */\n    _basePath;\n    /** @internal */\n    _locationStrategy;\n    /** @internal */\n    _urlChangeListeners = [];\n    /** @internal */\n    _urlChangeSubscription = null;\n    constructor(locationStrategy) {\n        this._locationStrategy = locationStrategy;\n        const baseHref = this._locationStrategy.getBaseHref();\n        // Note: This class's interaction with base HREF does not fully follow the rules\n        // outlined in the spec https://www.freesoft.org/CIE/RFC/1808/18.htm.\n        // Instead of trying to fix individual bugs with more and more code, we should\n        // investigate using the URL constructor and providing the base as a second\n        // argument.\n        // https://developer.mozilla.org/en-US/docs/Web/API/URL/URL#parameters\n        this._basePath = _stripOrigin(stripTrailingSlash(_stripIndexHtml(baseHref)));\n        this._locationStrategy.onPopState((ev) => {\n            this._subject.next({\n                'url': this.path(true),\n                'pop': true,\n                'state': ev.state,\n                'type': ev.type,\n            });\n        });\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeListeners = [];\n    }\n    /**\n     * Normalizes the URL path for this location.\n     *\n     * @param includeHash True to include an anchor fragment in the path.\n     *\n     * @returns The normalized URL path.\n     */\n    // TODO: vsavkin. Remove the boolean flag and always include hash once the deprecated router is\n    // removed.\n    path(includeHash = false) {\n        return this.normalize(this._locationStrategy.path(includeHash));\n    }\n    /**\n     * Reports the current state of the location history.\n     * @returns The current value of the `history.state` object.\n     */\n    getState() {\n        return this._locationStrategy.getState();\n    }\n    /**\n     * Normalizes the given path and compares to the current normalized path.\n     *\n     * @param path The given URL path.\n     * @param query Query parameters.\n     *\n     * @returns True if the given URL path is equal to the current normalized path, false\n     * otherwise.\n     */\n    isCurrentPathEqualTo(path, query = '') {\n        return this.path() == this.normalize(path + normalizeQueryParams(query));\n    }\n    /**\n     * Normalizes a URL path by stripping any trailing slashes.\n     *\n     * @param url String representing a URL.\n     *\n     * @returns The normalized URL string.\n     */\n    normalize(url) {\n        return Location.stripTrailingSlash(_stripBasePath(this._basePath, _stripIndexHtml(url)));\n    }\n    /**\n     * Normalizes an external URL path.\n     * If the given URL doesn't begin with a leading slash (`'/'`), adds one\n     * before normalizing. Adds a hash if `HashLocationStrategy` is\n     * in use, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n     *\n     * @param url String representing a URL.\n     *\n     * @returns  A normalized platform-specific URL.\n     */\n    prepareExternalUrl(url) {\n        if (url && url[0] !== '/') {\n            url = '/' + url;\n        }\n        return this._locationStrategy.prepareExternalUrl(url);\n    }\n    // TODO: rename this method to pushState\n    /**\n     * Changes the browser's URL to a normalized version of a given URL, and pushes a\n     * new item onto the platform's history.\n     *\n     * @param path  URL path to normalize.\n     * @param query Query parameters.\n     * @param state Location history state.\n     *\n     */\n    go(path, query = '', state = null) {\n        this._locationStrategy.pushState(state, '', path, query);\n        this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n    }\n    /**\n     * Changes the browser's URL to a normalized version of the given URL, and replaces\n     * the top item on the platform's history stack.\n     *\n     * @param path  URL path to normalize.\n     * @param query Query parameters.\n     * @param state Location history state.\n     */\n    replaceState(path, query = '', state = null) {\n        this._locationStrategy.replaceState(state, '', path, query);\n        this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n    }\n    /**\n     * Navigates forward in the platform's history.\n     */\n    forward() {\n        this._locationStrategy.forward();\n    }\n    /**\n     * Navigates back in the platform's history.\n     */\n    back() {\n        this._locationStrategy.back();\n    }\n    /**\n     * Navigate to a specific page from session history, identified by its relative position to the\n     * current page.\n     *\n     * @param relativePosition  Position of the target page in the history relative to the current\n     *     page.\n     * A negative value moves backwards, a positive value moves forwards, e.g. `location.historyGo(2)`\n     * moves forward two pages and `location.historyGo(-2)` moves back two pages. When we try to go\n     * beyond what's stored in the history session, we stay in the current page. Same behaviour occurs\n     * when `relativePosition` equals 0.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/History_API#Moving_to_a_specific_point_in_history\n     */\n    historyGo(relativePosition = 0) {\n        this._locationStrategy.historyGo?.(relativePosition);\n    }\n    /**\n     * Registers a URL change listener. Use to catch updates performed by the Angular\n     * framework that are not detectible through \"popstate\" or \"hashchange\" events.\n     *\n     * @param fn The change handler function, which take a URL and a location history state.\n     * @returns A function that, when executed, unregisters a URL change listener.\n     */\n    onUrlChange(fn) {\n        this._urlChangeListeners.push(fn);\n        this._urlChangeSubscription ??= this.subscribe((v) => {\n            this._notifyUrlChangeListeners(v.url, v.state);\n        });\n        return () => {\n            const fnIndex = this._urlChangeListeners.indexOf(fn);\n            this._urlChangeListeners.splice(fnIndex, 1);\n            if (this._urlChangeListeners.length === 0) {\n                this._urlChangeSubscription?.unsubscribe();\n                this._urlChangeSubscription = null;\n            }\n        };\n    }\n    /** @internal */\n    _notifyUrlChangeListeners(url = '', state) {\n        this._urlChangeListeners.forEach((fn) => fn(url, state));\n    }\n    /**\n     * Subscribes to the platform's `popState` events.\n     *\n     * Note: `Location.go()` does not trigger the `popState` event in the browser. Use\n     * `Location.onUrlChange()` to subscribe to URL changes instead.\n     *\n     * @param value Event that is triggered when the state history changes.\n     * @param exception The exception to throw.\n     *\n     * @see [onpopstate](https://developer.mozilla.org/en-US/docs/Web/API/WindowEventHandlers/onpopstate)\n     *\n     * @returns Subscribed events.\n     */\n    subscribe(onNext, onThrow, onReturn) {\n        return this._subject.subscribe({\n            next: onNext,\n            error: onThrow ?? undefined,\n            complete: onReturn ?? undefined,\n        });\n    }\n    /**\n     * Normalizes URL parameters by prepending with `?` if needed.\n     *\n     * @param  params String of URL parameters.\n     *\n     * @returns The normalized URL parameters string.\n     */\n    static normalizeQueryParams = normalizeQueryParams;\n    /**\n     * Joins two parts of a URL with a slash if needed.\n     *\n     * @param start  URL string\n     * @param end    URL string\n     *\n     *\n     * @returns The joined URL string.\n     */\n    static joinWithSlash = joinWithSlash;\n    /**\n     * Removes a trailing slash from a URL string if needed.\n     * Looks for the first occurrence of either `#`, `?`, or the end of the\n     * line as `/` characters and removes the trailing slash if one exists.\n     *\n     * @param url URL string.\n     *\n     * @returns The URL string, modified if needed.\n     */\n    static stripTrailingSlash = stripTrailingSlash;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: Location, deps: [{ token: LocationStrategy }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: Location, providedIn: 'root', useFactory: createLocation });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: Location, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                    // See #23917\n                    useFactory: createLocation,\n                }]\n        }], ctorParameters: () => [{ type: LocationStrategy }] });\nfunction createLocation() {\n    return new Location(__inject(LocationStrategy));\n}\nfunction _stripBasePath(basePath, url) {\n    if (!basePath || !url.startsWith(basePath)) {\n        return url;\n    }\n    const strippedUrl = url.substring(basePath.length);\n    if (strippedUrl === '' || ['/', ';', '?', '#'].includes(strippedUrl[0])) {\n        return strippedUrl;\n    }\n    return url;\n}\nfunction _stripIndexHtml(url) {\n    return url.replace(/\\/index.html$/, '');\n}\nfunction _stripOrigin(baseHref) {\n    // DO NOT REFACTOR! Previously, this check looked like this:\n    // `/^(https?:)?\\/\\//.test(baseHref)`, but that resulted in\n    // syntactically incorrect code after Closure Compiler minification.\n    // This was likely caused by a bug in Closure Compiler, but\n    // for now, the check is rewritten to use `new RegExp` instead.\n    const isAbsoluteUrl = new RegExp('^(https?:)?//').test(baseHref);\n    if (isAbsoluteUrl) {\n        const [, pathname] = baseHref.split(/\\/\\/[^\\/]+/);\n        return pathname;\n    }\n    return baseHref;\n}\n\nexport { APP_BASE_HREF, BrowserPlatformLocation, DomAdapter, LOCATION_INITIALIZED, Location, LocationStrategy, PathLocationStrategy, PlatformLocation, getDOM, joinWithSlash, normalizeQueryParams, setRootDomAdapter };\n//# sourceMappingURL=location-Dq4mJT-A.mjs.map\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,IAAIC,QAAQ,QAAQ,eAAe;AAC1G,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,2BAA2B;AAEpD,IAAIC,IAAI,GAAG,IAAI;AACf,SAASC,MAAMA,CAAA,EAAG;EACd,OAAOD,IAAI;AACf;AACA,SAASE,iBAAiBA,CAACC,OAAO,EAAE;EAChCH,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAJA,IAAI,GAAKG,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;;AAGjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBC,SAASA,CAACC,gBAAgB,EAAE;IACxB,MAAM,IAAIC,KAAK,CAACC,SAAS,GAAG,iBAAiB,GAAG,EAAE,CAAC;EACvD;AAGJ;AAACC,iBAAA,GANKL,gBAAgB;AAAAM,eAAA,CAAhBN,gBAAgB,wBAAAO,0BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAIkFR,iBAAgB;AAAA;AAAAM,eAAA,CAJlHN,gBAAgB,+BAO4Df,EAAE,CAAAwB,kBAAA;EAAAC,KAAA,EAFwBV,iBAAgB;EAAAW,OAAA,EAAAA,CAAA,MAAsC,MAAMxB,MAAM,CAACyB,uBAAuB,CAAC;EAAAC,UAAA,EAA7D;AAAU;AAEpJ;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KAAkFnB,EAAE,CAAA6B,iBAAA,CAAQd,gBAAgB,EAAc,CAAC;IAC/Ge,IAAI,EAAE3B,UAAU;IAChB4B,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,UAAU;MAAEI,UAAU,EAAEA,CAAA,KAAM9B,MAAM,CAACyB,uBAAuB;IAAE,CAAC;EACxF,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,oBAAoB,GAAG,IAAIhC,cAAc,CAACkB,SAAS,GAAG,sBAAsB,GAAG,EAAE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,uBAAuB,SAASZ,gBAAgB,CAAC;EAInDmB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IAACb,eAAA;IAAAA,eAAA;IAAAA,eAAA,eAFLnB,MAAM,CAACO,QAAQ,CAAC;IAGnB,IAAI,CAAC0B,SAAS,GAAGC,MAAM,CAACC,QAAQ;IAChC,IAAI,CAACC,QAAQ,GAAGF,MAAM,CAACG,OAAO;EAClC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,OAAO7B,MAAM,CAAC,CAAC,CAAC8B,WAAW,CAAC,IAAI,CAACC,IAAI,CAAC;EAC1C;EACAC,UAAUA,CAACC,EAAE,EAAE;IACX,MAAMR,MAAM,GAAGzB,MAAM,CAAC,CAAC,CAACkC,oBAAoB,CAAC,IAAI,CAACH,IAAI,EAAE,QAAQ,CAAC;IACjEN,MAAM,CAACU,gBAAgB,CAAC,UAAU,EAAEF,EAAE,EAAE,KAAK,CAAC;IAC9C,OAAO,MAAMR,MAAM,CAACW,mBAAmB,CAAC,UAAU,EAAEH,EAAE,CAAC;EAC3D;EACAI,YAAYA,CAACJ,EAAE,EAAE;IACb,MAAMR,MAAM,GAAGzB,MAAM,CAAC,CAAC,CAACkC,oBAAoB,CAAC,IAAI,CAACH,IAAI,EAAE,QAAQ,CAAC;IACjEN,MAAM,CAACU,gBAAgB,CAAC,YAAY,EAAEF,EAAE,EAAE,KAAK,CAAC;IAChD,OAAO,MAAMR,MAAM,CAACW,mBAAmB,CAAC,YAAY,EAAEH,EAAE,CAAC;EAC7D;EACA,IAAIK,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACd,SAAS,CAACc,IAAI;EAC9B;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACf,SAAS,CAACe,QAAQ;EAClC;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAChB,SAAS,CAACgB,QAAQ;EAClC;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACjB,SAAS,CAACiB,IAAI;EAC9B;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAClB,SAAS,CAACkB,QAAQ;EAClC;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACnB,SAAS,CAACmB,MAAM;EAChC;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACpB,SAAS,CAACoB,IAAI;EAC9B;EACA,IAAIF,QAAQA,CAACG,OAAO,EAAE;IAClB,IAAI,CAACrB,SAAS,CAACkB,QAAQ,GAAGG,OAAO;EACrC;EACAC,SAASA,CAACC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;IACzB,IAAI,CAACtB,QAAQ,CAACmB,SAAS,CAACC,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC;EAC9C;EACAC,YAAYA,CAACH,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;IAC5B,IAAI,CAACtB,QAAQ,CAACuB,YAAY,CAACH,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC;EACjD;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,CAACxB,QAAQ,CAACwB,OAAO,CAAC,CAAC;EAC3B;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACzB,QAAQ,CAACyB,IAAI,CAAC,CAAC;EACxB;EACA/C,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,IAAI,CAACqB,QAAQ,CAAC0B,EAAE,CAAC/C,gBAAgB,CAAC;EACtC;EACAgD,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC3B,QAAQ,CAACoB,KAAK;EAC9B;AAGJ;AAACQ,wBAAA,GAlEKvC,uBAAuB;AAAAN,eAAA,CAAvBM,uBAAuB,wBAAAwC,iCAAA5C,iBAAA;EAAA,YAAAA,iBAAA,IAgE2EI,wBAAuB;AAAA;AAAAN,eAAA,CAhEzHM,uBAAuB,+BAlBqD3B,EAAE,CAAAwB,kBAAA;EAAAC,KAAA,EAmFwBE,wBAAuB;EAAAD,OAAA,EAAAA,CAAA,MAAsC,MAAM,IAAIC,wBAAuB,CAAC,CAAC;EAAAC,UAAA,EAA3D;AAAU;AAE3J;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KArFkFnB,EAAE,CAAA6B,iBAAA,CAqFQF,uBAAuB,EAAc,CAAC;IACtHG,IAAI,EAAE3B,UAAU;IAChB4B,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,UAAU;MACtBI,UAAU,EAAEA,CAAA,KAAM,IAAIL,uBAAuB,CAAC;IAClD,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyC,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/B;EACA,IAAI,CAACD,KAAK,EACN,OAAOC,GAAG;EACd;EACA,IAAI,CAACA,GAAG,EACJ,OAAOD,KAAK;EAChB;EACA,IAAIA,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;IACrB,OAAOD,GAAG,CAACE,UAAU,CAAC,GAAG,CAAC,GAAGH,KAAK,GAAGC,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,GAAGJ,KAAK,GAAGC,GAAG;EACnE;EACA;EACA,OAAOA,GAAG,CAACE,UAAU,CAAC,GAAG,CAAC,GAAGH,KAAK,GAAGC,GAAG,GAAG,GAAGD,KAAK,IAAIC,GAAG,EAAE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,kBAAkBA,CAACd,GAAG,EAAE;EAC7B;EACA;EACA,MAAMe,UAAU,GAAGf,GAAG,CAACN,MAAM,CAAC,QAAQ,CAAC;EACvC;EACA;EACA;EACA,OAAOM,GAAG,CAACe,UAAU,GAAG,CAAC,CAAC,KAAK,GAAG,GAAGf,GAAG,CAACa,KAAK,CAAC,CAAC,EAAEE,UAAU,GAAG,CAAC,CAAC,GAAGf,GAAG,CAACa,KAAK,CAACE,UAAU,CAAC,GAAGf,GAAG;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,oBAAoBA,CAACC,MAAM,EAAE;EAClC,OAAOA,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,IAAIA,MAAM,EAAE,GAAGA,MAAM;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB9D,SAASA,CAACC,gBAAgB,EAAE;IACxB,MAAM,IAAIC,KAAK,CAACC,SAAS,GAAG,iBAAiB,GAAG,EAAE,CAAC;EACvD;AAGJ;AAAC4D,iBAAA,GANKD,gBAAgB;AAAAzD,eAAA,CAAhByD,gBAAgB,wBAAAE,0BAAAzD,iBAAA;EAAA,YAAAA,iBAAA,IAIkFuD,iBAAgB;AAAA;AAAAzD,eAAA,CAJlHyD,gBAAgB,+BAlK4D9E,EAAE,CAAAwB,kBAAA;EAAAC,KAAA,EAuKwBqD,iBAAgB;EAAApD,OAAA,EAAAA,CAAA,MAAkC,MAAMxB,MAAM,CAAC+E,oBAAoB,CAAC;EAAArD,UAAA,EAAtD;AAAM;AAEhJ;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KAzKkFnB,EAAE,CAAA6B,iBAAA,CAyKQiD,gBAAgB,EAAc,CAAC;IAC/GhD,IAAI,EAAE3B,UAAU;IAChB4B,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,MAAM;MAAEI,UAAU,EAAEA,CAAA,KAAM9B,MAAM,CAAC+E,oBAAoB;IAAE,CAAC;EACjF,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAIjF,cAAc,CAACkB,SAAS,GAAG,aAAa,GAAG,EAAE,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8D,oBAAoB,SAASH,gBAAgB,CAAC;EAIhD5C,WAAWA,CAACiD,iBAAiB,EAAElC,IAAI,EAAE;IAAA,IAAAmC,IAAA,EAAAC,KAAA,EAAAC,gBAAA;IACjC,KAAK,CAAC,CAAC;IAACjE,eAAA;IAAAA,eAAA;IAAAA,eAAA,6BAFS,EAAE;IAGnB,IAAI,CAAC8D,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACI,SAAS,IAAAH,IAAA,IAAAC,KAAA,GACVpC,IAAI,aAAJA,IAAI,cAAJA,IAAI,GACA,IAAI,CAACkC,iBAAiB,CAAC3C,kBAAkB,CAAC,CAAC,cAAA6C,KAAA,cAAAA,KAAA,IAAAC,gBAAA,GAC3CpF,MAAM,CAACO,QAAQ,CAAC,CAAC4B,QAAQ,cAAAiD,gBAAA,uBAAzBA,gBAAA,CAA2BE,MAAM,cAAAJ,IAAA,cAAAA,IAAA,GACjC,EAAE;EACd;EACA;EACAK,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,kBAAkB,CAACC,MAAM,EAAE;MACnC,IAAI,CAACD,kBAAkB,CAACE,GAAG,CAAC,CAAC,CAAC,CAAC;IACnC;EACJ;EACAjD,UAAUA,CAACC,EAAE,EAAE;IACX,IAAI,CAAC8C,kBAAkB,CAACG,IAAI,CAAC,IAAI,CAACV,iBAAiB,CAACxC,UAAU,CAACC,EAAE,CAAC,EAAE,IAAI,CAACuC,iBAAiB,CAACnC,YAAY,CAACJ,EAAE,CAAC,CAAC;EAChH;EACAH,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC8C,SAAS;EACzB;EACAO,kBAAkBA,CAACC,QAAQ,EAAE;IACzB,OAAO3B,aAAa,CAAC,IAAI,CAACmB,SAAS,EAAEQ,QAAQ,CAAC;EAClD;EACAC,IAAIA,CAACC,WAAW,GAAG,KAAK,EAAE;IACtB,MAAM5C,QAAQ,GAAG,IAAI,CAAC8B,iBAAiB,CAAC9B,QAAQ,GAAGuB,oBAAoB,CAAC,IAAI,CAACO,iBAAiB,CAAC7B,MAAM,CAAC;IACtG,MAAMC,IAAI,GAAG,IAAI,CAAC4B,iBAAiB,CAAC5B,IAAI;IACxC,OAAOA,IAAI,IAAI0C,WAAW,GAAG,GAAG5C,QAAQ,GAAGE,IAAI,EAAE,GAAGF,QAAQ;EAChE;EACAI,SAASA,CAACC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEsC,WAAW,EAAE;IACtC,MAAMC,WAAW,GAAG,IAAI,CAACL,kBAAkB,CAAClC,GAAG,GAAGgB,oBAAoB,CAACsB,WAAW,CAAC,CAAC;IACpF,IAAI,CAACf,iBAAiB,CAAC1B,SAAS,CAACC,KAAK,EAAEC,KAAK,EAAEwC,WAAW,CAAC;EAC/D;EACAtC,YAAYA,CAACH,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEsC,WAAW,EAAE;IACzC,MAAMC,WAAW,GAAG,IAAI,CAACL,kBAAkB,CAAClC,GAAG,GAAGgB,oBAAoB,CAACsB,WAAW,CAAC,CAAC;IACpF,IAAI,CAACf,iBAAiB,CAACtB,YAAY,CAACH,KAAK,EAAEC,KAAK,EAAEwC,WAAW,CAAC;EAClE;EACArC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACqB,iBAAiB,CAACrB,OAAO,CAAC,CAAC;EACpC;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACoB,iBAAiB,CAACpB,IAAI,CAAC,CAAC;EACjC;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACkB,iBAAiB,CAAClB,QAAQ,CAAC,CAAC;EAC5C;EACAjD,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAAA,IAAAmF,qBAAA,EAAAC,sBAAA;IAC5B,CAAAD,qBAAA,IAAAC,sBAAA,OAAI,CAAClB,iBAAiB,EAACnE,SAAS,cAAAoF,qBAAA,eAAhCA,qBAAA,CAAAE,IAAA,CAAAD,sBAAA,EAAmCpF,gBAAgB,CAAC;EACxD;AAGJ;AAACsF,qBAAA,GAvDKtB,oBAAoB;AAAA5D,eAAA,CAApB4D,oBAAoB,wBAAAuB,8BAAAjF,iBAAA;EAAA,YAAAA,iBAAA,IAqD8E0D,qBAAoB,EAzR1CjF,EAAE,CAAAM,QAAA,CAyR0DS,gBAAgB,GAzR5Ef,EAAE,CAAAM,QAAA,CAyRuF4E,aAAa;AAAA;AAAA7D,eAAA,CArDlL4D,oBAAoB,+BApOwDjF,EAAE,CAAAwB,kBAAA;EAAAC,KAAA,EA0RwBwD,qBAAoB;EAAAvD,OAAA,EAApBuD,qBAAoB,CAAAwB,IAAA;EAAA7E,UAAA,EAAc;AAAM;AAEpJ;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KA5RkFnB,EAAE,CAAA6B,iBAAA,CA4RQoD,oBAAoB,EAAc,CAAC;IACnHnD,IAAI,EAAE3B,UAAU;IAChB4B,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEE,IAAI,EAAEf;EAAiB,CAAC,EAAE;IAAEe,IAAI,EAAE4E,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC3E7E,IAAI,EAAE1B;IACV,CAAC,EAAE;MACC0B,IAAI,EAAEzB,MAAM;MACZ0B,IAAI,EAAE,CAACmD,aAAa;IACxB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0B,QAAQ,CAAC;EAWX1E,WAAWA,CAAC2E,gBAAgB,EAAE;IAV9B;IAAAxF,eAAA,mBACW,IAAIb,OAAO,CAAC,CAAC;IACxB;IAAAa,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,8BACsB,EAAE;IACxB;IAAAA,eAAA,iCACyB,IAAI;IAEzB,IAAI,CAACyF,iBAAiB,GAAGD,gBAAgB;IACzC,MAAME,QAAQ,GAAG,IAAI,CAACD,iBAAiB,CAACrE,WAAW,CAAC,CAAC;IACrD;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACuE,SAAS,GAAGC,YAAY,CAACvC,kBAAkB,CAACwC,eAAe,CAACH,QAAQ,CAAC,CAAC,CAAC;IAC5E,IAAI,CAACD,iBAAiB,CAACnE,UAAU,CAAEwE,EAAE,IAAK;MACtC,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;QACf,KAAK,EAAE,IAAI,CAACrB,IAAI,CAAC,IAAI,CAAC;QACtB,KAAK,EAAE,IAAI;QACX,OAAO,EAAEmB,EAAE,CAACzD,KAAK;QACjB,MAAM,EAAEyD,EAAE,CAACrF;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACA2D,WAAWA,CAAA,EAAG;IAAA,IAAA6B,qBAAA;IACV,CAAAA,qBAAA,OAAI,CAACC,sBAAsB,cAAAD,qBAAA,eAA3BA,qBAAA,CAA6BE,WAAW,CAAC,CAAC;IAC1C,IAAI,CAACC,mBAAmB,GAAG,EAAE;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;EACA;EACAzB,IAAIA,CAACC,WAAW,GAAG,KAAK,EAAE;IACtB,OAAO,IAAI,CAACyB,SAAS,CAAC,IAAI,CAACZ,iBAAiB,CAACd,IAAI,CAACC,WAAW,CAAC,CAAC;EACnE;EACA;AACJ;AACA;AACA;EACIhC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC6C,iBAAiB,CAAC7C,QAAQ,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI0D,oBAAoBA,CAAC3B,IAAI,EAAE4B,KAAK,GAAG,EAAE,EAAE;IACnC,OAAO,IAAI,CAAC5B,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC0B,SAAS,CAAC1B,IAAI,GAAGpB,oBAAoB,CAACgD,KAAK,CAAC,CAAC;EAC5E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIF,SAASA,CAAC9D,GAAG,EAAE;IACX,OAAOgD,QAAQ,CAAClC,kBAAkB,CAACmD,cAAc,CAAC,IAAI,CAACb,SAAS,EAAEE,eAAe,CAACtD,GAAG,CAAC,CAAC,CAAC;EAC5F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkC,kBAAkBA,CAAClC,GAAG,EAAE;IACpB,IAAIA,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACvBA,GAAG,GAAG,GAAG,GAAGA,GAAG;IACnB;IACA,OAAO,IAAI,CAACkD,iBAAiB,CAAChB,kBAAkB,CAAClC,GAAG,CAAC;EACzD;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACII,EAAEA,CAACgC,IAAI,EAAE4B,KAAK,GAAG,EAAE,EAAElE,KAAK,GAAG,IAAI,EAAE;IAC/B,IAAI,CAACoD,iBAAiB,CAACrD,SAAS,CAACC,KAAK,EAAE,EAAE,EAAEsC,IAAI,EAAE4B,KAAK,CAAC;IACxD,IAAI,CAACE,yBAAyB,CAAC,IAAI,CAAChC,kBAAkB,CAACE,IAAI,GAAGpB,oBAAoB,CAACgD,KAAK,CAAC,CAAC,EAAElE,KAAK,CAAC;EACtG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,YAAYA,CAACmC,IAAI,EAAE4B,KAAK,GAAG,EAAE,EAAElE,KAAK,GAAG,IAAI,EAAE;IACzC,IAAI,CAACoD,iBAAiB,CAACjD,YAAY,CAACH,KAAK,EAAE,EAAE,EAAEsC,IAAI,EAAE4B,KAAK,CAAC;IAC3D,IAAI,CAACE,yBAAyB,CAAC,IAAI,CAAChC,kBAAkB,CAACE,IAAI,GAAGpB,oBAAoB,CAACgD,KAAK,CAAC,CAAC,EAAElE,KAAK,CAAC;EACtG;EACA;AACJ;AACA;EACII,OAAOA,CAAA,EAAG;IACN,IAAI,CAACgD,iBAAiB,CAAChD,OAAO,CAAC,CAAC;EACpC;EACA;AACJ;AACA;EACIC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC+C,iBAAiB,CAAC/C,IAAI,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI/C,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAAA,IAAA8G,qBAAA,EAAAC,sBAAA;IAC5B,CAAAD,qBAAA,IAAAC,sBAAA,OAAI,CAAClB,iBAAiB,EAAC9F,SAAS,cAAA+G,qBAAA,eAAhCA,qBAAA,CAAAzB,IAAA,CAAA0B,sBAAA,EAAmC/G,gBAAgB,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIgH,WAAWA,CAACrF,EAAE,EAAE;IAAA,IAAAsF,sBAAA;IACZ,IAAI,CAACT,mBAAmB,CAAC5B,IAAI,CAACjD,EAAE,CAAC;IACjC,CAAAsF,sBAAA,OAAI,CAACX,sBAAsB,cAAAW,sBAAA,cAAAA,sBAAA,GAA3B,IAAI,CAACX,sBAAsB,GAAK,IAAI,CAACY,SAAS,CAAEC,CAAC,IAAK;MAClD,IAAI,CAACN,yBAAyB,CAACM,CAAC,CAACxE,GAAG,EAAEwE,CAAC,CAAC1E,KAAK,CAAC;IAClD,CAAC,CAAC;IACF,OAAO,MAAM;MACT,MAAM2E,OAAO,GAAG,IAAI,CAACZ,mBAAmB,CAACa,OAAO,CAAC1F,EAAE,CAAC;MACpD,IAAI,CAAC6E,mBAAmB,CAACc,MAAM,CAACF,OAAO,EAAE,CAAC,CAAC;MAC3C,IAAI,IAAI,CAACZ,mBAAmB,CAAC9B,MAAM,KAAK,CAAC,EAAE;QAAA,IAAA6C,sBAAA;QACvC,CAAAA,sBAAA,OAAI,CAACjB,sBAAsB,cAAAiB,sBAAA,eAA3BA,sBAAA,CAA6BhB,WAAW,CAAC,CAAC;QAC1C,IAAI,CAACD,sBAAsB,GAAG,IAAI;MACtC;IACJ,CAAC;EACL;EACA;EACAO,yBAAyBA,CAAClE,GAAG,GAAG,EAAE,EAAEF,KAAK,EAAE;IACvC,IAAI,CAAC+D,mBAAmB,CAACgB,OAAO,CAAE7F,EAAE,IAAKA,EAAE,CAACgB,GAAG,EAAEF,KAAK,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIyE,SAASA,CAACO,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IACjC,OAAO,IAAI,CAACxB,QAAQ,CAACe,SAAS,CAAC;MAC3Bd,IAAI,EAAEqB,MAAM;MACZG,KAAK,EAAEF,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAIjC,SAAS;MAC3BoC,QAAQ,EAAEF,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAIlC;IAC1B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AAwBA;AAACqC,SAAA,GA5NKnC,QAAQ;AAAAvF,eAAA,CAARuF,QAAQ,0BAqMoBhC,oBAAoB;AAClD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARIvD,eAAA,CAtMEuF,QAAQ,mBA+MaxC,aAAa;AACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARI/C,eAAA,CAhNEuF,QAAQ,wBAyNkBlC,kBAAkB;AAAArD,eAAA,CAzN5CuF,QAAQ,wBAAAoC,kBAAAzH,iBAAA;EAAA,YAAAA,iBAAA,IA0N0FqF,SAAQ,EA3hB9B5G,EAAE,CAAAM,QAAA,CA2hB8CwE,gBAAgB;AAAA;AAAAzD,eAAA,CA1N5IuF,QAAQ,+BAjUoE5G,EAAE,CAAAwB,kBAAA;EAAAC,KAAA,EA4hBwBmF,SAAQ;EAAAlF,OAAA,EAAAA,CAAA,KAAkCuH,cAAc;EAAArH,UAAA,EAAlC;AAAM;AAExI;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KA9hBkFnB,EAAE,CAAA6B,iBAAA,CA8hBQ+E,QAAQ,EAAc,CAAC;IACvG9E,IAAI,EAAE3B,UAAU;IAChB4B,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,MAAM;MAClB;MACAI,UAAU,EAAEiH;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnH,IAAI,EAAEgD;EAAiB,CAAC,CAAC;AAAA;AAC9D,SAASmE,cAAcA,CAAA,EAAG;EACtB,OAAO,IAAIrC,QAAQ,CAACrG,QAAQ,CAACuE,gBAAgB,CAAC,CAAC;AACnD;AACA,SAAS+C,cAAcA,CAACqB,QAAQ,EAAEtF,GAAG,EAAE;EACnC,IAAI,CAACsF,QAAQ,IAAI,CAACtF,GAAG,CAACY,UAAU,CAAC0E,QAAQ,CAAC,EAAE;IACxC,OAAOtF,GAAG;EACd;EACA,MAAMuF,WAAW,GAAGvF,GAAG,CAACwF,SAAS,CAACF,QAAQ,CAACvD,MAAM,CAAC;EAClD,IAAIwD,WAAW,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACE,QAAQ,CAACF,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;IACrE,OAAOA,WAAW;EACtB;EACA,OAAOvF,GAAG;AACd;AACA,SAASsD,eAAeA,CAACtD,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAAC0F,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;AAC3C;AACA,SAASrC,YAAYA,CAACF,QAAQ,EAAE;EAC5B;EACA;EACA;EACA;EACA;EACA,MAAMwC,aAAa,GAAG,IAAIC,MAAM,CAAC,eAAe,CAAC,CAACC,IAAI,CAAC1C,QAAQ,CAAC;EAChE,IAAIwC,aAAa,EAAE;IACf,MAAM,GAAGlG,QAAQ,CAAC,GAAG0D,QAAQ,CAAC2C,KAAK,CAAC,YAAY,CAAC;IACjD,OAAOrG,QAAQ;EACnB;EACA,OAAO0D,QAAQ;AACnB;AAEA,SAAS7B,aAAa,EAAEvD,uBAAuB,EAAEb,UAAU,EAAEmB,oBAAoB,EAAE2E,QAAQ,EAAE9B,gBAAgB,EAAEG,oBAAoB,EAAElE,gBAAgB,EAAEJ,MAAM,EAAEyD,aAAa,EAAEQ,oBAAoB,EAAEhE,iBAAiB;AACrN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}