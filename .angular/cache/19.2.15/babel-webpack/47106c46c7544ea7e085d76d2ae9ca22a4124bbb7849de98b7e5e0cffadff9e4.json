{"ast": null, "code": "var _InputDateComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-date.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-date.component.scss?ngResource\";\nimport { Component, Input, Optional } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { MatDynamicFormService } from '../mat-dynamic-form.service';\nlet InputDateComponent = (_InputDateComponent = class InputDateComponent {\n  set componentOptions(value) {\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.clearable = value === null || value === void 0 ? void 0 : value.clearable;\n    if (value !== null && value !== void 0 && value.config) {\n      Object.assign(this.config, value.config);\n    }\n  }\n  constructor(service) {\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n    this.config = {};\n    this.destroy$ = new Subject();\n    if (service) {\n      service.timezone.pipe(takeUntil(this.destroy$)).subscribe(timeZone => {\n        this.config.timeZone = timeZone;\n      });\n    }\n  }\n  clear(event) {\n    event.stopPropagation();\n    if (this.control) {\n      this.control.setValue(null);\n    }\n  }\n  isEmpty() {\n    var _this$control;\n    const value = (_this$control = this.control) === null || _this$control === void 0 ? void 0 : _this$control.value;\n    return !value;\n  }\n  ngOnDestroy() {\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n}, _InputDateComponent.ctorParameters = () => [{\n  type: MatDynamicFormService,\n  decorators: [{\n    type: Optional\n  }]\n}], _InputDateComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }]\n}, _InputDateComponent);\nInputDateComponent = __decorate([Component({\n  selector: 'lib-input-date',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], InputDateComponent);\nexport { InputDateComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "Optional", "Subject", "takeUntil", "MatDynamicFormService", "InputDateComponent", "_InputDateComponent", "componentOptions", "value", "title", "clearable", "config", "Object", "assign", "constructor", "service", "id", "readonly", "submitted", "destroy$", "timezone", "pipe", "subscribe", "timeZone", "clear", "event", "stopPropagation", "control", "setValue", "isEmpty", "_this$control", "ngOnDestroy", "next", "undefined", "complete", "ctorParameters", "type", "decorators", "propDecorators", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-date/input-date.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-date.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-date.component.scss?ngResource\";\nimport { Component, Input, Optional } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { MatDynamicFormService } from '../mat-dynamic-form.service';\nlet InputDateComponent = class InputDateComponent {\n    set componentOptions(value) {\n        this.title = value?.title;\n        this.clearable = value?.clearable;\n        if (value?.config) {\n            Object.assign(this.config, value.config);\n        }\n    }\n    constructor(service) {\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n        this.config = {};\n        this.destroy$ = new Subject();\n        if (service) {\n            service.timezone\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(timeZone => {\n                this.config.timeZone = timeZone;\n            });\n        }\n    }\n    clear(event) {\n        event.stopPropagation();\n        if (this.control) {\n            this.control.setValue(null);\n        }\n    }\n    isEmpty() {\n        const value = this.control?.value;\n        return !value;\n    }\n    ngOnDestroy() {\n        this.destroy$.next(undefined);\n        this.destroy$.complete();\n    }\n    static { this.ctorParameters = () => [\n        { type: MatDynamicFormService, decorators: [{ type: Optional }] }\n    ]; }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }]\n    }; }\n};\nInputDateComponent = __decorate([\n    Component({\n        selector: 'lib-input-date',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], InputDateComponent);\nexport { InputDateComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,wCAAwC;AACzE,OAAOC,oBAAoB,MAAM,wCAAwC;AACzE,SAASC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC1D,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,IAAIC,kBAAkB,IAAAC,mBAAA,GAAG,MAAMD,kBAAkB,CAAC;EAC9C,IAAIE,gBAAgBA,CAACC,KAAK,EAAE;IACxB,IAAI,CAACC,KAAK,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,KAAK;IACzB,IAAI,CAACC,SAAS,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,SAAS;IACjC,IAAIF,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEG,MAAM,EAAE;MACfC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACF,MAAM,EAAEH,KAAK,CAACG,MAAM,CAAC;IAC5C;EACJ;EACAG,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACQ,QAAQ,GAAG,IAAIjB,OAAO,CAAC,CAAC;IAC7B,IAAIa,OAAO,EAAE;MACTA,OAAO,CAACK,QAAQ,CACXC,IAAI,CAAClB,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAACC,QAAQ,IAAI;QACvB,IAAI,CAACZ,MAAM,CAACY,QAAQ,GAAGA,QAAQ;MACnC,CAAC,CAAC;IACN;EACJ;EACAC,KAAKA,CAACC,KAAK,EAAE;IACTA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,QAAQ,CAAC,IAAI,CAAC;IAC/B;EACJ;EACAC,OAAOA,CAAA,EAAG;IAAA,IAAAC,aAAA;IACN,MAAMtB,KAAK,IAAAsB,aAAA,GAAG,IAAI,CAACH,OAAO,cAAAG,aAAA,uBAAZA,aAAA,CAActB,KAAK;IACjC,OAAO,CAACA,KAAK;EACjB;EACAuB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACZ,QAAQ,CAACa,IAAI,CAACC,SAAS,CAAC;IAC7B,IAAI,CAACd,QAAQ,CAACe,QAAQ,CAAC,CAAC;EAC5B;AAWJ,CAAC,EAVY5B,mBAAA,CAAK6B,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEhC,qBAAqB;EAAEiC,UAAU,EAAE,CAAC;IAAED,IAAI,EAAEnC;EAAS,CAAC;AAAE,CAAC,CACpE,EACQK,mBAAA,CAAKgC,cAAc,GAAG;EAC3BX,OAAO,EAAE,CAAC;IAAES,IAAI,EAAEpC;EAAM,CAAC,CAAC;EAC1BgB,EAAE,EAAE,CAAC;IAAEoB,IAAI,EAAEpC;EAAM,CAAC,CAAC;EACrBiB,QAAQ,EAAE,CAAC;IAAEmB,IAAI,EAAEpC;EAAM,CAAC,CAAC;EAC3BkB,SAAS,EAAE,CAAC;IAAEkB,IAAI,EAAEpC;EAAM,CAAC,CAAC;EAC5BO,gBAAgB,EAAE,CAAC;IAAE6B,IAAI,EAAEpC;EAAM,CAAC;AACtC,CAAC,EAAAM,mBAAA,CACJ;AACDD,kBAAkB,GAAGT,UAAU,CAAC,CAC5BG,SAAS,CAAC;EACNwC,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAE3C,oBAAoB;EAC9B4C,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC5C,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEO,kBAAkB,CAAC;AACtB,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}