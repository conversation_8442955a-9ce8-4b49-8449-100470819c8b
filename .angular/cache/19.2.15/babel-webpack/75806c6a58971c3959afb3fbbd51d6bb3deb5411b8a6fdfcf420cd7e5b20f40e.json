{"ast": null, "code": "var _SwuiGridUrlHandlerService;\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nlet SwuiGridUrlHandlerService = (_SwuiGridUrlHandlerService = class SwuiGridUrlHandlerService {\n  constructor(router, activatedRoute) {\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.allowQueryParamsUpdate = true;\n    this.pageSortQueryParams = {\n      limit: 'limit',\n      offset: 'offset',\n      sortBy: 'sortBy',\n      sortOrder: 'sortOrder'\n    };\n  }\n  setAllowQueryParamsUpdate(allow) {\n    this.allowQueryParamsUpdate = allow;\n  }\n  setParams(queryParams, queryParamsHandling = '') {\n    if (this.allowQueryParamsUpdate) {\n      this.router.navigate([], {\n        relativeTo: this.activatedRoute,\n        queryParams,\n        queryParamsHandling,\n        preserveFragment: true\n      });\n    }\n  }\n  getParams() {\n    return this.activatedRoute.snapshot.queryParams;\n  }\n  fetchPageSize(initial = 0) {\n    const params = this.getParams();\n    let fetched = initial;\n    if (params && 'limit' in params) {\n      fetched = parseInt(params['limit'], 10);\n    }\n    return fetched;\n  }\n  setParamsToPaginator(paginator) {\n    const params = this.getParams();\n    if (Object.keys(params).length) {\n      if ('offset' in params) {\n        paginator.pageIndex = parseInt(params[this.pageSortQueryParams.offset], 10) / paginator.pageSize;\n      }\n    }\n  }\n  setParamsToSort(sort) {\n    const params = this.getParams();\n    if (Object.keys(params).length) {\n      if ('sortOrder' in params) {\n        sort.direction = params[this.pageSortQueryParams.sortOrder].toLowerCase();\n      }\n      if ('sortBy' in params) {\n        sort.active = params[this.pageSortQueryParams.sortBy];\n      }\n    }\n  }\n  getFilterQueryParams() {\n    const params = this.getParams();\n    const ignoredParams = Object.keys(this.pageSortQueryParams);\n    return Object.keys(params).filter(key => ignoredParams.indexOf(key) === -1).reduce((data, key) => {\n      data[key] = params[key];\n      return data;\n    }, {});\n  }\n}, _SwuiGridUrlHandlerService.ctorParameters = () => [{\n  type: Router\n}, {\n  type: ActivatedRoute\n}], _SwuiGridUrlHandlerService);\nSwuiGridUrlHandlerService = __decorate([Injectable()], SwuiGridUrlHandlerService);\nexport { SwuiGridUrlHandlerService };\nlet MockUrlHandler = class MockUrlHandler {\n  // @ts-ignore\n  setAllowQueryParamsUpdate(allow) {}\n  // @ts-ignore\n  setParams(params) {}\n  // @ts-ignore\n  fetchPageSize(initial, override) {\n    return initial;\n  }\n  // @ts-ignore\n  setParamsToPaginator(paginator) {}\n  // @ts-ignore\n  setParamsToFilter(filter) {}\n  // @ts-ignore\n  setParamsToSort(sort) {}\n};\nMockUrlHandler = __decorate([Injectable()], MockUrlHandler);\nexport { MockUrlHandler };", "map": {"version": 3, "names": ["Injectable", "ActivatedRoute", "Router", "SwuiGridUrlHandlerService", "_SwuiGridUrlHandlerService", "constructor", "router", "activatedRoute", "allowQueryParamsUpdate", "pageSortQueryParams", "limit", "offset", "sortBy", "sortOrder", "setAllowQueryParamsUpdate", "allow", "setParams", "queryParams", "queryParamsHandling", "navigate", "relativeTo", "preserveFragment", "getParams", "snapshot", "fetchPageSize", "initial", "params", "fetched", "parseInt", "setParamsToPaginator", "paginator", "Object", "keys", "length", "pageIndex", "pageSize", "setParamsToSort", "sort", "direction", "toLowerCase", "active", "getFilterQueryParams", "ignoredParams", "filter", "key", "indexOf", "reduce", "data", "__decorate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "override", "setParamsToFilter"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/swui-grid-url-handler.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { ActivatedRoute, Params, QueryParamsHandling, Router } from '@angular/router';\nimport { SwuiTopFilterDataService } from '../swui-schema-top-filter/top-filter-data.service';\n\n@Injectable()\nexport class SwuiGridUrlHandlerService {\n\n  allowQueryParamsUpdate = true;\n\n  pageSortQueryParams: {\n    limit: string,\n    offset: string,\n    sortBy: string,\n    sortOrder: string,\n  } = {\n    limit: 'limit',\n    offset: 'offset',\n    sortBy: 'sortBy',\n    sortOrder: 'sortOrder',\n  };\n\n  constructor(\n    private router: Router,\n    private activatedRoute: ActivatedRoute,\n  ) {\n  }\n\n  setAllowQueryParamsUpdate( allow: boolean ): void {\n    this.allowQueryParamsUpdate = allow;\n  }\n\n  setParams( queryParams: Params, queryParamsHandling: QueryParamsHandling = '' ) {\n    if (this.allowQueryParamsUpdate) {\n      this.router.navigate([],\n        {\n          relativeTo: this.activatedRoute,\n          queryParams,\n          queryParamsHandling,\n          preserveFragment: true,\n        }\n      );\n    }\n  }\n\n  getParams(): Params {\n    return this.activatedRoute.snapshot.queryParams;\n  }\n\n  fetchPageSize( initial: number = 0 ): number {\n    const params = this.getParams();\n    let fetched = initial;\n\n    if (params && 'limit' in params) {\n      fetched = parseInt(params['limit'], 10);\n    }\n\n    return fetched;\n  }\n\n  setParamsToPaginator( paginator: MatPaginator ) {\n    const params = this.getParams();\n    if (Object.keys(params).length) {\n      if ('offset' in params) {\n        paginator.pageIndex = parseInt(params[this.pageSortQueryParams.offset], 10) / paginator.pageSize;\n      }\n    }\n  }\n\n  setParamsToSort( sort: MatSort ) {\n    const params = this.getParams();\n    if (Object.keys(params).length) {\n      if ('sortOrder' in params) {\n        sort.direction = params[this.pageSortQueryParams.sortOrder].toLowerCase();\n      }\n      if ('sortBy' in params) {\n        sort.active = params[this.pageSortQueryParams.sortBy];\n      }\n    }\n  }\n\n  getFilterQueryParams(): { [params: string]: any } {\n    const params = this.getParams();\n    const ignoredParams = Object.keys(this.pageSortQueryParams);\n    return Object.keys(params)\n      .filter(( key ) => ignoredParams.indexOf(key) === -1)\n      .reduce(( data: { [key: string]: any }, key: string ) => {\n        data[key] = params[key];\n        return data;\n      }, {});\n  }\n}\n\n@Injectable()\nexport class MockUrlHandler {\n\n  // @ts-ignore\n  setAllowQueryParamsUpdate( allow: boolean ) {\n  }\n\n  // @ts-ignore\n  setParams( params: Params ) {\n  }\n\n  // @ts-ignore\n  fetchPageSize( initial: number, override: boolean ): number {\n    return initial;\n  }\n\n  // @ts-ignore\n  setParamsToPaginator( paginator: MatPaginator ): void {\n\n  }\n\n  // @ts-ignore\n  setParamsToFilter( filter: SwuiTopFilterDataService ) {\n  }\n\n  // @ts-ignore\n  setParamsToSort( sort: MatSort ) {\n  }\n\n\n}\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAG1C,SAASC,cAAc,EAA+BC,MAAM,QAAQ,iBAAiB;AAI9E,IAAMC,yBAAyB,IAAAC,0BAAA,GAA/B,MAAMD,yBAAyB;EAgBpCE,YACUC,MAAc,EACdC,cAA8B;IAD9B,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IAhBxB,KAAAC,sBAAsB,GAAG,IAAI;IAE7B,KAAAC,mBAAmB,GAKf;MACFC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE;KACZ;EAMD;EAEAC,yBAAyBA,CAAEC,KAAc;IACvC,IAAI,CAACP,sBAAsB,GAAGO,KAAK;EACrC;EAEAC,SAASA,CAAEC,WAAmB,EAAEC,mBAAA,GAA2C,EAAE;IAC3E,IAAI,IAAI,CAACV,sBAAsB,EAAE;MAC/B,IAAI,CAACF,MAAM,CAACa,QAAQ,CAAC,EAAE,EACrB;QACEC,UAAU,EAAE,IAAI,CAACb,cAAc;QAC/BU,WAAW;QACXC,mBAAmB;QACnBG,gBAAgB,EAAE;OACnB,CACF;IACH;EACF;EAEAC,SAASA,CAAA;IACP,OAAO,IAAI,CAACf,cAAc,CAACgB,QAAQ,CAACN,WAAW;EACjD;EAEAO,aAAaA,CAAEC,OAAA,GAAkB,CAAC;IAChC,MAAMC,MAAM,GAAG,IAAI,CAACJ,SAAS,EAAE;IAC/B,IAAIK,OAAO,GAAGF,OAAO;IAErB,IAAIC,MAAM,IAAI,OAAO,IAAIA,MAAM,EAAE;MAC/BC,OAAO,GAAGC,QAAQ,CAACF,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;IACzC;IAEA,OAAOC,OAAO;EAChB;EAEAE,oBAAoBA,CAAEC,SAAuB;IAC3C,MAAMJ,MAAM,GAAG,IAAI,CAACJ,SAAS,EAAE;IAC/B,IAAIS,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAACO,MAAM,EAAE;MAC9B,IAAI,QAAQ,IAAIP,MAAM,EAAE;QACtBI,SAAS,CAACI,SAAS,GAAGN,QAAQ,CAACF,MAAM,CAAC,IAAI,CAACjB,mBAAmB,CAACE,MAAM,CAAC,EAAE,EAAE,CAAC,GAAGmB,SAAS,CAACK,QAAQ;MAClG;IACF;EACF;EAEAC,eAAeA,CAAEC,IAAa;IAC5B,MAAMX,MAAM,GAAG,IAAI,CAACJ,SAAS,EAAE;IAC/B,IAAIS,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAACO,MAAM,EAAE;MAC9B,IAAI,WAAW,IAAIP,MAAM,EAAE;QACzBW,IAAI,CAACC,SAAS,GAAGZ,MAAM,CAAC,IAAI,CAACjB,mBAAmB,CAACI,SAAS,CAAC,CAAC0B,WAAW,EAAE;MAC3E;MACA,IAAI,QAAQ,IAAIb,MAAM,EAAE;QACtBW,IAAI,CAACG,MAAM,GAAGd,MAAM,CAAC,IAAI,CAACjB,mBAAmB,CAACG,MAAM,CAAC;MACvD;IACF;EACF;EAEA6B,oBAAoBA,CAAA;IAClB,MAAMf,MAAM,GAAG,IAAI,CAACJ,SAAS,EAAE;IAC/B,MAAMoB,aAAa,GAAGX,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvB,mBAAmB,CAAC;IAC3D,OAAOsB,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CACvBiB,MAAM,CAAGC,GAAG,IAAMF,aAAa,CAACG,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CACpDE,MAAM,CAAC,CAAEC,IAA4B,EAAEH,GAAW,KAAK;MACtDG,IAAI,CAACH,GAAG,CAAC,GAAGlB,MAAM,CAACkB,GAAG,CAAC;MACvB,OAAOG,IAAI;IACb,CAAC,EAAE,EAAE,CAAC;EACV;;;;;;AApFW5C,yBAAyB,GAAA6C,UAAA,EADrChD,UAAU,EAAE,C,EACAG,yBAAyB,CAqFrC;;AAGM,IAAM8C,cAAc,GAApB,MAAMA,cAAc;EAEzB;EACAnC,yBAAyBA,CAAEC,KAAc,GACzC;EAEA;EACAC,SAASA,CAAEU,MAAc,GACzB;EAEA;EACAF,aAAaA,CAAEC,OAAe,EAAEyB,QAAiB;IAC/C,OAAOzB,OAAO;EAChB;EAEA;EACAI,oBAAoBA,CAAEC,SAAuB,GAE7C;EAEA;EACAqB,iBAAiBA,CAAER,MAAgC,GACnD;EAEA;EACAP,eAAeA,CAAEC,IAAa,GAC9B;CAGD;AA7BYY,cAAc,GAAAD,UAAA,EAD1BhD,UAAU,EAAE,C,EACAiD,cAAc,CA6B1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}