{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatDivider, _MatDividerModule;\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nclass MatDivider {\n  constructor() {\n    _defineProperty(this, \"_vertical\", false);\n    _defineProperty(this, \"_inset\", false);\n  }\n  /** Whether the divider is vertically aligned. */\n  get vertical() {\n    return this._vertical;\n  }\n  set vertical(value) {\n    this._vertical = coerceBooleanProperty(value);\n  }\n  /** Whether the divider is an inset divider. */\n  get inset() {\n    return this._inset;\n  }\n  set inset(value) {\n    this._inset = coerceBooleanProperty(value);\n  }\n}\n_MatDivider = MatDivider;\n_defineProperty(MatDivider, \"\\u0275fac\", function _MatDivider_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatDivider)();\n});\n_defineProperty(MatDivider, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatDivider,\n  selectors: [[\"mat-divider\"]],\n  hostAttrs: [\"role\", \"separator\", 1, \"mat-divider\"],\n  hostVars: 7,\n  hostBindings: function _MatDivider_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-orientation\", ctx.vertical ? \"vertical\" : \"horizontal\");\n      i0.ɵɵclassProp(\"mat-divider-vertical\", ctx.vertical)(\"mat-divider-horizontal\", !ctx.vertical)(\"mat-divider-inset\", ctx.inset);\n    }\n  },\n  inputs: {\n    vertical: \"vertical\",\n    inset: \"inset\"\n  },\n  decls: 0,\n  vars: 0,\n  template: function _MatDivider_Template(rf, ctx) {},\n  styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDivider, [{\n    type: Component,\n    args: [{\n      selector: 'mat-divider',\n      host: {\n        'role': 'separator',\n        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n        '[class.mat-divider-vertical]': 'vertical',\n        '[class.mat-divider-horizontal]': '!vertical',\n        '[class.mat-divider-inset]': 'inset',\n        'class': 'mat-divider'\n      },\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"]\n    }]\n  }], null, {\n    vertical: [{\n      type: Input\n    }],\n    inset: [{\n      type: Input\n    }]\n  });\n})();\nclass MatDividerModule {}\n_MatDividerModule = MatDividerModule;\n_defineProperty(MatDividerModule, \"\\u0275fac\", function _MatDividerModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatDividerModule)();\n});\n_defineProperty(MatDividerModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatDividerModule,\n  imports: [MatCommonModule, MatDivider],\n  exports: [MatDivider, MatCommonModule]\n}));\n_defineProperty(MatDividerModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDividerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatDivider],\n      exports: [MatDivider, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatDivider, MatDividerModule };\n//# sourceMappingURL=divider.mjs.map", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "NgModule", "coerceBooleanProperty", "M", "MatCommonModule", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "_defineProperty", "vertical", "_vertical", "value", "inset", "_inset", "_<PERSON><PERSON><PERSON><PERSON>", "_MatDivider_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "_MatDivider_HostBindings", "rf", "ctx", "ɵɵattribute", "ɵɵclassProp", "inputs", "decls", "vars", "template", "_MatDivider_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "None", "OnPush", "MatDividerModule", "_MatDividerModule", "_MatDividerModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/divider.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\nclass MatDivider {\n    /** Whether the divider is vertically aligned. */\n    get vertical() {\n        return this._vertical;\n    }\n    set vertical(value) {\n        this._vertical = coerceBooleanProperty(value);\n    }\n    _vertical = false;\n    /** Whether the divider is an inset divider. */\n    get inset() {\n        return this._inset;\n    }\n    set inset(value) {\n        this._inset = coerceBooleanProperty(value);\n    }\n    _inset = false;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDivider, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatDivider, isStandalone: true, selector: \"mat-divider\", inputs: { vertical: \"vertical\", inset: \"inset\" }, host: { attributes: { \"role\": \"separator\" }, properties: { \"attr.aria-orientation\": \"vertical ? \\\"vertical\\\" : \\\"horizontal\\\"\", \"class.mat-divider-vertical\": \"vertical\", \"class.mat-divider-horizontal\": \"!vertical\", \"class.mat-divider-inset\": \"inset\" }, classAttribute: \"mat-divider\" }, ngImport: i0, template: '', isInline: true, styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDivider, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-divider', host: {\n                        'role': 'separator',\n                        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n                        '[class.mat-divider-vertical]': 'vertical',\n                        '[class.mat-divider-horizontal]': '!vertical',\n                        '[class.mat-divider-inset]': 'inset',\n                        'class': 'mat-divider',\n                    }, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"] }]\n        }], propDecorators: { vertical: [{\n                type: Input\n            }], inset: [{\n                type: Input\n            }] } });\n\nclass MatDividerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDividerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDividerModule, imports: [MatCommonModule, MatDivider], exports: [MatDivider, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDividerModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDividerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatDivider],\n                    exports: [MatDivider, MatCommonModule],\n                }]\n        }] });\n\nexport { MatDivider, MatDividerModule };\n//# sourceMappingURL=divider.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACtG,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;AAE1B,MAAMC,UAAU,CAAC;EAAAC,YAAA;IAAAC,eAAA,oBAQD,KAAK;IAAAA,eAAA,iBAQR,KAAK;EAAA;EAfd;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGP,qBAAqB,CAACQ,KAAK,CAAC;EACjD;EAEA;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACD,KAAK,EAAE;IACb,IAAI,CAACE,MAAM,GAAGV,qBAAqB,CAACQ,KAAK,CAAC;EAC9C;AAIJ;AAACG,WAAA,GAnBKR,UAAU;AAAAE,eAAA,CAAVF,UAAU,wBAAAS,oBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAiBuFV,WAAU;AAAA;AAAAE,eAAA,CAjB3GF,UAAU,8BAoBiET,EAAE,CAAAoB,iBAAA;EAAAC,IAAA,EAFQZ,WAAU;EAAAa,SAAA;EAAAC,SAAA,WAA+H,WAAW;EAAAC,QAAA;EAAAC,YAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAE9J3B,EAAE,CAAA6B,WAAA,qBAAAD,GAAA,CAAAhB,QAAA,GAFmB,UAAU,GAAG,YAAY;MAE9CZ,EAAE,CAAA8B,WAAA,yBAAAF,GAAA,CAAAhB,QAFiB,CAAC,4BAAAgB,GAAA,CAAAhB,QAAD,CAAC,sBAAAgB,GAAA,CAAAb,KAAD,CAAC;IAAA;EAAA;EAAAgB,MAAA;IAAAnB,QAAA;IAAAG,KAAA;EAAA;EAAAiB,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,qBAAAR,EAAA,EAAAC,GAAA;EAAAQ,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAErG;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFvC,EAAE,CAAAwC,iBAAA,CAAQ/B,UAAU,EAAc,CAAC;IACxGY,IAAI,EAAEpB,SAAS;IACfwC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAa;MAAEC,IAAI,EAAE;QAC5B,MAAM,EAAE,WAAW;QACnB,yBAAyB,EAAE,sCAAsC;QACjE,8BAA8B,EAAE,UAAU;QAC1C,gCAAgC,EAAE,WAAW;QAC7C,2BAA2B,EAAE,OAAO;QACpC,OAAO,EAAE;MACb,CAAC;MAAET,QAAQ,EAAE,EAAE;MAAEG,aAAa,EAAEnC,iBAAiB,CAAC0C,IAAI;MAAEN,eAAe,EAAEnC,uBAAuB,CAAC0C,MAAM;MAAET,MAAM,EAAE,CAAC,yeAAye;IAAE,CAAC;EAC1mB,CAAC,CAAC,QAAkB;IAAExB,QAAQ,EAAE,CAAC;MACzBS,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEW,KAAK,EAAE,CAAC;MACRM,IAAI,EAAEjB;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0C,gBAAgB,CAAC;AAItBC,iBAAA,GAJKD,gBAAgB;AAAAnC,eAAA,CAAhBmC,gBAAgB,wBAAAE,0BAAA7B,iBAAA;EAAA,YAAAA,iBAAA,IACiF2B,iBAAgB;AAAA;AAAAnC,eAAA,CADjHmC,gBAAgB,8BAhB2D9C,EAAE,CAAAiD,gBAAA;EAAA5B,IAAA,EAkBqByB,iBAAgB;EAAAI,OAAA,GAAY1C,eAAe,EAAEC,UAAU;EAAA0C,OAAA,GAAa1C,UAAU,EAAED,eAAe;AAAA;AAAAG,eAAA,CAFjMmC,gBAAgB,8BAhB2D9C,EAAE,CAAAoD,gBAAA;EAAAF,OAAA,GAmBiD1C,eAAe,EAAEA,eAAe;AAAA;AAEpK;EAAA,QAAA+B,SAAA,oBAAAA,SAAA,KArBiFvC,EAAE,CAAAwC,iBAAA,CAqBQM,gBAAgB,EAAc,CAAC;IAC9GzB,IAAI,EAAEhB,QAAQ;IACdoC,IAAI,EAAE,CAAC;MACCS,OAAO,EAAE,CAAC1C,eAAe,EAAEC,UAAU,CAAC;MACtC0C,OAAO,EAAE,CAAC1C,UAAU,EAAED,eAAe;IACzC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASC,UAAU,EAAEqC,gBAAgB;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}