{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nlet SwuiSidebarService = class SwuiSidebarService {\n  constructor() {\n    this.isCollapsed = new BehaviorSubject(false);\n  }\n};\nSwuiSidebarService = __decorate([Injectable({\n  providedIn: 'root'\n})], SwuiSidebarService);\nexport { SwuiSidebarService };", "map": {"version": 3, "names": ["__decorate", "Injectable", "BehaviorSubject", "SwuiSidebarService", "constructor", "isCollapsed", "providedIn"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-sidebar/swui-sidebar.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nlet SwuiSidebarService = class SwuiSidebarService {\n    constructor() {\n        this.isCollapsed = new BehaviorSubject(false);\n    }\n};\nSwuiSidebarService = __decorate([\n    Injectable({ providedIn: 'root' })\n], SwuiSidebarService);\nexport { SwuiSidebarService };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,QAAQ,MAAM;AACtC,IAAIC,kBAAkB,GAAG,MAAMA,kBAAkB,CAAC;EAC9CC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,IAAIH,eAAe,CAAC,KAAK,CAAC;EACjD;AACJ,CAAC;AACDC,kBAAkB,GAAGH,UAAU,CAAC,CAC5BC,UAAU,CAAC;EAAEK,UAAU,EAAE;AAAO,CAAC,CAAC,CACrC,EAAEH,kBAAkB,CAAC;AACtB,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}