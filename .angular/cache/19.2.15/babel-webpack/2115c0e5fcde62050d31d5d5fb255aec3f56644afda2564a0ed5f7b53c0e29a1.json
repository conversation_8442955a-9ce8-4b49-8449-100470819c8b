{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { debounceTime } from 'rxjs/operators';\nlet SwuiTranslationsManagerService = class SwuiTranslationsManagerService {\n  constructor() {\n    this._languages$ = new BehaviorSubject([]);\n  }\n  get languages$() {\n    return this._languages$.asObservable().pipe(debounceTime(20));\n  }\n  get languages() {\n    return this._languages$.value;\n  }\n  addLanguages(langs) {\n    const uniqueLanguages = new Set([...this._languages$.value, ...langs]);\n    this._languages$.next(Array.from(uniqueLanguages));\n  }\n  setLanguages(langs) {\n    const uniqueLanguages = new Set(langs);\n    this._languages$.next(Array.from(uniqueLanguages));\n  }\n  removeLanguage(lang) {\n    const languages = this._languages$.value;\n    const index = languages.indexOf(lang);\n    languages.splice(index, 1);\n    this._languages$.next([...languages]);\n  }\n};\nSwuiTranslationsManagerService = __decorate([Injectable()], SwuiTranslationsManagerService);\nexport { SwuiTranslationsManagerService };", "map": {"version": 3, "names": ["__decorate", "Injectable", "BehaviorSubject", "debounceTime", "SwuiTranslationsManagerService", "constructor", "_languages$", "languages$", "asObservable", "pipe", "languages", "value", "addLanguages", "langs", "uniqueLanguages", "Set", "next", "Array", "from", "setLanguages", "removeLanguage", "lang", "index", "indexOf", "splice"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-translations-manager/swui-translations-manager.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { debounceTime } from 'rxjs/operators';\nlet SwuiTranslationsManagerService = class SwuiTranslationsManagerService {\n    constructor() {\n        this._languages$ = new BehaviorSubject([]);\n    }\n    get languages$() {\n        return this._languages$.asObservable()\n            .pipe(debounceTime(20));\n    }\n    get languages() {\n        return this._languages$.value;\n    }\n    addLanguages(langs) {\n        const uniqueLanguages = new Set([...this._languages$.value, ...langs]);\n        this._languages$.next(Array.from(uniqueLanguages));\n    }\n    setLanguages(langs) {\n        const uniqueLanguages = new Set(langs);\n        this._languages$.next(Array.from(uniqueLanguages));\n    }\n    removeLanguage(lang) {\n        const languages = this._languages$.value;\n        const index = languages.indexOf(lang);\n        languages.splice(index, 1);\n        this._languages$.next([...languages]);\n    }\n};\nSwuiTranslationsManagerService = __decorate([\n    Injectable()\n], SwuiTranslationsManagerService);\nexport { SwuiTranslationsManagerService };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,IAAIC,8BAA8B,GAAG,MAAMA,8BAA8B,CAAC;EACtEC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,IAAIJ,eAAe,CAAC,EAAE,CAAC;EAC9C;EACA,IAAIK,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,WAAW,CAACE,YAAY,CAAC,CAAC,CACjCC,IAAI,CAACN,YAAY,CAAC,EAAE,CAAC,CAAC;EAC/B;EACA,IAAIO,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACJ,WAAW,CAACK,KAAK;EACjC;EACAC,YAAYA,CAACC,KAAK,EAAE;IAChB,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACT,WAAW,CAACK,KAAK,EAAE,GAAGE,KAAK,CAAC,CAAC;IACtE,IAAI,CAACP,WAAW,CAACU,IAAI,CAACC,KAAK,CAACC,IAAI,CAACJ,eAAe,CAAC,CAAC;EACtD;EACAK,YAAYA,CAACN,KAAK,EAAE;IAChB,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAACF,KAAK,CAAC;IACtC,IAAI,CAACP,WAAW,CAACU,IAAI,CAACC,KAAK,CAACC,IAAI,CAACJ,eAAe,CAAC,CAAC;EACtD;EACAM,cAAcA,CAACC,IAAI,EAAE;IACjB,MAAMX,SAAS,GAAG,IAAI,CAACJ,WAAW,CAACK,KAAK;IACxC,MAAMW,KAAK,GAAGZ,SAAS,CAACa,OAAO,CAACF,IAAI,CAAC;IACrCX,SAAS,CAACc,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC1B,IAAI,CAAChB,WAAW,CAACU,IAAI,CAAC,CAAC,GAAGN,SAAS,CAAC,CAAC;EACzC;AACJ,CAAC;AACDN,8BAA8B,GAAGJ,UAAU,CAAC,CACxCC,UAAU,CAAC,CAAC,CACf,EAAEG,8BAA8B,CAAC;AAClC,SAASA,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}