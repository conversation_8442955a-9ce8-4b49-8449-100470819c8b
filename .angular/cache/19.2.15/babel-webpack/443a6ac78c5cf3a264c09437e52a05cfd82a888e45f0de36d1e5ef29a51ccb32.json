{"ast": null, "code": "var _SwBrowserTitleService;\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { Title } from '@angular/platform-browser';\nimport { ActivatedRoute, NavigationEnd, Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { filter, map, takeUntil } from 'rxjs/operators';\nimport { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';\nlet SwBrowserTitleService = (_SwBrowserTitleService = class SwBrowserTitleService {\n  constructor(hubConfigService, activatedRoute, router, titleService) {\n    this.hubConfigService = hubConfigService;\n    this.activatedRoute = activatedRoute;\n    this.router = router;\n    this.titleService = titleService;\n    this.destroyed$ = new Subject();\n  }\n  setupTitles(hubName, additionalTitle, fixedUrl) {\n    this.router.events.pipe(filter(event => {\n      if (fixedUrl && event instanceof NavigationEnd) {\n        return event === null || event === void 0 ? void 0 : event.urlAfterRedirects.includes(fixedUrl);\n      }\n      return event instanceof NavigationEnd;\n    }), map(() => {\n      let child = this.activatedRoute.firstChild;\n      while (child) {\n        if (child.firstChild) {\n          child = child.firstChild;\n        } else if (child.snapshot.data && child.snapshot.data['title']) {\n          return child.snapshot.data['title'];\n        } else {\n          return '';\n        }\n      }\n      return '';\n    }), takeUntil(this.destroyed$)).subscribe(title => {\n      var _this$hubConfigServic, _this$hubConfigServic2;\n      const envName = (_this$hubConfigServic = this.hubConfigService.envName) === null || _this$hubConfigServic === void 0 ? void 0 : _this$hubConfigServic.toLocaleUpperCase();\n      const location = (_this$hubConfigServic2 = this.hubConfigService.locationName) === null || _this$hubConfigServic2 === void 0 ? void 0 : _this$hubConfigServic2.toLocaleUpperCase();\n      const processedHubName = hubName ? `${hubName} | ` : '';\n      this.titleService.setTitle(`UBO ${location || ''} ${envName || ''} | ${processedHubName} ${title} ${additionalTitle || ''}`);\n    });\n  }\n}, _SwBrowserTitleService.ctorParameters = () => [{\n  type: SwHubConfigService\n}, {\n  type: ActivatedRoute\n}, {\n  type: Router\n}, {\n  type: Title\n}], _SwBrowserTitleService);\nSwBrowserTitleService = __decorate([Injectable()], SwBrowserTitleService);\nexport { SwBrowserTitleService };", "map": {"version": 3, "names": ["Injectable", "Title", "ActivatedRoute", "NavigationEnd", "Router", "Subject", "filter", "map", "takeUntil", "SwHubConfigService", "SwBrowserTitleService", "_SwBrowserTitleService", "constructor", "hubConfigService", "activatedRoute", "router", "titleService", "destroyed$", "setupTitles", "hubName", "additionalTitle", "fixedUrl", "events", "pipe", "event", "urlAfterRedirects", "includes", "child", "<PERSON><PERSON><PERSON><PERSON>", "snapshot", "data", "subscribe", "title", "_this$hubConfigServic", "_this$hubConfigServic2", "envName", "toLocaleUpperCase", "location", "locationName", "processedHubName", "setTitle", "__decorate"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-browser-title/sw-browser-title.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Title } from '@angular/platform-browser';\nimport { ActivatedRoute, NavigationEnd, Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { filter, map, takeUntil } from 'rxjs/operators';\nimport { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';\n\n\n@Injectable()\nexport class SwBrowserTitleService {\n\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor( private hubConfigService: SwHubConfigService,\n               private activatedRoute: ActivatedRoute,\n               private readonly router: Router,\n               private titleService: Title ) {\n  }\n\n  setupTitles(hubName?: string, additionalTitle?: string, fixedUrl?: string): void {\n    this.router.events.pipe(\n      filter(event => {\n        if (fixedUrl && event instanceof NavigationEnd) {\n          return event?.urlAfterRedirects.includes(fixedUrl);\n        }\n        return event instanceof NavigationEnd;\n      }),\n      map(() => {\n        let child = this.activatedRoute.firstChild;\n        while (child) {\n          if (child.firstChild) {\n            child = child.firstChild;\n          } else if (child.snapshot.data &&    child.snapshot.data['title']) {\n            return child.snapshot.data['title'];\n          } else {\n            return '';\n          }\n        }\n        return '';\n      }),\n      takeUntil(this.destroyed$)\n    ).subscribe((title: string) => {\n        const envName = this.hubConfigService.envName?.toLocaleUpperCase();\n        const location = this.hubConfigService.locationName?.toLocaleUpperCase();\n        const processedHubName = hubName ? `${hubName} | ` : '';\n      this.titleService.setTitle(`UBO ${location || ''} ${envName || ''} | ${processedHubName} ${title} ${additionalTitle || ''}`);\n    });\n  }\n}\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,cAAc,EAAEC,aAAa,EAAEC,MAAM,QAAQ,iBAAiB;AACvE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AACvD,SAASC,kBAAkB,QAAQ,wCAAwC;AAIpE,IAAMC,qBAAqB,IAAAC,sBAAA,GAA3B,MAAMD,qBAAqB;EAIhCE,YAAqBC,gBAAoC,EACpCC,cAA8B,EACrBC,MAAc,EACvBC,YAAmB;IAHnB,KAAAH,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACL,KAAAC,MAAM,GAANA,MAAM;IACf,KAAAC,YAAY,GAAZA,YAAY;IALhB,KAAAC,UAAU,GAAG,IAAIZ,OAAO,EAAQ;EAMjD;EAEAa,WAAWA,CAACC,OAAgB,EAAEC,eAAwB,EAAEC,QAAiB;IACvE,IAAI,CAACN,MAAM,CAACO,MAAM,CAACC,IAAI,CACrBjB,MAAM,CAACkB,KAAK,IAAG;MACb,IAAIH,QAAQ,IAAIG,KAAK,YAAYrB,aAAa,EAAE;QAC9C,OAAOqB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,iBAAiB,CAACC,QAAQ,CAACL,QAAQ,CAAC;MACpD;MACA,OAAOG,KAAK,YAAYrB,aAAa;IACvC,CAAC,CAAC,EACFI,GAAG,CAAC,MAAK;MACP,IAAIoB,KAAK,GAAG,IAAI,CAACb,cAAc,CAACc,UAAU;MAC1C,OAAOD,KAAK,EAAE;QACZ,IAAIA,KAAK,CAACC,UAAU,EAAE;UACpBD,KAAK,GAAGA,KAAK,CAACC,UAAU;QAC1B,CAAC,MAAM,IAAID,KAAK,CAACE,QAAQ,CAACC,IAAI,IAAOH,KAAK,CAACE,QAAQ,CAACC,IAAI,CAAC,OAAO,CAAC,EAAE;UACjE,OAAOH,KAAK,CAACE,QAAQ,CAACC,IAAI,CAAC,OAAO,CAAC;QACrC,CAAC,MAAM;UACL,OAAO,EAAE;QACX;MACF;MACA,OAAO,EAAE;IACX,CAAC,CAAC,EACFtB,SAAS,CAAC,IAAI,CAACS,UAAU,CAAC,CAC3B,CAACc,SAAS,CAAEC,KAAa,IAAI;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MAC1B,MAAMC,OAAO,IAAAF,qBAAA,GAAG,IAAI,CAACpB,gBAAgB,CAACsB,OAAO,cAAAF,qBAAA,uBAA7BA,qBAAA,CAA+BG,iBAAiB,EAAE;MAClE,MAAMC,QAAQ,IAAAH,sBAAA,GAAG,IAAI,CAACrB,gBAAgB,CAACyB,YAAY,cAAAJ,sBAAA,uBAAlCA,sBAAA,CAAoCE,iBAAiB,EAAE;MACxE,MAAMG,gBAAgB,GAAGpB,OAAO,GAAG,GAAGA,OAAO,KAAK,GAAG,EAAE;MACzD,IAAI,CAACH,YAAY,CAACwB,QAAQ,CAAC,OAAOH,QAAQ,IAAI,EAAE,IAAIF,OAAO,IAAI,EAAE,MAAMI,gBAAgB,IAAIP,KAAK,IAAIZ,eAAe,IAAI,EAAE,EAAE,CAAC;IAC9H,CAAC,CAAC;EACJ;;;;;;;;;;AAtCWV,qBAAqB,GAAA+B,UAAA,EADjCzC,UAAU,EAAE,C,EACAU,qBAAqB,CAuCjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}