{"ast": null, "code": "export { e as MAT_DIALOG_DATA, f as MAT_DIALOG_DEFAULT_OPTIONS, g as MAT_DIALOG_SCROLL_STRATEGY, i as MAT_DIALOG_SCROLL_STRATEGY_PROVIDER, h as MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, j as MatDialog, M as MatDialogActions, a as MatDialogClose, k as MatDialogConfig, d as Mat<PERSON>ialogContainer, c as MatDialogContent, n as MatDialogModule, m as MatDialogRef, l as MatDialogState, b as MatDialogTitle, _ as _closeDialogVia } from './module-BnDTus5c.mjs';\nimport '@angular/cdk/dialog';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/portal';\nimport '@angular/core';\nimport '@angular/cdk/coercion';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/keycodes';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/scrolling';\nimport './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/bidi';\n\n/**\n * Default parameters for the animation for backwards compatibility.\n * @docs-private\n * @deprecated Will stop being exported.\n * @breaking-change 21.0.0\n */\nconst _defaultParams = {\n  params: {\n    enterAnimationDuration: '150ms',\n    exitAnimationDuration: '75ms'\n  }\n};\n/**\n * Animations used by MatDialog.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matDialogAnimations = {\n  // Represents:\n  // trigger('dialogContainer', [\n  //   // Note: The `enter` animation transitions to `transform: none`, because for some reason\n  //   // specifying the transform explicitly, causes IE both to blur the dialog content and\n  //   // decimate the animation performance. Leaving it as `none` solves both issues.\n  //   state('void, exit', style({opacity: 0, transform: 'scale(0.7)'})),\n  //   state('enter', style({transform: 'none'})),\n  //   transition(\n  //     '* => enter',\n  //     group([\n  //       animate(\n  //         '{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)',\n  //         style({transform: 'none', opacity: 1}),\n  //       ),\n  //       query('@*', animateChild(), {optional: true}),\n  //     ]),\n  //     _defaultParams,\n  //   ),\n  //   transition(\n  //     '* => void, * => exit',\n  //     group([\n  //       animate('{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)', style({opacity: 0})),\n  //       query('@*', animateChild(), {optional: true}),\n  //     ]),\n  //     _defaultParams,\n  //   ),\n  // ])\n  /** Animation that is applied on the dialog container by default. */\n  dialogContainer: {\n    type: 7,\n    name: 'dialogContainer',\n    definitions: [{\n      type: 0,\n      name: 'void, exit',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'scale(0.7)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'enter',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'none'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => enter',\n      animation: {\n        type: 3,\n        steps: [{\n          type: 4,\n          styles: {\n            type: 6,\n            styles: {\n              transform: 'none',\n              opacity: 1\n            },\n            offset: null\n          },\n          timings: '{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)'\n        }, {\n          type: 11,\n          selector: '@*',\n          animation: {\n            type: 9,\n            options: null\n          },\n          options: {\n            optional: true\n          }\n        }],\n        options: null\n      },\n      options: {\n        params: {\n          enterAnimationDuration: '150ms',\n          exitAnimationDuration: '75ms'\n        }\n      }\n    }, {\n      type: 1,\n      expr: '* => void, * => exit',\n      animation: {\n        type: 3,\n        steps: [{\n          type: 4,\n          styles: {\n            type: 6,\n            styles: {\n              opacity: 0\n            },\n            offset: null\n          },\n          timings: '{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)'\n        }, {\n          type: 11,\n          selector: '@*',\n          animation: {\n            type: 9,\n            options: null\n          },\n          options: {\n            optional: true\n          }\n        }],\n        options: null\n      },\n      options: {\n        params: {\n          enterAnimationDuration: '150ms',\n          exitAnimationDuration: '75ms'\n        }\n      }\n    }],\n    options: {}\n  }\n};\nexport { _defaultParams, matDialogAnimations };\n//# sourceMappingURL=dialog.mjs.map", "map": {"version": 3, "names": ["e", "MAT_DIALOG_DATA", "f", "MAT_DIALOG_DEFAULT_OPTIONS", "g", "MAT_DIALOG_SCROLL_STRATEGY", "i", "MAT_DIALOG_SCROLL_STRATEGY_PROVIDER", "h", "MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY", "j", "MatDialog", "M", "MatDialogActions", "a", "MatDialogClose", "k", "MatDialogConfig", "d", "MatDialogContainer", "c", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "n", "MatDialogModule", "m", "MatDialogRef", "l", "MatDialogState", "b", "MatDialogTitle", "_", "_closeDialogVia", "_defaultParams", "params", "enterAnimationDuration", "exitAnimationDuration", "matDialogAnimations", "dialogContainer", "type", "name", "definitions", "styles", "opacity", "transform", "offset", "expr", "animation", "steps", "timings", "selector", "options", "optional"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/dialog.mjs"], "sourcesContent": ["export { e as MAT_DIALOG_DATA, f as MAT_DIALOG_DEFAULT_OPTIONS, g as MAT_DIALOG_SCROLL_STRATEGY, i as MAT_DIALOG_SCROLL_STRATEGY_PROVIDER, h as MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, j as MatDialog, M as MatDialogActions, a as MatDialogClose, k as MatDialogConfig, d as MatDialogContainer, c as MatDialogContent, n as MatDialogModule, m as MatDialogRef, l as MatDialogState, b as MatDialogTitle, _ as _closeDialogVia } from './module-BnDTus5c.mjs';\nimport '@angular/cdk/dialog';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/portal';\nimport '@angular/core';\nimport '@angular/cdk/coercion';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/keycodes';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/scrolling';\nimport './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/bidi';\n\n/**\n * Default parameters for the animation for backwards compatibility.\n * @docs-private\n * @deprecated Will stop being exported.\n * @breaking-change 21.0.0\n */\nconst _defaultParams = {\n    params: { enterAnimationDuration: '150ms', exitAnimationDuration: '75ms' },\n};\n/**\n * Animations used by MatDialog.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matDialogAnimations = {\n    // Represents:\n    // trigger('dialogContainer', [\n    //   // Note: The `enter` animation transitions to `transform: none`, because for some reason\n    //   // specifying the transform explicitly, causes IE both to blur the dialog content and\n    //   // decimate the animation performance. Leaving it as `none` solves both issues.\n    //   state('void, exit', style({opacity: 0, transform: 'scale(0.7)'})),\n    //   state('enter', style({transform: 'none'})),\n    //   transition(\n    //     '* => enter',\n    //     group([\n    //       animate(\n    //         '{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)',\n    //         style({transform: 'none', opacity: 1}),\n    //       ),\n    //       query('@*', animateChild(), {optional: true}),\n    //     ]),\n    //     _defaultParams,\n    //   ),\n    //   transition(\n    //     '* => void, * => exit',\n    //     group([\n    //       animate('{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)', style({opacity: 0})),\n    //       query('@*', animateChild(), {optional: true}),\n    //     ]),\n    //     _defaultParams,\n    //   ),\n    // ])\n    /** Animation that is applied on the dialog container by default. */\n    dialogContainer: {\n        type: 7,\n        name: 'dialogContainer',\n        definitions: [\n            {\n                type: 0,\n                name: 'void, exit',\n                styles: { type: 6, styles: { opacity: 0, transform: 'scale(0.7)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'enter',\n                styles: { type: 6, styles: { transform: 'none' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: '* => enter',\n                animation: {\n                    type: 3,\n                    steps: [\n                        {\n                            type: 4,\n                            styles: { type: 6, styles: { transform: 'none', opacity: 1 }, offset: null },\n                            timings: '{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)',\n                        },\n                        {\n                            type: 11,\n                            selector: '@*',\n                            animation: { type: 9, options: null },\n                            options: { optional: true },\n                        },\n                    ],\n                    options: null,\n                },\n                options: { params: { enterAnimationDuration: '150ms', exitAnimationDuration: '75ms' } },\n            },\n            {\n                type: 1,\n                expr: '* => void, * => exit',\n                animation: {\n                    type: 3,\n                    steps: [\n                        {\n                            type: 4,\n                            styles: { type: 6, styles: { opacity: 0 }, offset: null },\n                            timings: '{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)',\n                        },\n                        {\n                            type: 11,\n                            selector: '@*',\n                            animation: { type: 9, options: null },\n                            options: { optional: true },\n                        },\n                    ],\n                    options: null,\n                },\n                options: { params: { enterAnimationDuration: '150ms', exitAnimationDuration: '75ms' } },\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { _defaultParams, matDialogAnimations };\n//# sourceMappingURL=dialog.mjs.map\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,0BAA0B,EAAEC,CAAC,IAAIC,0BAA0B,EAAEC,CAAC,IAAIC,mCAAmC,EAAEC,CAAC,IAAIC,2CAA2C,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,eAAe,QAAQ,uBAAuB;AACrc,OAAO,qBAAqB;AAC5B,OAAO,sBAAsB;AAC7B,OAAO,qBAAqB;AAC5B,OAAO,eAAe;AACtB,OAAO,uBAAuB;AAC9B,OAAO,MAAM;AACb,OAAO,gBAAgB;AACvB,OAAO,uBAAuB;AAC9B,OAAO,mBAAmB;AAC1B,OAAO,wBAAwB;AAC/B,OAAO,8BAA8B;AACrC,OAAO,mBAAmB;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG;EACnBC,MAAM,EAAE;IAAEC,sBAAsB,EAAE,OAAO;IAAEC,qBAAqB,EAAE;EAAO;AAC7E,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG;EACxB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,eAAe,EAAE;IACbC,IAAI,EAAE,CAAC;IACPC,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE,CACT;MACIF,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBE,MAAM,EAAE;QAAEH,IAAI,EAAE,CAAC;QAAEG,MAAM,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAa,CAAC;QAAEC,MAAM,EAAE;MAAK;IACrF,CAAC,EACD;MACIN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbE,MAAM,EAAE;QAAEH,IAAI,EAAE,CAAC;QAAEG,MAAM,EAAE;UAAEE,SAAS,EAAE;QAAO,CAAC;QAAEC,MAAM,EAAE;MAAK;IACnE,CAAC,EACD;MACIN,IAAI,EAAE,CAAC;MACPO,IAAI,EAAE,YAAY;MAClBC,SAAS,EAAE;QACPR,IAAI,EAAE,CAAC;QACPS,KAAK,EAAE,CACH;UACIT,IAAI,EAAE,CAAC;UACPG,MAAM,EAAE;YAAEH,IAAI,EAAE,CAAC;YAAEG,MAAM,EAAE;cAAEE,SAAS,EAAE,MAAM;cAAED,OAAO,EAAE;YAAE,CAAC;YAAEE,MAAM,EAAE;UAAK,CAAC;UAC5EI,OAAO,EAAE;QACb,CAAC,EACD;UACIV,IAAI,EAAE,EAAE;UACRW,QAAQ,EAAE,IAAI;UACdH,SAAS,EAAE;YAAER,IAAI,EAAE,CAAC;YAAEY,OAAO,EAAE;UAAK,CAAC;UACrCA,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAK;QAC9B,CAAC,CACJ;QACDD,OAAO,EAAE;MACb,CAAC;MACDA,OAAO,EAAE;QAAEjB,MAAM,EAAE;UAAEC,sBAAsB,EAAE,OAAO;UAAEC,qBAAqB,EAAE;QAAO;MAAE;IAC1F,CAAC,EACD;MACIG,IAAI,EAAE,CAAC;MACPO,IAAI,EAAE,sBAAsB;MAC5BC,SAAS,EAAE;QACPR,IAAI,EAAE,CAAC;QACPS,KAAK,EAAE,CACH;UACIT,IAAI,EAAE,CAAC;UACPG,MAAM,EAAE;YAAEH,IAAI,EAAE,CAAC;YAAEG,MAAM,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAC;YAAEE,MAAM,EAAE;UAAK,CAAC;UACzDI,OAAO,EAAE;QACb,CAAC,EACD;UACIV,IAAI,EAAE,EAAE;UACRW,QAAQ,EAAE,IAAI;UACdH,SAAS,EAAE;YAAER,IAAI,EAAE,CAAC;YAAEY,OAAO,EAAE;UAAK,CAAC;UACrCA,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAK;QAC9B,CAAC,CACJ;QACDD,OAAO,EAAE;MACb,CAAC;MACDA,OAAO,EAAE;QAAEjB,MAAM,EAAE;UAAEC,sBAAsB,EAAE,OAAO;UAAEC,qBAAqB,EAAE;QAAO;MAAE;IAC1F,CAAC,CACJ;IACDe,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAASlB,cAAc,EAAEI,mBAAmB;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}