{"ast": null, "code": "// This file is required by karma.conf.js and loads recursively all the .spec and framework files\nimport 'zone.js';\nimport 'zone.js/testing';\nimport { getTestBed } from '@angular/core/testing';\nimport { BrowserDynamicTestingModule, platformBrowserDynamicTesting } from '@angular/platform-browser-dynamic/testing';\n// First, initialize the Angular testing environment.\ngetTestBed().initTestEnvironment(BrowserDynamicTestingModule, platformBrowserDynamicTesting(), {\n  teardown: {\n    destroyAfterEach: false\n  }\n});", "map": {"version": 3, "names": ["getTestBed", "BrowserDynamicTestingModule", "platformBrowserDynamicTesting", "initTestEnvironment", "teardown", "destroyAfterEach"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/test.ts"], "sourcesContent": ["// This file is required by karma.conf.js and loads recursively all the .spec and framework files\n\nimport 'zone.js';\nimport 'zone.js/testing';\nimport { getTestBed } from '@angular/core/testing';\nimport {\n  BrowserDynamicTestingModule,\n  platformBrowserDynamicTesting\n} from '@angular/platform-browser-dynamic/testing';\n\n// First, initialize the Angular testing environment.\ngetTestBed().initTestEnvironment(\n  BrowserDynamicTestingModule,\n  platformBrowserDynamicTesting(), {\n    teardown: { destroyAfterEach: false }\n}\n);\n"], "mappings": "AAAA;AAEA,OAAO,SAAS;AAChB,OAAO,iBAAiB;AACxB,SAASA,UAAU,QAAQ,uBAAuB;AAClD,SACEC,2BAA2B,EAC3BC,6BAA6B,QACxB,2CAA2C;AAElD;AACAF,UAAU,EAAE,CAACG,mBAAmB,CAC9BF,2BAA2B,EAC3BC,6BAA6B,EAAE,EAAE;EAC/BE,QAAQ,EAAE;IAAEC,gBAAgB,EAAE;EAAK;CACtC,CACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}