{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Subject } from './Subject';\nvar AsyncSubject = function (_super) {\n  __extends(AsyncSubject, _super);\n  function AsyncSubject() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this._value = null;\n    _this._hasValue = false;\n    _this._isComplete = false;\n    return _this;\n  }\n  AsyncSubject.prototype._checkFinalizedStatuses = function (subscriber) {\n    var _a = this,\n      hasError = _a.hasError,\n      _hasValue = _a._hasValue,\n      _value = _a._value,\n      thrownError = _a.thrownError,\n      isStopped = _a.isStopped,\n      _isComplete = _a._isComplete;\n    if (hasError) {\n      subscriber.error(thrownError);\n    } else if (isStopped || _isComplete) {\n      _hasValue && subscriber.next(_value);\n      subscriber.complete();\n    }\n  };\n  AsyncSubject.prototype.next = function (value) {\n    if (!this.isStopped) {\n      this._value = value;\n      this._hasValue = true;\n    }\n  };\n  AsyncSubject.prototype.complete = function () {\n    var _a = this,\n      _hasValue = _a._hasValue,\n      _value = _a._value,\n      _isComplete = _a._isComplete;\n    if (!_isComplete) {\n      this._isComplete = true;\n      _hasValue && _super.prototype.next.call(this, _value);\n      _super.prototype.complete.call(this);\n    }\n  };\n  return AsyncSubject;\n}(Subject);\nexport { AsyncSubject };\n//# sourceMappingURL=AsyncSubject.js.map", "map": {"version": 3, "names": ["__extends", "Subject", "AsyncSubject", "_super", "_this", "apply", "arguments", "_value", "_hasValue", "_isComplete", "prototype", "_checkFinalizedStatuses", "subscriber", "_a", "<PERSON><PERSON><PERSON><PERSON>", "thrownError", "isStopped", "error", "next", "complete", "value", "call"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/AsyncSubject.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { Subject } from './Subject';\nvar AsyncSubject = (function (_super) {\n    __extends(AsyncSubject, _super);\n    function AsyncSubject() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this._value = null;\n        _this._hasValue = false;\n        _this._isComplete = false;\n        return _this;\n    }\n    AsyncSubject.prototype._checkFinalizedStatuses = function (subscriber) {\n        var _a = this, hasError = _a.hasError, _hasValue = _a._hasValue, _value = _a._value, thrownError = _a.thrownError, isStopped = _a.isStopped, _isComplete = _a._isComplete;\n        if (hasError) {\n            subscriber.error(thrownError);\n        }\n        else if (isStopped || _isComplete) {\n            _hasValue && subscriber.next(_value);\n            subscriber.complete();\n        }\n    };\n    AsyncSubject.prototype.next = function (value) {\n        if (!this.isStopped) {\n            this._value = value;\n            this._hasValue = true;\n        }\n    };\n    AsyncSubject.prototype.complete = function () {\n        var _a = this, _hasValue = _a._hasValue, _value = _a._value, _isComplete = _a._isComplete;\n        if (!_isComplete) {\n            this._isComplete = true;\n            _hasValue && _super.prototype.next.call(this, _value);\n            _super.prototype.complete.call(this);\n        }\n    };\n    return AsyncSubject;\n}(Subject));\nexport { AsyncSubject };\n//# sourceMappingURL=AsyncSubject.js.map"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,OAAO,QAAQ,WAAW;AACnC,IAAIC,YAAY,GAAI,UAAUC,MAAM,EAAE;EAClCH,SAAS,CAACE,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,MAAM,GAAG,IAAI;IACnBH,KAAK,CAACI,SAAS,GAAG,KAAK;IACvBJ,KAAK,CAACK,WAAW,GAAG,KAAK;IACzB,OAAOL,KAAK;EAChB;EACAF,YAAY,CAACQ,SAAS,CAACC,uBAAuB,GAAG,UAAUC,UAAU,EAAE;IACnE,IAAIC,EAAE,GAAG,IAAI;MAAEC,QAAQ,GAAGD,EAAE,CAACC,QAAQ;MAAEN,SAAS,GAAGK,EAAE,CAACL,SAAS;MAAED,MAAM,GAAGM,EAAE,CAACN,MAAM;MAAEQ,WAAW,GAAGF,EAAE,CAACE,WAAW;MAAEC,SAAS,GAAGH,EAAE,CAACG,SAAS;MAAEP,WAAW,GAAGI,EAAE,CAACJ,WAAW;IACzK,IAAIK,QAAQ,EAAE;MACVF,UAAU,CAACK,KAAK,CAACF,WAAW,CAAC;IACjC,CAAC,MACI,IAAIC,SAAS,IAAIP,WAAW,EAAE;MAC/BD,SAAS,IAAII,UAAU,CAACM,IAAI,CAACX,MAAM,CAAC;MACpCK,UAAU,CAACO,QAAQ,CAAC,CAAC;IACzB;EACJ,CAAC;EACDjB,YAAY,CAACQ,SAAS,CAACQ,IAAI,GAAG,UAAUE,KAAK,EAAE;IAC3C,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACjB,IAAI,CAACT,MAAM,GAAGa,KAAK;MACnB,IAAI,CAACZ,SAAS,GAAG,IAAI;IACzB;EACJ,CAAC;EACDN,YAAY,CAACQ,SAAS,CAACS,QAAQ,GAAG,YAAY;IAC1C,IAAIN,EAAE,GAAG,IAAI;MAAEL,SAAS,GAAGK,EAAE,CAACL,SAAS;MAAED,MAAM,GAAGM,EAAE,CAACN,MAAM;MAAEE,WAAW,GAAGI,EAAE,CAACJ,WAAW;IACzF,IAAI,CAACA,WAAW,EAAE;MACd,IAAI,CAACA,WAAW,GAAG,IAAI;MACvBD,SAAS,IAAIL,MAAM,CAACO,SAAS,CAACQ,IAAI,CAACG,IAAI,CAAC,IAAI,EAAEd,MAAM,CAAC;MACrDJ,MAAM,CAACO,SAAS,CAACS,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC;IACxC;EACJ,CAAC;EACD,OAAOnB,YAAY;AACvB,CAAC,CAACD,OAAO,CAAE;AACX,SAASC,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}