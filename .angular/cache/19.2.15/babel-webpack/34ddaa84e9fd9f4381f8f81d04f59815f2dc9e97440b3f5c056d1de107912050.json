{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwHubEntityService;\nimport { __decorate } from \"tslib\";\nimport { Inject, Injectable, Optional } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\nimport { SwHubEntityDataSource } from './sw-hub-entity-data-source';\nfunction findEntity(entity, id) {\n  if (entity.id === id) {\n    return entity;\n  }\n  if (Array.isArray(entity.child)) {\n    let found = null;\n    entity.child.every(item => {\n      found = findEntity(item, id);\n      return !found;\n    });\n    return found;\n  }\n  return null;\n}\nlet SwHubEntityService = (_SwHubEntityService = class SwHubEntityService {\n  constructor(activatedRoute, service) {\n    this.activatedRoute = activatedRoute;\n    this.entities = [];\n    this.foundedEntities = [];\n    this.expandedEntities = new Map();\n    this.id$ = new BehaviorSubject(null);\n    this.brief$ = service ? service.getBrief() : of(null);\n    this.top$ = service ? service.getEntity() : of(null);\n    this.items$ = this.top$.pipe(map(entity => {\n      this.entities = this.convertStructure(entity);\n      this.entitiesObject = this.entities.reduce((res, item) => {\n        res[item.id] = item;\n        return res;\n      }, {});\n      return this.entities;\n    }));\n    this.itemSelected$ = combineLatest([this.id$, this.items$]).pipe(map(([id, items]) => {\n      if (items.length === 0) {\n        return null;\n      }\n      if (!id) {\n        return items[0] || null;\n      }\n      return items.find(item => item.id === id) || items[0] || null;\n    }));\n    this.entitySelected$ = combineLatest([this.top$, this.itemSelected$]).pipe(map(([entity, selected]) => {\n      if (selected && entity) {\n        return findEntity(entity, selected.id);\n      }\n      return null;\n    }));\n  }\n  convertStructure(structure) {\n    if (!structure) {\n      return [];\n    }\n    const entities = [];\n    this.convertItem(structure, entities, 0);\n    return entities;\n  }\n  convertItem(item, result, level, parent) {\n    const children = item.child ? [...item.child] : [];\n    const childrenIds = children.map(({\n      id\n    }) => id);\n    const currItem = _objectSpread(_objectSpread({}, item), {}, {\n      child: [],\n      level,\n      parentId: parent === null || parent === void 0 ? void 0 : parent.id,\n      children: childrenIds\n    });\n    currItem.parentId = parent === null || parent === void 0 ? void 0 : parent.id;\n    result.push(currItem);\n    if (children) {\n      children.forEach(child => this.convertItem(child, result, level + 1, currItem));\n    }\n  }\n  useByPath(entityId) {\n    this.items$.pipe(take(1), map(items => {\n      const {\n        path\n      } = this.activatedRoute.snapshot.queryParams;\n      if (!path) {\n        return entityId;\n      }\n      if (!items) {\n        return null;\n      }\n      const entity = items.find(item => item.path === path);\n      return (entity === null || entity === void 0 ? void 0 : entity.id) || null;\n    })).subscribe(id => {\n      this.id$.next(id);\n    });\n  }\n  use(entityId, force) {\n    this.entitySelected$.pipe(take(1)).subscribe(entity => {\n      if (!entity && !force || force && (entity === null || entity === void 0 ? void 0 : entity.id) !== entityId) {\n        this.id$.next(entityId);\n      }\n    });\n  }\n}, _SwHubEntityService.ctorParameters = () => [{\n  type: ActivatedRoute\n}, {\n  type: SwHubEntityDataSource,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [SwHubEntityDataSource]\n  }]\n}], _SwHubEntityService);\nSwHubEntityService = __decorate([Injectable()], SwHubEntityService);\nexport { SwHubEntityService };", "map": {"version": 3, "names": ["__decorate", "Inject", "Injectable", "Optional", "ActivatedRoute", "BehaviorSubject", "combineLatest", "of", "map", "take", "SwHubEntityDataSource", "findEntity", "entity", "id", "Array", "isArray", "child", "found", "every", "item", "SwHubEntityService", "_SwHubEntityService", "constructor", "activatedRoute", "service", "entities", "foundedEntities", "expandedEntities", "Map", "id$", "brief$", "getBrief", "top$", "getEntity", "items$", "pipe", "convertStructure", "entitiesObject", "reduce", "res", "itemSelected$", "items", "length", "find", "entitySelected$", "selected", "structure", "convertItem", "result", "level", "parent", "children", "childrenIds", "currItem", "_objectSpread", "parentId", "push", "for<PERSON>ach", "useByPath", "entityId", "path", "snapshot", "queryParams", "subscribe", "next", "use", "force", "ctorParameters", "type", "decorators", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-entity/sw-hub-entity.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Inject, Injectable, Optional } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\nimport { SwHubEntityDataSource } from './sw-hub-entity-data-source';\nfunction findEntity(entity, id) {\n    if (entity.id === id) {\n        return entity;\n    }\n    if (Array.isArray(entity.child)) {\n        let found = null;\n        entity.child.every(item => {\n            found = findEntity(item, id);\n            return !found;\n        });\n        return found;\n    }\n    return null;\n}\nlet SwHubEntityService = class SwHubEntityService {\n    constructor(activatedRoute, service) {\n        this.activatedRoute = activatedRoute;\n        this.entities = [];\n        this.foundedEntities = [];\n        this.expandedEntities = new Map();\n        this.id$ = new BehaviorSubject(null);\n        this.brief$ = service ? service.getBrief() : of(null);\n        this.top$ = service ? service.getEntity() : of(null);\n        this.items$ = this.top$.pipe(map(entity => {\n            this.entities = this.convertStructure(entity);\n            this.entitiesObject = this.entities.reduce((res, item) => {\n                res[item.id] = item;\n                return res;\n            }, {});\n            return this.entities;\n        }));\n        this.itemSelected$ = combineLatest([this.id$, this.items$]).pipe(map(([id, items]) => {\n            if (items.length === 0) {\n                return null;\n            }\n            if (!id) {\n                return items[0] || null;\n            }\n            return items.find(item => item.id === id) || items[0] || null;\n        }));\n        this.entitySelected$ = combineLatest([this.top$, this.itemSelected$]).pipe(map(([entity, selected]) => {\n            if (selected && entity) {\n                return findEntity(entity, selected.id);\n            }\n            return null;\n        }));\n    }\n    convertStructure(structure) {\n        if (!structure) {\n            return [];\n        }\n        const entities = [];\n        this.convertItem(structure, entities, 0);\n        return entities;\n    }\n    convertItem(item, result, level, parent) {\n        const children = item.child ? [...item.child] : [];\n        const childrenIds = children.map(({ id }) => id);\n        const currItem = { ...item, child: [], level, parentId: parent?.id, children: childrenIds };\n        currItem.parentId = parent?.id;\n        result.push(currItem);\n        if (children) {\n            children.forEach((child) => this.convertItem(child, result, level + 1, currItem));\n        }\n    }\n    useByPath(entityId) {\n        this.items$\n            .pipe(take(1), map(items => {\n            const { path } = this.activatedRoute.snapshot.queryParams;\n            if (!path) {\n                return entityId;\n            }\n            if (!items) {\n                return null;\n            }\n            const entity = items.find(item => item.path === path);\n            return entity?.id || null;\n        }))\n            .subscribe(id => {\n            this.id$.next(id);\n        });\n    }\n    use(entityId, force) {\n        this.entitySelected$\n            .pipe(take(1))\n            .subscribe(entity => {\n            if ((!entity && !force) || (force && entity?.id !== entityId)) {\n                this.id$.next(entityId);\n            }\n        });\n    }\n    static { this.ctorParameters = () => [\n        { type: ActivatedRoute },\n        { type: SwHubEntityDataSource, decorators: [{ type: Optional }, { type: Inject, args: [SwHubEntityDataSource,] }] }\n    ]; }\n};\nSwHubEntityService = __decorate([\n    Injectable()\n], SwHubEntityService);\nexport { SwHubEntityService };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAC5D,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,eAAe,EAAEC,aAAa,EAAEC,EAAE,QAAQ,MAAM;AACzD,SAASC,GAAG,EAAEC,IAAI,QAAQ,gBAAgB;AAC1C,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,SAASC,UAAUA,CAACC,MAAM,EAAEC,EAAE,EAAE;EAC5B,IAAID,MAAM,CAACC,EAAE,KAAKA,EAAE,EAAE;IAClB,OAAOD,MAAM;EACjB;EACA,IAAIE,KAAK,CAACC,OAAO,CAACH,MAAM,CAACI,KAAK,CAAC,EAAE;IAC7B,IAAIC,KAAK,GAAG,IAAI;IAChBL,MAAM,CAACI,KAAK,CAACE,KAAK,CAACC,IAAI,IAAI;MACvBF,KAAK,GAAGN,UAAU,CAACQ,IAAI,EAAEN,EAAE,CAAC;MAC5B,OAAO,CAACI,KAAK;IACjB,CAAC,CAAC;IACF,OAAOA,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,IAAIG,kBAAkB,IAAAC,mBAAA,GAAG,MAAMD,kBAAkB,CAAC;EAC9CE,WAAWA,CAACC,cAAc,EAAEC,OAAO,EAAE;IACjC,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACE,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC,IAAI,CAACC,GAAG,GAAG,IAAIxB,eAAe,CAAC,IAAI,CAAC;IACpC,IAAI,CAACyB,MAAM,GAAGN,OAAO,GAAGA,OAAO,CAACO,QAAQ,CAAC,CAAC,GAAGxB,EAAE,CAAC,IAAI,CAAC;IACrD,IAAI,CAACyB,IAAI,GAAGR,OAAO,GAAGA,OAAO,CAACS,SAAS,CAAC,CAAC,GAAG1B,EAAE,CAAC,IAAI,CAAC;IACpD,IAAI,CAAC2B,MAAM,GAAG,IAAI,CAACF,IAAI,CAACG,IAAI,CAAC3B,GAAG,CAACI,MAAM,IAAI;MACvC,IAAI,CAACa,QAAQ,GAAG,IAAI,CAACW,gBAAgB,CAACxB,MAAM,CAAC;MAC7C,IAAI,CAACyB,cAAc,GAAG,IAAI,CAACZ,QAAQ,CAACa,MAAM,CAAC,CAACC,GAAG,EAAEpB,IAAI,KAAK;QACtDoB,GAAG,CAACpB,IAAI,CAACN,EAAE,CAAC,GAAGM,IAAI;QACnB,OAAOoB,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,OAAO,IAAI,CAACd,QAAQ;IACxB,CAAC,CAAC,CAAC;IACH,IAAI,CAACe,aAAa,GAAGlC,aAAa,CAAC,CAAC,IAAI,CAACuB,GAAG,EAAE,IAAI,CAACK,MAAM,CAAC,CAAC,CAACC,IAAI,CAAC3B,GAAG,CAAC,CAAC,CAACK,EAAE,EAAE4B,KAAK,CAAC,KAAK;MAClF,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,IAAI;MACf;MACA,IAAI,CAAC7B,EAAE,EAAE;QACL,OAAO4B,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;MAC3B;MACA,OAAOA,KAAK,CAACE,IAAI,CAACxB,IAAI,IAAIA,IAAI,CAACN,EAAE,KAAKA,EAAE,CAAC,IAAI4B,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IACjE,CAAC,CAAC,CAAC;IACH,IAAI,CAACG,eAAe,GAAGtC,aAAa,CAAC,CAAC,IAAI,CAAC0B,IAAI,EAAE,IAAI,CAACQ,aAAa,CAAC,CAAC,CAACL,IAAI,CAAC3B,GAAG,CAAC,CAAC,CAACI,MAAM,EAAEiC,QAAQ,CAAC,KAAK;MACnG,IAAIA,QAAQ,IAAIjC,MAAM,EAAE;QACpB,OAAOD,UAAU,CAACC,MAAM,EAAEiC,QAAQ,CAAChC,EAAE,CAAC;MAC1C;MACA,OAAO,IAAI;IACf,CAAC,CAAC,CAAC;EACP;EACAuB,gBAAgBA,CAACU,SAAS,EAAE;IACxB,IAAI,CAACA,SAAS,EAAE;MACZ,OAAO,EAAE;IACb;IACA,MAAMrB,QAAQ,GAAG,EAAE;IACnB,IAAI,CAACsB,WAAW,CAACD,SAAS,EAAErB,QAAQ,EAAE,CAAC,CAAC;IACxC,OAAOA,QAAQ;EACnB;EACAsB,WAAWA,CAAC5B,IAAI,EAAE6B,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE;IACrC,MAAMC,QAAQ,GAAGhC,IAAI,CAACH,KAAK,GAAG,CAAC,GAAGG,IAAI,CAACH,KAAK,CAAC,GAAG,EAAE;IAClD,MAAMoC,WAAW,GAAGD,QAAQ,CAAC3C,GAAG,CAAC,CAAC;MAAEK;IAAG,CAAC,KAAKA,EAAE,CAAC;IAChD,MAAMwC,QAAQ,GAAAC,aAAA,CAAAA,aAAA,KAAQnC,IAAI;MAAEH,KAAK,EAAE,EAAE;MAAEiC,KAAK;MAAEM,QAAQ,EAAEL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAErC,EAAE;MAAEsC,QAAQ,EAAEC;IAAW,EAAE;IAC3FC,QAAQ,CAACE,QAAQ,GAAGL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAErC,EAAE;IAC9BmC,MAAM,CAACQ,IAAI,CAACH,QAAQ,CAAC;IACrB,IAAIF,QAAQ,EAAE;MACVA,QAAQ,CAACM,OAAO,CAAEzC,KAAK,IAAK,IAAI,CAAC+B,WAAW,CAAC/B,KAAK,EAAEgC,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAEI,QAAQ,CAAC,CAAC;IACrF;EACJ;EACAK,SAASA,CAACC,QAAQ,EAAE;IAChB,IAAI,CAACzB,MAAM,CACNC,IAAI,CAAC1B,IAAI,CAAC,CAAC,CAAC,EAAED,GAAG,CAACiC,KAAK,IAAI;MAC5B,MAAM;QAAEmB;MAAK,CAAC,GAAG,IAAI,CAACrC,cAAc,CAACsC,QAAQ,CAACC,WAAW;MACzD,IAAI,CAACF,IAAI,EAAE;QACP,OAAOD,QAAQ;MACnB;MACA,IAAI,CAAClB,KAAK,EAAE;QACR,OAAO,IAAI;MACf;MACA,MAAM7B,MAAM,GAAG6B,KAAK,CAACE,IAAI,CAACxB,IAAI,IAAIA,IAAI,CAACyC,IAAI,KAAKA,IAAI,CAAC;MACrD,OAAO,CAAAhD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,EAAE,KAAI,IAAI;IAC7B,CAAC,CAAC,CAAC,CACEkD,SAAS,CAAClD,EAAE,IAAI;MACjB,IAAI,CAACgB,GAAG,CAACmC,IAAI,CAACnD,EAAE,CAAC;IACrB,CAAC,CAAC;EACN;EACAoD,GAAGA,CAACN,QAAQ,EAAEO,KAAK,EAAE;IACjB,IAAI,CAACtB,eAAe,CACfT,IAAI,CAAC1B,IAAI,CAAC,CAAC,CAAC,CAAC,CACbsD,SAAS,CAACnD,MAAM,IAAI;MACrB,IAAK,CAACA,MAAM,IAAI,CAACsD,KAAK,IAAMA,KAAK,IAAI,CAAAtD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,EAAE,MAAK8C,QAAS,EAAE;QAC3D,IAAI,CAAC9B,GAAG,CAACmC,IAAI,CAACL,QAAQ,CAAC;MAC3B;IACJ,CAAC,CAAC;EACN;AAKJ,CAAC,EAJYtC,mBAAA,CAAK8C,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEhE;AAAe,CAAC,EACxB;EAAEgE,IAAI,EAAE1D,qBAAqB;EAAE2D,UAAU,EAAE,CAAC;IAAED,IAAI,EAAEjE;EAAS,CAAC,EAAE;IAAEiE,IAAI,EAAEnE,MAAM;IAAEqE,IAAI,EAAE,CAAC5D,qBAAqB;EAAG,CAAC;AAAE,CAAC,CACtH,EAAAW,mBAAA,CACJ;AACDD,kBAAkB,GAAGpB,UAAU,CAAC,CAC5BE,UAAU,CAAC,CAAC,CACf,EAAEkB,kBAAkB,CAAC;AACtB,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}