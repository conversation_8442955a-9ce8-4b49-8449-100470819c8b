{"ast": null, "code": "//! moment.js\n//! version : 2.29.1\n//! authors : <PERSON>, <PERSON><PERSON><PERSON>, Moment.js contributors\n//! license : MIT\n//! momentjs.com\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() : typeof define === 'function' && define.amd ? define(factory) : global.moment = factory();\n})(this, function () {\n  'use strict';\n\n  var hookCallback;\n  function hooks() {\n    return hookCallback.apply(null, arguments);\n  }\n\n  // This is done to register the method called with moment()\n  // without creating circular dependencies.\n  function setHookCallback(callback) {\n    hookCallback = callback;\n  }\n  function isArray(input) {\n    return input instanceof Array || Object.prototype.toString.call(input) === '[object Array]';\n  }\n  function isObject(input) {\n    // IE8 will treat undefined and null as object if it wasn't for\n    // input != null\n    return input != null && Object.prototype.toString.call(input) === '[object Object]';\n  }\n  function hasOwnProp(a, b) {\n    return Object.prototype.hasOwnProperty.call(a, b);\n  }\n  function isObjectEmpty(obj) {\n    if (Object.getOwnPropertyNames) {\n      return Object.getOwnPropertyNames(obj).length === 0;\n    } else {\n      var k;\n      for (k in obj) {\n        if (hasOwnProp(obj, k)) {\n          return false;\n        }\n      }\n      return true;\n    }\n  }\n  function isUndefined(input) {\n    return input === void 0;\n  }\n  function isNumber(input) {\n    return typeof input === 'number' || Object.prototype.toString.call(input) === '[object Number]';\n  }\n  function isDate(input) {\n    return input instanceof Date || Object.prototype.toString.call(input) === '[object Date]';\n  }\n  function map(arr, fn) {\n    var res = [],\n      i;\n    for (i = 0; i < arr.length; ++i) {\n      res.push(fn(arr[i], i));\n    }\n    return res;\n  }\n  function extend(a, b) {\n    for (var i in b) {\n      if (hasOwnProp(b, i)) {\n        a[i] = b[i];\n      }\n    }\n    if (hasOwnProp(b, 'toString')) {\n      a.toString = b.toString;\n    }\n    if (hasOwnProp(b, 'valueOf')) {\n      a.valueOf = b.valueOf;\n    }\n    return a;\n  }\n  function createUTC(input, format, locale, strict) {\n    return createLocalOrUTC(input, format, locale, strict, true).utc();\n  }\n  function defaultParsingFlags() {\n    // We need to deep clone this object.\n    return {\n      empty: false,\n      unusedTokens: [],\n      unusedInput: [],\n      overflow: -2,\n      charsLeftOver: 0,\n      nullInput: false,\n      invalidEra: null,\n      invalidMonth: null,\n      invalidFormat: false,\n      userInvalidated: false,\n      iso: false,\n      parsedDateParts: [],\n      era: null,\n      meridiem: null,\n      rfc2822: false,\n      weekdayMismatch: false\n    };\n  }\n  function getParsingFlags(m) {\n    if (m._pf == null) {\n      m._pf = defaultParsingFlags();\n    }\n    return m._pf;\n  }\n  var some;\n  if (Array.prototype.some) {\n    some = Array.prototype.some;\n  } else {\n    some = function (fun) {\n      var t = Object(this),\n        len = t.length >>> 0,\n        i;\n      for (i = 0; i < len; i++) {\n        if (i in t && fun.call(this, t[i], i, t)) {\n          return true;\n        }\n      }\n      return false;\n    };\n  }\n  function isValid(m) {\n    if (m._isValid == null) {\n      var flags = getParsingFlags(m),\n        parsedParts = some.call(flags.parsedDateParts, function (i) {\n          return i != null;\n        }),\n        isNowValid = !isNaN(m._d.getTime()) && flags.overflow < 0 && !flags.empty && !flags.invalidEra && !flags.invalidMonth && !flags.invalidWeekday && !flags.weekdayMismatch && !flags.nullInput && !flags.invalidFormat && !flags.userInvalidated && (!flags.meridiem || flags.meridiem && parsedParts);\n      if (m._strict) {\n        isNowValid = isNowValid && flags.charsLeftOver === 0 && flags.unusedTokens.length === 0 && flags.bigHour === undefined;\n      }\n      if (Object.isFrozen == null || !Object.isFrozen(m)) {\n        m._isValid = isNowValid;\n      } else {\n        return isNowValid;\n      }\n    }\n    return m._isValid;\n  }\n  function createInvalid(flags) {\n    var m = createUTC(NaN);\n    if (flags != null) {\n      extend(getParsingFlags(m), flags);\n    } else {\n      getParsingFlags(m).userInvalidated = true;\n    }\n    return m;\n  }\n\n  // Plugins that add properties should also add the key here (null value),\n  // so we can properly clone ourselves.\n  var momentProperties = hooks.momentProperties = [],\n    updateInProgress = false;\n  function copyConfig(to, from) {\n    var i, prop, val;\n    if (!isUndefined(from._isAMomentObject)) {\n      to._isAMomentObject = from._isAMomentObject;\n    }\n    if (!isUndefined(from._i)) {\n      to._i = from._i;\n    }\n    if (!isUndefined(from._f)) {\n      to._f = from._f;\n    }\n    if (!isUndefined(from._l)) {\n      to._l = from._l;\n    }\n    if (!isUndefined(from._strict)) {\n      to._strict = from._strict;\n    }\n    if (!isUndefined(from._tzm)) {\n      to._tzm = from._tzm;\n    }\n    if (!isUndefined(from._isUTC)) {\n      to._isUTC = from._isUTC;\n    }\n    if (!isUndefined(from._offset)) {\n      to._offset = from._offset;\n    }\n    if (!isUndefined(from._pf)) {\n      to._pf = getParsingFlags(from);\n    }\n    if (!isUndefined(from._locale)) {\n      to._locale = from._locale;\n    }\n    if (momentProperties.length > 0) {\n      for (i = 0; i < momentProperties.length; i++) {\n        prop = momentProperties[i];\n        val = from[prop];\n        if (!isUndefined(val)) {\n          to[prop] = val;\n        }\n      }\n    }\n    return to;\n  }\n\n  // Moment prototype object\n  function Moment(config) {\n    copyConfig(this, config);\n    this._d = new Date(config._d != null ? config._d.getTime() : NaN);\n    if (!this.isValid()) {\n      this._d = new Date(NaN);\n    }\n    // Prevent infinite loop in case updateOffset creates new moment\n    // objects.\n    if (updateInProgress === false) {\n      updateInProgress = true;\n      hooks.updateOffset(this);\n      updateInProgress = false;\n    }\n  }\n  function isMoment(obj) {\n    return obj instanceof Moment || obj != null && obj._isAMomentObject != null;\n  }\n  function warn(msg) {\n    if (hooks.suppressDeprecationWarnings === false && typeof console !== 'undefined' && console.warn) {\n      console.warn('Deprecation warning: ' + msg);\n    }\n  }\n  function deprecate(msg, fn) {\n    var firstTime = true;\n    return extend(function () {\n      if (hooks.deprecationHandler != null) {\n        hooks.deprecationHandler(null, msg);\n      }\n      if (firstTime) {\n        var args = [],\n          arg,\n          i,\n          key;\n        for (i = 0; i < arguments.length; i++) {\n          arg = '';\n          if (typeof arguments[i] === 'object') {\n            arg += '\\n[' + i + '] ';\n            for (key in arguments[0]) {\n              if (hasOwnProp(arguments[0], key)) {\n                arg += key + ': ' + arguments[0][key] + ', ';\n              }\n            }\n            arg = arg.slice(0, -2); // Remove trailing comma and space\n          } else {\n            arg = arguments[i];\n          }\n          args.push(arg);\n        }\n        warn(msg + '\\nArguments: ' + Array.prototype.slice.call(args).join('') + '\\n' + new Error().stack);\n        firstTime = false;\n      }\n      return fn.apply(this, arguments);\n    }, fn);\n  }\n  var deprecations = {};\n  function deprecateSimple(name, msg) {\n    if (hooks.deprecationHandler != null) {\n      hooks.deprecationHandler(name, msg);\n    }\n    if (!deprecations[name]) {\n      warn(msg);\n      deprecations[name] = true;\n    }\n  }\n  hooks.suppressDeprecationWarnings = false;\n  hooks.deprecationHandler = null;\n  function isFunction(input) {\n    return typeof Function !== 'undefined' && input instanceof Function || Object.prototype.toString.call(input) === '[object Function]';\n  }\n  function set(config) {\n    var prop, i;\n    for (i in config) {\n      if (hasOwnProp(config, i)) {\n        prop = config[i];\n        if (isFunction(prop)) {\n          this[i] = prop;\n        } else {\n          this['_' + i] = prop;\n        }\n      }\n    }\n    this._config = config;\n    // Lenient ordinal parsing accepts just a number in addition to\n    // number + (possibly) stuff coming from _dayOfMonthOrdinalParse.\n    // TODO: Remove \"ordinalParse\" fallback in next major release.\n    this._dayOfMonthOrdinalParseLenient = new RegExp((this._dayOfMonthOrdinalParse.source || this._ordinalParse.source) + '|' + /\\d{1,2}/.source);\n  }\n  function mergeConfigs(parentConfig, childConfig) {\n    var res = extend({}, parentConfig),\n      prop;\n    for (prop in childConfig) {\n      if (hasOwnProp(childConfig, prop)) {\n        if (isObject(parentConfig[prop]) && isObject(childConfig[prop])) {\n          res[prop] = {};\n          extend(res[prop], parentConfig[prop]);\n          extend(res[prop], childConfig[prop]);\n        } else if (childConfig[prop] != null) {\n          res[prop] = childConfig[prop];\n        } else {\n          delete res[prop];\n        }\n      }\n    }\n    for (prop in parentConfig) {\n      if (hasOwnProp(parentConfig, prop) && !hasOwnProp(childConfig, prop) && isObject(parentConfig[prop])) {\n        // make sure changes to properties don't modify parent config\n        res[prop] = extend({}, res[prop]);\n      }\n    }\n    return res;\n  }\n  function Locale(config) {\n    if (config != null) {\n      this.set(config);\n    }\n  }\n  var keys;\n  if (Object.keys) {\n    keys = Object.keys;\n  } else {\n    keys = function (obj) {\n      var i,\n        res = [];\n      for (i in obj) {\n        if (hasOwnProp(obj, i)) {\n          res.push(i);\n        }\n      }\n      return res;\n    };\n  }\n  var defaultCalendar = {\n    sameDay: '[Today at] LT',\n    nextDay: '[Tomorrow at] LT',\n    nextWeek: 'dddd [at] LT',\n    lastDay: '[Yesterday at] LT',\n    lastWeek: '[Last] dddd [at] LT',\n    sameElse: 'L'\n  };\n  function calendar(key, mom, now) {\n    var output = this._calendar[key] || this._calendar['sameElse'];\n    return isFunction(output) ? output.call(mom, now) : output;\n  }\n  function zeroFill(number, targetLength, forceSign) {\n    var absNumber = '' + Math.abs(number),\n      zerosToFill = targetLength - absNumber.length,\n      sign = number >= 0;\n    return (sign ? forceSign ? '+' : '' : '-') + Math.pow(10, Math.max(0, zerosToFill)).toString().substr(1) + absNumber;\n  }\n  var formattingTokens = /(\\[[^\\[]*\\])|(\\\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,\n    localFormattingTokens = /(\\[[^\\[]*\\])|(\\\\)?(LTS|LT|LL?L?L?|l{1,4})/g,\n    formatFunctions = {},\n    formatTokenFunctions = {};\n\n  // token:    'M'\n  // padded:   ['MM', 2]\n  // ordinal:  'Mo'\n  // callback: function () { this.month() + 1 }\n  function addFormatToken(token, padded, ordinal, callback) {\n    var func = callback;\n    if (typeof callback === 'string') {\n      func = function () {\n        return this[callback]();\n      };\n    }\n    if (token) {\n      formatTokenFunctions[token] = func;\n    }\n    if (padded) {\n      formatTokenFunctions[padded[0]] = function () {\n        return zeroFill(func.apply(this, arguments), padded[1], padded[2]);\n      };\n    }\n    if (ordinal) {\n      formatTokenFunctions[ordinal] = function () {\n        return this.localeData().ordinal(func.apply(this, arguments), token);\n      };\n    }\n  }\n  function removeFormattingTokens(input) {\n    if (input.match(/\\[[\\s\\S]/)) {\n      return input.replace(/^\\[|\\]$/g, '');\n    }\n    return input.replace(/\\\\/g, '');\n  }\n  function makeFormatFunction(format) {\n    var array = format.match(formattingTokens),\n      i,\n      length;\n    for (i = 0, length = array.length; i < length; i++) {\n      if (formatTokenFunctions[array[i]]) {\n        array[i] = formatTokenFunctions[array[i]];\n      } else {\n        array[i] = removeFormattingTokens(array[i]);\n      }\n    }\n    return function (mom) {\n      var output = '',\n        i;\n      for (i = 0; i < length; i++) {\n        output += isFunction(array[i]) ? array[i].call(mom, format) : array[i];\n      }\n      return output;\n    };\n  }\n\n  // format date using native date object\n  function formatMoment(m, format) {\n    if (!m.isValid()) {\n      return m.localeData().invalidDate();\n    }\n    format = expandFormat(format, m.localeData());\n    formatFunctions[format] = formatFunctions[format] || makeFormatFunction(format);\n    return formatFunctions[format](m);\n  }\n  function expandFormat(format, locale) {\n    var i = 5;\n    function replaceLongDateFormatTokens(input) {\n      return locale.longDateFormat(input) || input;\n    }\n    localFormattingTokens.lastIndex = 0;\n    while (i >= 0 && localFormattingTokens.test(format)) {\n      format = format.replace(localFormattingTokens, replaceLongDateFormatTokens);\n      localFormattingTokens.lastIndex = 0;\n      i -= 1;\n    }\n    return format;\n  }\n  var defaultLongDateFormat = {\n    LTS: 'h:mm:ss A',\n    LT: 'h:mm A',\n    L: 'MM/DD/YYYY',\n    LL: 'MMMM D, YYYY',\n    LLL: 'MMMM D, YYYY h:mm A',\n    LLLL: 'dddd, MMMM D, YYYY h:mm A'\n  };\n  function longDateFormat(key) {\n    var format = this._longDateFormat[key],\n      formatUpper = this._longDateFormat[key.toUpperCase()];\n    if (format || !formatUpper) {\n      return format;\n    }\n    this._longDateFormat[key] = formatUpper.match(formattingTokens).map(function (tok) {\n      if (tok === 'MMMM' || tok === 'MM' || tok === 'DD' || tok === 'dddd') {\n        return tok.slice(1);\n      }\n      return tok;\n    }).join('');\n    return this._longDateFormat[key];\n  }\n  var defaultInvalidDate = 'Invalid date';\n  function invalidDate() {\n    return this._invalidDate;\n  }\n  var defaultOrdinal = '%d',\n    defaultDayOfMonthOrdinalParse = /\\d{1,2}/;\n  function ordinal(number) {\n    return this._ordinal.replace('%d', number);\n  }\n  var defaultRelativeTime = {\n    future: 'in %s',\n    past: '%s ago',\n    s: 'a few seconds',\n    ss: '%d seconds',\n    m: 'a minute',\n    mm: '%d minutes',\n    h: 'an hour',\n    hh: '%d hours',\n    d: 'a day',\n    dd: '%d days',\n    w: 'a week',\n    ww: '%d weeks',\n    M: 'a month',\n    MM: '%d months',\n    y: 'a year',\n    yy: '%d years'\n  };\n  function relativeTime(number, withoutSuffix, string, isFuture) {\n    var output = this._relativeTime[string];\n    return isFunction(output) ? output(number, withoutSuffix, string, isFuture) : output.replace(/%d/i, number);\n  }\n  function pastFuture(diff, output) {\n    var format = this._relativeTime[diff > 0 ? 'future' : 'past'];\n    return isFunction(format) ? format(output) : format.replace(/%s/i, output);\n  }\n  var aliases = {};\n  function addUnitAlias(unit, shorthand) {\n    var lowerCase = unit.toLowerCase();\n    aliases[lowerCase] = aliases[lowerCase + 's'] = aliases[shorthand] = unit;\n  }\n  function normalizeUnits(units) {\n    return typeof units === 'string' ? aliases[units] || aliases[units.toLowerCase()] : undefined;\n  }\n  function normalizeObjectUnits(inputObject) {\n    var normalizedInput = {},\n      normalizedProp,\n      prop;\n    for (prop in inputObject) {\n      if (hasOwnProp(inputObject, prop)) {\n        normalizedProp = normalizeUnits(prop);\n        if (normalizedProp) {\n          normalizedInput[normalizedProp] = inputObject[prop];\n        }\n      }\n    }\n    return normalizedInput;\n  }\n  var priorities = {};\n  function addUnitPriority(unit, priority) {\n    priorities[unit] = priority;\n  }\n  function getPrioritizedUnits(unitsObj) {\n    var units = [],\n      u;\n    for (u in unitsObj) {\n      if (hasOwnProp(unitsObj, u)) {\n        units.push({\n          unit: u,\n          priority: priorities[u]\n        });\n      }\n    }\n    units.sort(function (a, b) {\n      return a.priority - b.priority;\n    });\n    return units;\n  }\n  function isLeapYear(year) {\n    return year % 4 === 0 && year % 100 !== 0 || year % 400 === 0;\n  }\n  function absFloor(number) {\n    if (number < 0) {\n      // -0 -> 0\n      return Math.ceil(number) || 0;\n    } else {\n      return Math.floor(number);\n    }\n  }\n  function toInt(argumentForCoercion) {\n    var coercedNumber = +argumentForCoercion,\n      value = 0;\n    if (coercedNumber !== 0 && isFinite(coercedNumber)) {\n      value = absFloor(coercedNumber);\n    }\n    return value;\n  }\n  function makeGetSet(unit, keepTime) {\n    return function (value) {\n      if (value != null) {\n        set$1(this, unit, value);\n        hooks.updateOffset(this, keepTime);\n        return this;\n      } else {\n        return get(this, unit);\n      }\n    };\n  }\n  function get(mom, unit) {\n    return mom.isValid() ? mom._d['get' + (mom._isUTC ? 'UTC' : '') + unit]() : NaN;\n  }\n  function set$1(mom, unit, value) {\n    if (mom.isValid() && !isNaN(value)) {\n      if (unit === 'FullYear' && isLeapYear(mom.year()) && mom.month() === 1 && mom.date() === 29) {\n        value = toInt(value);\n        mom._d['set' + (mom._isUTC ? 'UTC' : '') + unit](value, mom.month(), daysInMonth(value, mom.month()));\n      } else {\n        mom._d['set' + (mom._isUTC ? 'UTC' : '') + unit](value);\n      }\n    }\n  }\n\n  // MOMENTS\n\n  function stringGet(units) {\n    units = normalizeUnits(units);\n    if (isFunction(this[units])) {\n      return this[units]();\n    }\n    return this;\n  }\n  function stringSet(units, value) {\n    if (typeof units === 'object') {\n      units = normalizeObjectUnits(units);\n      var prioritized = getPrioritizedUnits(units),\n        i;\n      for (i = 0; i < prioritized.length; i++) {\n        this[prioritized[i].unit](units[prioritized[i].unit]);\n      }\n    } else {\n      units = normalizeUnits(units);\n      if (isFunction(this[units])) {\n        return this[units](value);\n      }\n    }\n    return this;\n  }\n  var match1 = /\\d/,\n    //       0 - 9\n    match2 = /\\d\\d/,\n    //      00 - 99\n    match3 = /\\d{3}/,\n    //     000 - 999\n    match4 = /\\d{4}/,\n    //    0000 - 9999\n    match6 = /[+-]?\\d{6}/,\n    // -999999 - 999999\n    match1to2 = /\\d\\d?/,\n    //       0 - 99\n    match3to4 = /\\d\\d\\d\\d?/,\n    //     999 - 9999\n    match5to6 = /\\d\\d\\d\\d\\d\\d?/,\n    //   99999 - 999999\n    match1to3 = /\\d{1,3}/,\n    //       0 - 999\n    match1to4 = /\\d{1,4}/,\n    //       0 - 9999\n    match1to6 = /[+-]?\\d{1,6}/,\n    // -999999 - 999999\n    matchUnsigned = /\\d+/,\n    //       0 - inf\n    matchSigned = /[+-]?\\d+/,\n    //    -inf - inf\n    matchOffset = /Z|[+-]\\d\\d:?\\d\\d/gi,\n    // +00:00 -00:00 +0000 -0000 or Z\n    matchShortOffset = /Z|[+-]\\d\\d(?::?\\d\\d)?/gi,\n    // +00 -00 +00:00 -00:00 +0000 -0000 or Z\n    matchTimestamp = /[+-]?\\d+(\\.\\d{1,3})?/,\n    // 123456789 123456789.123\n    // any word (or two) characters or numbers including two/three word month in arabic.\n    // includes scottish gaelic two word and hyphenated months\n    matchWord = /[0-9]{0,256}['a-z\\u00A0-\\u05FF\\u0700-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFF07\\uFF10-\\uFFEF]{1,256}|[\\u0600-\\u06FF\\/]{1,256}(\\s*?[\\u0600-\\u06FF]{1,256}){1,2}/i,\n    regexes;\n  regexes = {};\n  function addRegexToken(token, regex, strictRegex) {\n    regexes[token] = isFunction(regex) ? regex : function (isStrict, localeData) {\n      return isStrict && strictRegex ? strictRegex : regex;\n    };\n  }\n  function getParseRegexForToken(token, config) {\n    if (!hasOwnProp(regexes, token)) {\n      return new RegExp(unescapeFormat(token));\n    }\n    return regexes[token](config._strict, config._locale);\n  }\n\n  // Code from http://stackoverflow.com/questions/3561493/is-there-a-regexp-escape-function-in-javascript\n  function unescapeFormat(s) {\n    return regexEscape(s.replace('\\\\', '').replace(/\\\\(\\[)|\\\\(\\])|\\[([^\\]\\[]*)\\]|\\\\(.)/g, function (matched, p1, p2, p3, p4) {\n      return p1 || p2 || p3 || p4;\n    }));\n  }\n  function regexEscape(s) {\n    return s.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n  }\n  var tokens = {};\n  function addParseToken(token, callback) {\n    var i,\n      func = callback;\n    if (typeof token === 'string') {\n      token = [token];\n    }\n    if (isNumber(callback)) {\n      func = function (input, array) {\n        array[callback] = toInt(input);\n      };\n    }\n    for (i = 0; i < token.length; i++) {\n      tokens[token[i]] = func;\n    }\n  }\n  function addWeekParseToken(token, callback) {\n    addParseToken(token, function (input, array, config, token) {\n      config._w = config._w || {};\n      callback(input, config._w, config, token);\n    });\n  }\n  function addTimeToArrayFromToken(token, input, config) {\n    if (input != null && hasOwnProp(tokens, token)) {\n      tokens[token](input, config._a, config, token);\n    }\n  }\n  var YEAR = 0,\n    MONTH = 1,\n    DATE = 2,\n    HOUR = 3,\n    MINUTE = 4,\n    SECOND = 5,\n    MILLISECOND = 6,\n    WEEK = 7,\n    WEEKDAY = 8;\n  function mod(n, x) {\n    return (n % x + x) % x;\n  }\n  var indexOf;\n  if (Array.prototype.indexOf) {\n    indexOf = Array.prototype.indexOf;\n  } else {\n    indexOf = function (o) {\n      // I know\n      var i;\n      for (i = 0; i < this.length; ++i) {\n        if (this[i] === o) {\n          return i;\n        }\n      }\n      return -1;\n    };\n  }\n  function daysInMonth(year, month) {\n    if (isNaN(year) || isNaN(month)) {\n      return NaN;\n    }\n    var modMonth = mod(month, 12);\n    year += (month - modMonth) / 12;\n    return modMonth === 1 ? isLeapYear(year) ? 29 : 28 : 31 - modMonth % 7 % 2;\n  }\n\n  // FORMATTING\n\n  addFormatToken('M', ['MM', 2], 'Mo', function () {\n    return this.month() + 1;\n  });\n  addFormatToken('MMM', 0, 0, function (format) {\n    return this.localeData().monthsShort(this, format);\n  });\n  addFormatToken('MMMM', 0, 0, function (format) {\n    return this.localeData().months(this, format);\n  });\n\n  // ALIASES\n\n  addUnitAlias('month', 'M');\n\n  // PRIORITY\n\n  addUnitPriority('month', 8);\n\n  // PARSING\n\n  addRegexToken('M', match1to2);\n  addRegexToken('MM', match1to2, match2);\n  addRegexToken('MMM', function (isStrict, locale) {\n    return locale.monthsShortRegex(isStrict);\n  });\n  addRegexToken('MMMM', function (isStrict, locale) {\n    return locale.monthsRegex(isStrict);\n  });\n  addParseToken(['M', 'MM'], function (input, array) {\n    array[MONTH] = toInt(input) - 1;\n  });\n  addParseToken(['MMM', 'MMMM'], function (input, array, config, token) {\n    var month = config._locale.monthsParse(input, token, config._strict);\n    // if we didn't find a month name, mark the date as invalid.\n    if (month != null) {\n      array[MONTH] = month;\n    } else {\n      getParsingFlags(config).invalidMonth = input;\n    }\n  });\n\n  // LOCALES\n\n  var defaultLocaleMonths = 'January_February_March_April_May_June_July_August_September_October_November_December'.split('_'),\n    defaultLocaleMonthsShort = 'Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec'.split('_'),\n    MONTHS_IN_FORMAT = /D[oD]?(\\[[^\\[\\]]*\\]|\\s)+MMMM?/,\n    defaultMonthsShortRegex = matchWord,\n    defaultMonthsRegex = matchWord;\n  function localeMonths(m, format) {\n    if (!m) {\n      return isArray(this._months) ? this._months : this._months['standalone'];\n    }\n    return isArray(this._months) ? this._months[m.month()] : this._months[(this._months.isFormat || MONTHS_IN_FORMAT).test(format) ? 'format' : 'standalone'][m.month()];\n  }\n  function localeMonthsShort(m, format) {\n    if (!m) {\n      return isArray(this._monthsShort) ? this._monthsShort : this._monthsShort['standalone'];\n    }\n    return isArray(this._monthsShort) ? this._monthsShort[m.month()] : this._monthsShort[MONTHS_IN_FORMAT.test(format) ? 'format' : 'standalone'][m.month()];\n  }\n  function handleStrictParse(monthName, format, strict) {\n    var i,\n      ii,\n      mom,\n      llc = monthName.toLocaleLowerCase();\n    if (!this._monthsParse) {\n      // this is not used\n      this._monthsParse = [];\n      this._longMonthsParse = [];\n      this._shortMonthsParse = [];\n      for (i = 0; i < 12; ++i) {\n        mom = createUTC([2000, i]);\n        this._shortMonthsParse[i] = this.monthsShort(mom, '').toLocaleLowerCase();\n        this._longMonthsParse[i] = this.months(mom, '').toLocaleLowerCase();\n      }\n    }\n    if (strict) {\n      if (format === 'MMM') {\n        ii = indexOf.call(this._shortMonthsParse, llc);\n        return ii !== -1 ? ii : null;\n      } else {\n        ii = indexOf.call(this._longMonthsParse, llc);\n        return ii !== -1 ? ii : null;\n      }\n    } else {\n      if (format === 'MMM') {\n        ii = indexOf.call(this._shortMonthsParse, llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = indexOf.call(this._longMonthsParse, llc);\n        return ii !== -1 ? ii : null;\n      } else {\n        ii = indexOf.call(this._longMonthsParse, llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = indexOf.call(this._shortMonthsParse, llc);\n        return ii !== -1 ? ii : null;\n      }\n    }\n  }\n  function localeMonthsParse(monthName, format, strict) {\n    var i, mom, regex;\n    if (this._monthsParseExact) {\n      return handleStrictParse.call(this, monthName, format, strict);\n    }\n    if (!this._monthsParse) {\n      this._monthsParse = [];\n      this._longMonthsParse = [];\n      this._shortMonthsParse = [];\n    }\n\n    // TODO: add sorting\n    // Sorting makes sure if one month (or abbr) is a prefix of another\n    // see sorting in computeMonthsParse\n    for (i = 0; i < 12; i++) {\n      // make the regex if we don't have it already\n      mom = createUTC([2000, i]);\n      if (strict && !this._longMonthsParse[i]) {\n        this._longMonthsParse[i] = new RegExp('^' + this.months(mom, '').replace('.', '') + '$', 'i');\n        this._shortMonthsParse[i] = new RegExp('^' + this.monthsShort(mom, '').replace('.', '') + '$', 'i');\n      }\n      if (!strict && !this._monthsParse[i]) {\n        regex = '^' + this.months(mom, '') + '|^' + this.monthsShort(mom, '');\n        this._monthsParse[i] = new RegExp(regex.replace('.', ''), 'i');\n      }\n      // test the regex\n      if (strict && format === 'MMMM' && this._longMonthsParse[i].test(monthName)) {\n        return i;\n      } else if (strict && format === 'MMM' && this._shortMonthsParse[i].test(monthName)) {\n        return i;\n      } else if (!strict && this._monthsParse[i].test(monthName)) {\n        return i;\n      }\n    }\n  }\n\n  // MOMENTS\n\n  function setMonth(mom, value) {\n    var dayOfMonth;\n    if (!mom.isValid()) {\n      // No op\n      return mom;\n    }\n    if (typeof value === 'string') {\n      if (/^\\d+$/.test(value)) {\n        value = toInt(value);\n      } else {\n        value = mom.localeData().monthsParse(value);\n        // TODO: Another silent failure?\n        if (!isNumber(value)) {\n          return mom;\n        }\n      }\n    }\n    dayOfMonth = Math.min(mom.date(), daysInMonth(mom.year(), value));\n    mom._d['set' + (mom._isUTC ? 'UTC' : '') + 'Month'](value, dayOfMonth);\n    return mom;\n  }\n  function getSetMonth(value) {\n    if (value != null) {\n      setMonth(this, value);\n      hooks.updateOffset(this, true);\n      return this;\n    } else {\n      return get(this, 'Month');\n    }\n  }\n  function getDaysInMonth() {\n    return daysInMonth(this.year(), this.month());\n  }\n  function monthsShortRegex(isStrict) {\n    if (this._monthsParseExact) {\n      if (!hasOwnProp(this, '_monthsRegex')) {\n        computeMonthsParse.call(this);\n      }\n      if (isStrict) {\n        return this._monthsShortStrictRegex;\n      } else {\n        return this._monthsShortRegex;\n      }\n    } else {\n      if (!hasOwnProp(this, '_monthsShortRegex')) {\n        this._monthsShortRegex = defaultMonthsShortRegex;\n      }\n      return this._monthsShortStrictRegex && isStrict ? this._monthsShortStrictRegex : this._monthsShortRegex;\n    }\n  }\n  function monthsRegex(isStrict) {\n    if (this._monthsParseExact) {\n      if (!hasOwnProp(this, '_monthsRegex')) {\n        computeMonthsParse.call(this);\n      }\n      if (isStrict) {\n        return this._monthsStrictRegex;\n      } else {\n        return this._monthsRegex;\n      }\n    } else {\n      if (!hasOwnProp(this, '_monthsRegex')) {\n        this._monthsRegex = defaultMonthsRegex;\n      }\n      return this._monthsStrictRegex && isStrict ? this._monthsStrictRegex : this._monthsRegex;\n    }\n  }\n  function computeMonthsParse() {\n    function cmpLenRev(a, b) {\n      return b.length - a.length;\n    }\n    var shortPieces = [],\n      longPieces = [],\n      mixedPieces = [],\n      i,\n      mom;\n    for (i = 0; i < 12; i++) {\n      // make the regex if we don't have it already\n      mom = createUTC([2000, i]);\n      shortPieces.push(this.monthsShort(mom, ''));\n      longPieces.push(this.months(mom, ''));\n      mixedPieces.push(this.months(mom, ''));\n      mixedPieces.push(this.monthsShort(mom, ''));\n    }\n    // Sorting makes sure if one month (or abbr) is a prefix of another it\n    // will match the longer piece.\n    shortPieces.sort(cmpLenRev);\n    longPieces.sort(cmpLenRev);\n    mixedPieces.sort(cmpLenRev);\n    for (i = 0; i < 12; i++) {\n      shortPieces[i] = regexEscape(shortPieces[i]);\n      longPieces[i] = regexEscape(longPieces[i]);\n    }\n    for (i = 0; i < 24; i++) {\n      mixedPieces[i] = regexEscape(mixedPieces[i]);\n    }\n    this._monthsRegex = new RegExp('^(' + mixedPieces.join('|') + ')', 'i');\n    this._monthsShortRegex = this._monthsRegex;\n    this._monthsStrictRegex = new RegExp('^(' + longPieces.join('|') + ')', 'i');\n    this._monthsShortStrictRegex = new RegExp('^(' + shortPieces.join('|') + ')', 'i');\n  }\n\n  // FORMATTING\n\n  addFormatToken('Y', 0, 0, function () {\n    var y = this.year();\n    return y <= 9999 ? zeroFill(y, 4) : '+' + y;\n  });\n  addFormatToken(0, ['YY', 2], 0, function () {\n    return this.year() % 100;\n  });\n  addFormatToken(0, ['YYYY', 4], 0, 'year');\n  addFormatToken(0, ['YYYYY', 5], 0, 'year');\n  addFormatToken(0, ['YYYYYY', 6, true], 0, 'year');\n\n  // ALIASES\n\n  addUnitAlias('year', 'y');\n\n  // PRIORITIES\n\n  addUnitPriority('year', 1);\n\n  // PARSING\n\n  addRegexToken('Y', matchSigned);\n  addRegexToken('YY', match1to2, match2);\n  addRegexToken('YYYY', match1to4, match4);\n  addRegexToken('YYYYY', match1to6, match6);\n  addRegexToken('YYYYYY', match1to6, match6);\n  addParseToken(['YYYYY', 'YYYYYY'], YEAR);\n  addParseToken('YYYY', function (input, array) {\n    array[YEAR] = input.length === 2 ? hooks.parseTwoDigitYear(input) : toInt(input);\n  });\n  addParseToken('YY', function (input, array) {\n    array[YEAR] = hooks.parseTwoDigitYear(input);\n  });\n  addParseToken('Y', function (input, array) {\n    array[YEAR] = parseInt(input, 10);\n  });\n\n  // HELPERS\n\n  function daysInYear(year) {\n    return isLeapYear(year) ? 366 : 365;\n  }\n\n  // HOOKS\n\n  hooks.parseTwoDigitYear = function (input) {\n    return toInt(input) + (toInt(input) > 68 ? 1900 : 2000);\n  };\n\n  // MOMENTS\n\n  var getSetYear = makeGetSet('FullYear', true);\n  function getIsLeapYear() {\n    return isLeapYear(this.year());\n  }\n  function createDate(y, m, d, h, M, s, ms) {\n    // can't just apply() to create a date:\n    // https://stackoverflow.com/q/181348\n    var date;\n    // the date constructor remaps years 0-99 to 1900-1999\n    if (y < 100 && y >= 0) {\n      // preserve leap years using a full 400 year cycle, then reset\n      date = new Date(y + 400, m, d, h, M, s, ms);\n      if (isFinite(date.getFullYear())) {\n        date.setFullYear(y);\n      }\n    } else {\n      date = new Date(y, m, d, h, M, s, ms);\n    }\n    return date;\n  }\n  function createUTCDate(y) {\n    var date, args;\n    // the Date.UTC function remaps years 0-99 to 1900-1999\n    if (y < 100 && y >= 0) {\n      args = Array.prototype.slice.call(arguments);\n      // preserve leap years using a full 400 year cycle, then reset\n      args[0] = y + 400;\n      date = new Date(Date.UTC.apply(null, args));\n      if (isFinite(date.getUTCFullYear())) {\n        date.setUTCFullYear(y);\n      }\n    } else {\n      date = new Date(Date.UTC.apply(null, arguments));\n    }\n    return date;\n  }\n\n  // start-of-first-week - start-of-year\n  function firstWeekOffset(year, dow, doy) {\n    var\n      // first-week day -- which january is always in the first week (4 for iso, 1 for other)\n      fwd = 7 + dow - doy,\n      // first-week day local weekday -- which local weekday is fwd\n      fwdlw = (7 + createUTCDate(year, 0, fwd).getUTCDay() - dow) % 7;\n    return -fwdlw + fwd - 1;\n  }\n\n  // https://en.wikipedia.org/wiki/ISO_week_date#Calculating_a_date_given_the_year.2C_week_number_and_weekday\n  function dayOfYearFromWeeks(year, week, weekday, dow, doy) {\n    var localWeekday = (7 + weekday - dow) % 7,\n      weekOffset = firstWeekOffset(year, dow, doy),\n      dayOfYear = 1 + 7 * (week - 1) + localWeekday + weekOffset,\n      resYear,\n      resDayOfYear;\n    if (dayOfYear <= 0) {\n      resYear = year - 1;\n      resDayOfYear = daysInYear(resYear) + dayOfYear;\n    } else if (dayOfYear > daysInYear(year)) {\n      resYear = year + 1;\n      resDayOfYear = dayOfYear - daysInYear(year);\n    } else {\n      resYear = year;\n      resDayOfYear = dayOfYear;\n    }\n    return {\n      year: resYear,\n      dayOfYear: resDayOfYear\n    };\n  }\n  function weekOfYear(mom, dow, doy) {\n    var weekOffset = firstWeekOffset(mom.year(), dow, doy),\n      week = Math.floor((mom.dayOfYear() - weekOffset - 1) / 7) + 1,\n      resWeek,\n      resYear;\n    if (week < 1) {\n      resYear = mom.year() - 1;\n      resWeek = week + weeksInYear(resYear, dow, doy);\n    } else if (week > weeksInYear(mom.year(), dow, doy)) {\n      resWeek = week - weeksInYear(mom.year(), dow, doy);\n      resYear = mom.year() + 1;\n    } else {\n      resYear = mom.year();\n      resWeek = week;\n    }\n    return {\n      week: resWeek,\n      year: resYear\n    };\n  }\n  function weeksInYear(year, dow, doy) {\n    var weekOffset = firstWeekOffset(year, dow, doy),\n      weekOffsetNext = firstWeekOffset(year + 1, dow, doy);\n    return (daysInYear(year) - weekOffset + weekOffsetNext) / 7;\n  }\n\n  // FORMATTING\n\n  addFormatToken('w', ['ww', 2], 'wo', 'week');\n  addFormatToken('W', ['WW', 2], 'Wo', 'isoWeek');\n\n  // ALIASES\n\n  addUnitAlias('week', 'w');\n  addUnitAlias('isoWeek', 'W');\n\n  // PRIORITIES\n\n  addUnitPriority('week', 5);\n  addUnitPriority('isoWeek', 5);\n\n  // PARSING\n\n  addRegexToken('w', match1to2);\n  addRegexToken('ww', match1to2, match2);\n  addRegexToken('W', match1to2);\n  addRegexToken('WW', match1to2, match2);\n  addWeekParseToken(['w', 'ww', 'W', 'WW'], function (input, week, config, token) {\n    week[token.substr(0, 1)] = toInt(input);\n  });\n\n  // HELPERS\n\n  // LOCALES\n\n  function localeWeek(mom) {\n    return weekOfYear(mom, this._week.dow, this._week.doy).week;\n  }\n  var defaultLocaleWeek = {\n    dow: 0,\n    // Sunday is the first day of the week.\n    doy: 6 // The week that contains Jan 6th is the first week of the year.\n  };\n  function localeFirstDayOfWeek() {\n    return this._week.dow;\n  }\n  function localeFirstDayOfYear() {\n    return this._week.doy;\n  }\n\n  // MOMENTS\n\n  function getSetWeek(input) {\n    var week = this.localeData().week(this);\n    return input == null ? week : this.add((input - week) * 7, 'd');\n  }\n  function getSetISOWeek(input) {\n    var week = weekOfYear(this, 1, 4).week;\n    return input == null ? week : this.add((input - week) * 7, 'd');\n  }\n\n  // FORMATTING\n\n  addFormatToken('d', 0, 'do', 'day');\n  addFormatToken('dd', 0, 0, function (format) {\n    return this.localeData().weekdaysMin(this, format);\n  });\n  addFormatToken('ddd', 0, 0, function (format) {\n    return this.localeData().weekdaysShort(this, format);\n  });\n  addFormatToken('dddd', 0, 0, function (format) {\n    return this.localeData().weekdays(this, format);\n  });\n  addFormatToken('e', 0, 0, 'weekday');\n  addFormatToken('E', 0, 0, 'isoWeekday');\n\n  // ALIASES\n\n  addUnitAlias('day', 'd');\n  addUnitAlias('weekday', 'e');\n  addUnitAlias('isoWeekday', 'E');\n\n  // PRIORITY\n  addUnitPriority('day', 11);\n  addUnitPriority('weekday', 11);\n  addUnitPriority('isoWeekday', 11);\n\n  // PARSING\n\n  addRegexToken('d', match1to2);\n  addRegexToken('e', match1to2);\n  addRegexToken('E', match1to2);\n  addRegexToken('dd', function (isStrict, locale) {\n    return locale.weekdaysMinRegex(isStrict);\n  });\n  addRegexToken('ddd', function (isStrict, locale) {\n    return locale.weekdaysShortRegex(isStrict);\n  });\n  addRegexToken('dddd', function (isStrict, locale) {\n    return locale.weekdaysRegex(isStrict);\n  });\n  addWeekParseToken(['dd', 'ddd', 'dddd'], function (input, week, config, token) {\n    var weekday = config._locale.weekdaysParse(input, token, config._strict);\n    // if we didn't get a weekday name, mark the date as invalid\n    if (weekday != null) {\n      week.d = weekday;\n    } else {\n      getParsingFlags(config).invalidWeekday = input;\n    }\n  });\n  addWeekParseToken(['d', 'e', 'E'], function (input, week, config, token) {\n    week[token] = toInt(input);\n  });\n\n  // HELPERS\n\n  function parseWeekday(input, locale) {\n    if (typeof input !== 'string') {\n      return input;\n    }\n    if (!isNaN(input)) {\n      return parseInt(input, 10);\n    }\n    input = locale.weekdaysParse(input);\n    if (typeof input === 'number') {\n      return input;\n    }\n    return null;\n  }\n  function parseIsoWeekday(input, locale) {\n    if (typeof input === 'string') {\n      return locale.weekdaysParse(input) % 7 || 7;\n    }\n    return isNaN(input) ? null : input;\n  }\n\n  // LOCALES\n  function shiftWeekdays(ws, n) {\n    return ws.slice(n, 7).concat(ws.slice(0, n));\n  }\n  var defaultLocaleWeekdays = 'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split('_'),\n    defaultLocaleWeekdaysShort = 'Sun_Mon_Tue_Wed_Thu_Fri_Sat'.split('_'),\n    defaultLocaleWeekdaysMin = 'Su_Mo_Tu_We_Th_Fr_Sa'.split('_'),\n    defaultWeekdaysRegex = matchWord,\n    defaultWeekdaysShortRegex = matchWord,\n    defaultWeekdaysMinRegex = matchWord;\n  function localeWeekdays(m, format) {\n    var weekdays = isArray(this._weekdays) ? this._weekdays : this._weekdays[m && m !== true && this._weekdays.isFormat.test(format) ? 'format' : 'standalone'];\n    return m === true ? shiftWeekdays(weekdays, this._week.dow) : m ? weekdays[m.day()] : weekdays;\n  }\n  function localeWeekdaysShort(m) {\n    return m === true ? shiftWeekdays(this._weekdaysShort, this._week.dow) : m ? this._weekdaysShort[m.day()] : this._weekdaysShort;\n  }\n  function localeWeekdaysMin(m) {\n    return m === true ? shiftWeekdays(this._weekdaysMin, this._week.dow) : m ? this._weekdaysMin[m.day()] : this._weekdaysMin;\n  }\n  function handleStrictParse$1(weekdayName, format, strict) {\n    var i,\n      ii,\n      mom,\n      llc = weekdayName.toLocaleLowerCase();\n    if (!this._weekdaysParse) {\n      this._weekdaysParse = [];\n      this._shortWeekdaysParse = [];\n      this._minWeekdaysParse = [];\n      for (i = 0; i < 7; ++i) {\n        mom = createUTC([2000, 1]).day(i);\n        this._minWeekdaysParse[i] = this.weekdaysMin(mom, '').toLocaleLowerCase();\n        this._shortWeekdaysParse[i] = this.weekdaysShort(mom, '').toLocaleLowerCase();\n        this._weekdaysParse[i] = this.weekdays(mom, '').toLocaleLowerCase();\n      }\n    }\n    if (strict) {\n      if (format === 'dddd') {\n        ii = indexOf.call(this._weekdaysParse, llc);\n        return ii !== -1 ? ii : null;\n      } else if (format === 'ddd') {\n        ii = indexOf.call(this._shortWeekdaysParse, llc);\n        return ii !== -1 ? ii : null;\n      } else {\n        ii = indexOf.call(this._minWeekdaysParse, llc);\n        return ii !== -1 ? ii : null;\n      }\n    } else {\n      if (format === 'dddd') {\n        ii = indexOf.call(this._weekdaysParse, llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = indexOf.call(this._shortWeekdaysParse, llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = indexOf.call(this._minWeekdaysParse, llc);\n        return ii !== -1 ? ii : null;\n      } else if (format === 'ddd') {\n        ii = indexOf.call(this._shortWeekdaysParse, llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = indexOf.call(this._weekdaysParse, llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = indexOf.call(this._minWeekdaysParse, llc);\n        return ii !== -1 ? ii : null;\n      } else {\n        ii = indexOf.call(this._minWeekdaysParse, llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = indexOf.call(this._weekdaysParse, llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = indexOf.call(this._shortWeekdaysParse, llc);\n        return ii !== -1 ? ii : null;\n      }\n    }\n  }\n  function localeWeekdaysParse(weekdayName, format, strict) {\n    var i, mom, regex;\n    if (this._weekdaysParseExact) {\n      return handleStrictParse$1.call(this, weekdayName, format, strict);\n    }\n    if (!this._weekdaysParse) {\n      this._weekdaysParse = [];\n      this._minWeekdaysParse = [];\n      this._shortWeekdaysParse = [];\n      this._fullWeekdaysParse = [];\n    }\n    for (i = 0; i < 7; i++) {\n      // make the regex if we don't have it already\n\n      mom = createUTC([2000, 1]).day(i);\n      if (strict && !this._fullWeekdaysParse[i]) {\n        this._fullWeekdaysParse[i] = new RegExp('^' + this.weekdays(mom, '').replace('.', '\\\\.?') + '$', 'i');\n        this._shortWeekdaysParse[i] = new RegExp('^' + this.weekdaysShort(mom, '').replace('.', '\\\\.?') + '$', 'i');\n        this._minWeekdaysParse[i] = new RegExp('^' + this.weekdaysMin(mom, '').replace('.', '\\\\.?') + '$', 'i');\n      }\n      if (!this._weekdaysParse[i]) {\n        regex = '^' + this.weekdays(mom, '') + '|^' + this.weekdaysShort(mom, '') + '|^' + this.weekdaysMin(mom, '');\n        this._weekdaysParse[i] = new RegExp(regex.replace('.', ''), 'i');\n      }\n      // test the regex\n      if (strict && format === 'dddd' && this._fullWeekdaysParse[i].test(weekdayName)) {\n        return i;\n      } else if (strict && format === 'ddd' && this._shortWeekdaysParse[i].test(weekdayName)) {\n        return i;\n      } else if (strict && format === 'dd' && this._minWeekdaysParse[i].test(weekdayName)) {\n        return i;\n      } else if (!strict && this._weekdaysParse[i].test(weekdayName)) {\n        return i;\n      }\n    }\n  }\n\n  // MOMENTS\n\n  function getSetDayOfWeek(input) {\n    if (!this.isValid()) {\n      return input != null ? this : NaN;\n    }\n    var day = this._isUTC ? this._d.getUTCDay() : this._d.getDay();\n    if (input != null) {\n      input = parseWeekday(input, this.localeData());\n      return this.add(input - day, 'd');\n    } else {\n      return day;\n    }\n  }\n  function getSetLocaleDayOfWeek(input) {\n    if (!this.isValid()) {\n      return input != null ? this : NaN;\n    }\n    var weekday = (this.day() + 7 - this.localeData()._week.dow) % 7;\n    return input == null ? weekday : this.add(input - weekday, 'd');\n  }\n  function getSetISODayOfWeek(input) {\n    if (!this.isValid()) {\n      return input != null ? this : NaN;\n    }\n\n    // behaves the same as moment#day except\n    // as a getter, returns 7 instead of 0 (1-7 range instead of 0-6)\n    // as a setter, sunday should belong to the previous week.\n\n    if (input != null) {\n      var weekday = parseIsoWeekday(input, this.localeData());\n      return this.day(this.day() % 7 ? weekday : weekday - 7);\n    } else {\n      return this.day() || 7;\n    }\n  }\n  function weekdaysRegex(isStrict) {\n    if (this._weekdaysParseExact) {\n      if (!hasOwnProp(this, '_weekdaysRegex')) {\n        computeWeekdaysParse.call(this);\n      }\n      if (isStrict) {\n        return this._weekdaysStrictRegex;\n      } else {\n        return this._weekdaysRegex;\n      }\n    } else {\n      if (!hasOwnProp(this, '_weekdaysRegex')) {\n        this._weekdaysRegex = defaultWeekdaysRegex;\n      }\n      return this._weekdaysStrictRegex && isStrict ? this._weekdaysStrictRegex : this._weekdaysRegex;\n    }\n  }\n  function weekdaysShortRegex(isStrict) {\n    if (this._weekdaysParseExact) {\n      if (!hasOwnProp(this, '_weekdaysRegex')) {\n        computeWeekdaysParse.call(this);\n      }\n      if (isStrict) {\n        return this._weekdaysShortStrictRegex;\n      } else {\n        return this._weekdaysShortRegex;\n      }\n    } else {\n      if (!hasOwnProp(this, '_weekdaysShortRegex')) {\n        this._weekdaysShortRegex = defaultWeekdaysShortRegex;\n      }\n      return this._weekdaysShortStrictRegex && isStrict ? this._weekdaysShortStrictRegex : this._weekdaysShortRegex;\n    }\n  }\n  function weekdaysMinRegex(isStrict) {\n    if (this._weekdaysParseExact) {\n      if (!hasOwnProp(this, '_weekdaysRegex')) {\n        computeWeekdaysParse.call(this);\n      }\n      if (isStrict) {\n        return this._weekdaysMinStrictRegex;\n      } else {\n        return this._weekdaysMinRegex;\n      }\n    } else {\n      if (!hasOwnProp(this, '_weekdaysMinRegex')) {\n        this._weekdaysMinRegex = defaultWeekdaysMinRegex;\n      }\n      return this._weekdaysMinStrictRegex && isStrict ? this._weekdaysMinStrictRegex : this._weekdaysMinRegex;\n    }\n  }\n  function computeWeekdaysParse() {\n    function cmpLenRev(a, b) {\n      return b.length - a.length;\n    }\n    var minPieces = [],\n      shortPieces = [],\n      longPieces = [],\n      mixedPieces = [],\n      i,\n      mom,\n      minp,\n      shortp,\n      longp;\n    for (i = 0; i < 7; i++) {\n      // make the regex if we don't have it already\n      mom = createUTC([2000, 1]).day(i);\n      minp = regexEscape(this.weekdaysMin(mom, ''));\n      shortp = regexEscape(this.weekdaysShort(mom, ''));\n      longp = regexEscape(this.weekdays(mom, ''));\n      minPieces.push(minp);\n      shortPieces.push(shortp);\n      longPieces.push(longp);\n      mixedPieces.push(minp);\n      mixedPieces.push(shortp);\n      mixedPieces.push(longp);\n    }\n    // Sorting makes sure if one weekday (or abbr) is a prefix of another it\n    // will match the longer piece.\n    minPieces.sort(cmpLenRev);\n    shortPieces.sort(cmpLenRev);\n    longPieces.sort(cmpLenRev);\n    mixedPieces.sort(cmpLenRev);\n    this._weekdaysRegex = new RegExp('^(' + mixedPieces.join('|') + ')', 'i');\n    this._weekdaysShortRegex = this._weekdaysRegex;\n    this._weekdaysMinRegex = this._weekdaysRegex;\n    this._weekdaysStrictRegex = new RegExp('^(' + longPieces.join('|') + ')', 'i');\n    this._weekdaysShortStrictRegex = new RegExp('^(' + shortPieces.join('|') + ')', 'i');\n    this._weekdaysMinStrictRegex = new RegExp('^(' + minPieces.join('|') + ')', 'i');\n  }\n\n  // FORMATTING\n\n  function hFormat() {\n    return this.hours() % 12 || 12;\n  }\n  function kFormat() {\n    return this.hours() || 24;\n  }\n  addFormatToken('H', ['HH', 2], 0, 'hour');\n  addFormatToken('h', ['hh', 2], 0, hFormat);\n  addFormatToken('k', ['kk', 2], 0, kFormat);\n  addFormatToken('hmm', 0, 0, function () {\n    return '' + hFormat.apply(this) + zeroFill(this.minutes(), 2);\n  });\n  addFormatToken('hmmss', 0, 0, function () {\n    return '' + hFormat.apply(this) + zeroFill(this.minutes(), 2) + zeroFill(this.seconds(), 2);\n  });\n  addFormatToken('Hmm', 0, 0, function () {\n    return '' + this.hours() + zeroFill(this.minutes(), 2);\n  });\n  addFormatToken('Hmmss', 0, 0, function () {\n    return '' + this.hours() + zeroFill(this.minutes(), 2) + zeroFill(this.seconds(), 2);\n  });\n  function meridiem(token, lowercase) {\n    addFormatToken(token, 0, 0, function () {\n      return this.localeData().meridiem(this.hours(), this.minutes(), lowercase);\n    });\n  }\n  meridiem('a', true);\n  meridiem('A', false);\n\n  // ALIASES\n\n  addUnitAlias('hour', 'h');\n\n  // PRIORITY\n  addUnitPriority('hour', 13);\n\n  // PARSING\n\n  function matchMeridiem(isStrict, locale) {\n    return locale._meridiemParse;\n  }\n  addRegexToken('a', matchMeridiem);\n  addRegexToken('A', matchMeridiem);\n  addRegexToken('H', match1to2);\n  addRegexToken('h', match1to2);\n  addRegexToken('k', match1to2);\n  addRegexToken('HH', match1to2, match2);\n  addRegexToken('hh', match1to2, match2);\n  addRegexToken('kk', match1to2, match2);\n  addRegexToken('hmm', match3to4);\n  addRegexToken('hmmss', match5to6);\n  addRegexToken('Hmm', match3to4);\n  addRegexToken('Hmmss', match5to6);\n  addParseToken(['H', 'HH'], HOUR);\n  addParseToken(['k', 'kk'], function (input, array, config) {\n    var kInput = toInt(input);\n    array[HOUR] = kInput === 24 ? 0 : kInput;\n  });\n  addParseToken(['a', 'A'], function (input, array, config) {\n    config._isPm = config._locale.isPM(input);\n    config._meridiem = input;\n  });\n  addParseToken(['h', 'hh'], function (input, array, config) {\n    array[HOUR] = toInt(input);\n    getParsingFlags(config).bigHour = true;\n  });\n  addParseToken('hmm', function (input, array, config) {\n    var pos = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos));\n    array[MINUTE] = toInt(input.substr(pos));\n    getParsingFlags(config).bigHour = true;\n  });\n  addParseToken('hmmss', function (input, array, config) {\n    var pos1 = input.length - 4,\n      pos2 = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos1));\n    array[MINUTE] = toInt(input.substr(pos1, 2));\n    array[SECOND] = toInt(input.substr(pos2));\n    getParsingFlags(config).bigHour = true;\n  });\n  addParseToken('Hmm', function (input, array, config) {\n    var pos = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos));\n    array[MINUTE] = toInt(input.substr(pos));\n  });\n  addParseToken('Hmmss', function (input, array, config) {\n    var pos1 = input.length - 4,\n      pos2 = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos1));\n    array[MINUTE] = toInt(input.substr(pos1, 2));\n    array[SECOND] = toInt(input.substr(pos2));\n  });\n\n  // LOCALES\n\n  function localeIsPM(input) {\n    // IE8 Quirks Mode & IE7 Standards Mode do not allow accessing strings like arrays\n    // Using charAt should be more compatible.\n    return (input + '').toLowerCase().charAt(0) === 'p';\n  }\n  var defaultLocaleMeridiemParse = /[ap]\\.?m?\\.?/i,\n    // Setting the hour should keep the time, because the user explicitly\n    // specified which hour they want. So trying to maintain the same hour (in\n    // a new timezone) makes sense. Adding/subtracting hours does not follow\n    // this rule.\n    getSetHour = makeGetSet('Hours', true);\n  function localeMeridiem(hours, minutes, isLower) {\n    if (hours > 11) {\n      return isLower ? 'pm' : 'PM';\n    } else {\n      return isLower ? 'am' : 'AM';\n    }\n  }\n  var baseConfig = {\n    calendar: defaultCalendar,\n    longDateFormat: defaultLongDateFormat,\n    invalidDate: defaultInvalidDate,\n    ordinal: defaultOrdinal,\n    dayOfMonthOrdinalParse: defaultDayOfMonthOrdinalParse,\n    relativeTime: defaultRelativeTime,\n    months: defaultLocaleMonths,\n    monthsShort: defaultLocaleMonthsShort,\n    week: defaultLocaleWeek,\n    weekdays: defaultLocaleWeekdays,\n    weekdaysMin: defaultLocaleWeekdaysMin,\n    weekdaysShort: defaultLocaleWeekdaysShort,\n    meridiemParse: defaultLocaleMeridiemParse\n  };\n\n  // internal storage for locale config files\n  var locales = {},\n    localeFamilies = {},\n    globalLocale;\n  function commonPrefix(arr1, arr2) {\n    var i,\n      minl = Math.min(arr1.length, arr2.length);\n    for (i = 0; i < minl; i += 1) {\n      if (arr1[i] !== arr2[i]) {\n        return i;\n      }\n    }\n    return minl;\n  }\n  function normalizeLocale(key) {\n    return key ? key.toLowerCase().replace('_', '-') : key;\n  }\n\n  // pick the locale from the array\n  // try ['en-au', 'en-gb'] as 'en-au', 'en-gb', 'en', as in move through the list trying each\n  // substring from most specific to least, but move to the next array item if it's a more specific variant than the current root\n  function chooseLocale(names) {\n    var i = 0,\n      j,\n      next,\n      locale,\n      split;\n    while (i < names.length) {\n      split = normalizeLocale(names[i]).split('-');\n      j = split.length;\n      next = normalizeLocale(names[i + 1]);\n      next = next ? next.split('-') : null;\n      while (j > 0) {\n        locale = loadLocale(split.slice(0, j).join('-'));\n        if (locale) {\n          return locale;\n        }\n        if (next && next.length >= j && commonPrefix(split, next) >= j - 1) {\n          //the next array item is better than a shallower substring of this one\n          break;\n        }\n        j--;\n      }\n      i++;\n    }\n    return globalLocale;\n  }\n  function loadLocale(name) {\n    var oldLocale = null,\n      aliasedRequire;\n    // TODO: Find a better way to register and load all the locales in Node\n    if (locales[name] === undefined && typeof module !== 'undefined' && module && module.exports) {\n      try {\n        oldLocale = globalLocale._abbr;\n        aliasedRequire = require;\n        aliasedRequire('./locale/' + name);\n        getSetGlobalLocale(oldLocale);\n      } catch (e) {\n        // mark as not found to avoid repeating expensive file require call causing high CPU\n        // when trying to find en-US, en_US, en-us for every format call\n        locales[name] = null; // null means not found\n      }\n    }\n    return locales[name];\n  }\n\n  // This function will load locale and then set the global locale.  If\n  // no arguments are passed in, it will simply return the current global\n  // locale key.\n  function getSetGlobalLocale(key, values) {\n    var data;\n    if (key) {\n      if (isUndefined(values)) {\n        data = getLocale(key);\n      } else {\n        data = defineLocale(key, values);\n      }\n      if (data) {\n        // moment.duration._locale = moment._locale = data;\n        globalLocale = data;\n      } else {\n        if (typeof console !== 'undefined' && console.warn) {\n          //warn user if arguments are passed but the locale could not be set\n          console.warn('Locale ' + key + ' not found. Did you forget to load it?');\n        }\n      }\n    }\n    return globalLocale._abbr;\n  }\n  function defineLocale(name, config) {\n    if (config !== null) {\n      var locale,\n        parentConfig = baseConfig;\n      config.abbr = name;\n      if (locales[name] != null) {\n        deprecateSimple('defineLocaleOverride', 'use moment.updateLocale(localeName, config) to change ' + 'an existing locale. moment.defineLocale(localeName, ' + 'config) should only be used for creating a new locale ' + 'See http://momentjs.com/guides/#/warnings/define-locale/ for more info.');\n        parentConfig = locales[name]._config;\n      } else if (config.parentLocale != null) {\n        if (locales[config.parentLocale] != null) {\n          parentConfig = locales[config.parentLocale]._config;\n        } else {\n          locale = loadLocale(config.parentLocale);\n          if (locale != null) {\n            parentConfig = locale._config;\n          } else {\n            if (!localeFamilies[config.parentLocale]) {\n              localeFamilies[config.parentLocale] = [];\n            }\n            localeFamilies[config.parentLocale].push({\n              name: name,\n              config: config\n            });\n            return null;\n          }\n        }\n      }\n      locales[name] = new Locale(mergeConfigs(parentConfig, config));\n      if (localeFamilies[name]) {\n        localeFamilies[name].forEach(function (x) {\n          defineLocale(x.name, x.config);\n        });\n      }\n\n      // backwards compat for now: also set the locale\n      // make sure we set the locale AFTER all child locales have been\n      // created, so we won't end up with the child locale set.\n      getSetGlobalLocale(name);\n      return locales[name];\n    } else {\n      // useful for testing\n      delete locales[name];\n      return null;\n    }\n  }\n  function updateLocale(name, config) {\n    if (config != null) {\n      var locale,\n        tmpLocale,\n        parentConfig = baseConfig;\n      if (locales[name] != null && locales[name].parentLocale != null) {\n        // Update existing child locale in-place to avoid memory-leaks\n        locales[name].set(mergeConfigs(locales[name]._config, config));\n      } else {\n        // MERGE\n        tmpLocale = loadLocale(name);\n        if (tmpLocale != null) {\n          parentConfig = tmpLocale._config;\n        }\n        config = mergeConfigs(parentConfig, config);\n        if (tmpLocale == null) {\n          // updateLocale is called for creating a new locale\n          // Set abbr so it will have a name (getters return\n          // undefined otherwise).\n          config.abbr = name;\n        }\n        locale = new Locale(config);\n        locale.parentLocale = locales[name];\n        locales[name] = locale;\n      }\n\n      // backwards compat for now: also set the locale\n      getSetGlobalLocale(name);\n    } else {\n      // pass null for config to unupdate, useful for tests\n      if (locales[name] != null) {\n        if (locales[name].parentLocale != null) {\n          locales[name] = locales[name].parentLocale;\n          if (name === getSetGlobalLocale()) {\n            getSetGlobalLocale(name);\n          }\n        } else if (locales[name] != null) {\n          delete locales[name];\n        }\n      }\n    }\n    return locales[name];\n  }\n\n  // returns locale data\n  function getLocale(key) {\n    var locale;\n    if (key && key._locale && key._locale._abbr) {\n      key = key._locale._abbr;\n    }\n    if (!key) {\n      return globalLocale;\n    }\n    if (!isArray(key)) {\n      //short-circuit everything else\n      locale = loadLocale(key);\n      if (locale) {\n        return locale;\n      }\n      key = [key];\n    }\n    return chooseLocale(key);\n  }\n  function listLocales() {\n    return keys(locales);\n  }\n  function checkOverflow(m) {\n    var overflow,\n      a = m._a;\n    if (a && getParsingFlags(m).overflow === -2) {\n      overflow = a[MONTH] < 0 || a[MONTH] > 11 ? MONTH : a[DATE] < 1 || a[DATE] > daysInMonth(a[YEAR], a[MONTH]) ? DATE : a[HOUR] < 0 || a[HOUR] > 24 || a[HOUR] === 24 && (a[MINUTE] !== 0 || a[SECOND] !== 0 || a[MILLISECOND] !== 0) ? HOUR : a[MINUTE] < 0 || a[MINUTE] > 59 ? MINUTE : a[SECOND] < 0 || a[SECOND] > 59 ? SECOND : a[MILLISECOND] < 0 || a[MILLISECOND] > 999 ? MILLISECOND : -1;\n      if (getParsingFlags(m)._overflowDayOfYear && (overflow < YEAR || overflow > DATE)) {\n        overflow = DATE;\n      }\n      if (getParsingFlags(m)._overflowWeeks && overflow === -1) {\n        overflow = WEEK;\n      }\n      if (getParsingFlags(m)._overflowWeekday && overflow === -1) {\n        overflow = WEEKDAY;\n      }\n      getParsingFlags(m).overflow = overflow;\n    }\n    return m;\n  }\n\n  // iso 8601 regex\n  // 0000-00-00 0000-W00 or 0000-W00-0 + T + 00 or 00:00 or 00:00:00 or 00:00:00.000 + +00:00 or +0000 or +00)\n  var extendedIsoRegex = /^\\s*((?:[+-]\\d{6}|\\d{4})-(?:\\d\\d-\\d\\d|W\\d\\d-\\d|W\\d\\d|\\d\\d\\d|\\d\\d))(?:(T| )(\\d\\d(?::\\d\\d(?::\\d\\d(?:[.,]\\d+)?)?)?)([+-]\\d\\d(?::?\\d\\d)?|\\s*Z)?)?$/,\n    basicIsoRegex = /^\\s*((?:[+-]\\d{6}|\\d{4})(?:\\d\\d\\d\\d|W\\d\\d\\d|W\\d\\d|\\d\\d\\d|\\d\\d|))(?:(T| )(\\d\\d(?:\\d\\d(?:\\d\\d(?:[.,]\\d+)?)?)?)([+-]\\d\\d(?::?\\d\\d)?|\\s*Z)?)?$/,\n    tzRegex = /Z|[+-]\\d\\d(?::?\\d\\d)?/,\n    isoDates = [['YYYYYY-MM-DD', /[+-]\\d{6}-\\d\\d-\\d\\d/], ['YYYY-MM-DD', /\\d{4}-\\d\\d-\\d\\d/], ['GGGG-[W]WW-E', /\\d{4}-W\\d\\d-\\d/], ['GGGG-[W]WW', /\\d{4}-W\\d\\d/, false], ['YYYY-DDD', /\\d{4}-\\d{3}/], ['YYYY-MM', /\\d{4}-\\d\\d/, false], ['YYYYYYMMDD', /[+-]\\d{10}/], ['YYYYMMDD', /\\d{8}/], ['GGGG[W]WWE', /\\d{4}W\\d{3}/], ['GGGG[W]WW', /\\d{4}W\\d{2}/, false], ['YYYYDDD', /\\d{7}/], ['YYYYMM', /\\d{6}/, false], ['YYYY', /\\d{4}/, false]],\n    // iso time formats and regexes\n    isoTimes = [['HH:mm:ss.SSSS', /\\d\\d:\\d\\d:\\d\\d\\.\\d+/], ['HH:mm:ss,SSSS', /\\d\\d:\\d\\d:\\d\\d,\\d+/], ['HH:mm:ss', /\\d\\d:\\d\\d:\\d\\d/], ['HH:mm', /\\d\\d:\\d\\d/], ['HHmmss.SSSS', /\\d\\d\\d\\d\\d\\d\\.\\d+/], ['HHmmss,SSSS', /\\d\\d\\d\\d\\d\\d,\\d+/], ['HHmmss', /\\d\\d\\d\\d\\d\\d/], ['HHmm', /\\d\\d\\d\\d/], ['HH', /\\d\\d/]],\n    aspNetJsonRegex = /^\\/?Date\\((-?\\d+)/i,\n    // RFC 2822 regex: For details see https://tools.ietf.org/html/rfc2822#section-3.3\n    rfc2822 = /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\\s)?(\\d{1,2})\\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s(\\d{2,4})\\s(\\d\\d):(\\d\\d)(?::(\\d\\d))?\\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\\d{4}))$/,\n    obsOffsets = {\n      UT: 0,\n      GMT: 0,\n      EDT: -4 * 60,\n      EST: -5 * 60,\n      CDT: -5 * 60,\n      CST: -6 * 60,\n      MDT: -6 * 60,\n      MST: -7 * 60,\n      PDT: -7 * 60,\n      PST: -8 * 60\n    };\n\n  // date from iso format\n  function configFromISO(config) {\n    var i,\n      l,\n      string = config._i,\n      match = extendedIsoRegex.exec(string) || basicIsoRegex.exec(string),\n      allowTime,\n      dateFormat,\n      timeFormat,\n      tzFormat;\n    if (match) {\n      getParsingFlags(config).iso = true;\n      for (i = 0, l = isoDates.length; i < l; i++) {\n        if (isoDates[i][1].exec(match[1])) {\n          dateFormat = isoDates[i][0];\n          allowTime = isoDates[i][2] !== false;\n          break;\n        }\n      }\n      if (dateFormat == null) {\n        config._isValid = false;\n        return;\n      }\n      if (match[3]) {\n        for (i = 0, l = isoTimes.length; i < l; i++) {\n          if (isoTimes[i][1].exec(match[3])) {\n            // match[2] should be 'T' or space\n            timeFormat = (match[2] || ' ') + isoTimes[i][0];\n            break;\n          }\n        }\n        if (timeFormat == null) {\n          config._isValid = false;\n          return;\n        }\n      }\n      if (!allowTime && timeFormat != null) {\n        config._isValid = false;\n        return;\n      }\n      if (match[4]) {\n        if (tzRegex.exec(match[4])) {\n          tzFormat = 'Z';\n        } else {\n          config._isValid = false;\n          return;\n        }\n      }\n      config._f = dateFormat + (timeFormat || '') + (tzFormat || '');\n      configFromStringAndFormat(config);\n    } else {\n      config._isValid = false;\n    }\n  }\n  function extractFromRFC2822Strings(yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr) {\n    var result = [untruncateYear(yearStr), defaultLocaleMonthsShort.indexOf(monthStr), parseInt(dayStr, 10), parseInt(hourStr, 10), parseInt(minuteStr, 10)];\n    if (secondStr) {\n      result.push(parseInt(secondStr, 10));\n    }\n    return result;\n  }\n  function untruncateYear(yearStr) {\n    var year = parseInt(yearStr, 10);\n    if (year <= 49) {\n      return 2000 + year;\n    } else if (year <= 999) {\n      return 1900 + year;\n    }\n    return year;\n  }\n  function preprocessRFC2822(s) {\n    // Remove comments and folding whitespace and replace multiple-spaces with a single space\n    return s.replace(/\\([^)]*\\)|[\\n\\t]/g, ' ').replace(/(\\s\\s+)/g, ' ').replace(/^\\s\\s*/, '').replace(/\\s\\s*$/, '');\n  }\n  function checkWeekday(weekdayStr, parsedInput, config) {\n    if (weekdayStr) {\n      // TODO: Replace the vanilla JS Date object with an independent day-of-week check.\n      var weekdayProvided = defaultLocaleWeekdaysShort.indexOf(weekdayStr),\n        weekdayActual = new Date(parsedInput[0], parsedInput[1], parsedInput[2]).getDay();\n      if (weekdayProvided !== weekdayActual) {\n        getParsingFlags(config).weekdayMismatch = true;\n        config._isValid = false;\n        return false;\n      }\n    }\n    return true;\n  }\n  function calculateOffset(obsOffset, militaryOffset, numOffset) {\n    if (obsOffset) {\n      return obsOffsets[obsOffset];\n    } else if (militaryOffset) {\n      // the only allowed military tz is Z\n      return 0;\n    } else {\n      var hm = parseInt(numOffset, 10),\n        m = hm % 100,\n        h = (hm - m) / 100;\n      return h * 60 + m;\n    }\n  }\n\n  // date and time from ref 2822 format\n  function configFromRFC2822(config) {\n    var match = rfc2822.exec(preprocessRFC2822(config._i)),\n      parsedArray;\n    if (match) {\n      parsedArray = extractFromRFC2822Strings(match[4], match[3], match[2], match[5], match[6], match[7]);\n      if (!checkWeekday(match[1], parsedArray, config)) {\n        return;\n      }\n      config._a = parsedArray;\n      config._tzm = calculateOffset(match[8], match[9], match[10]);\n      config._d = createUTCDate.apply(null, config._a);\n      config._d.setUTCMinutes(config._d.getUTCMinutes() - config._tzm);\n      getParsingFlags(config).rfc2822 = true;\n    } else {\n      config._isValid = false;\n    }\n  }\n\n  // date from 1) ASP.NET, 2) ISO, 3) RFC 2822 formats, or 4) optional fallback if parsing isn't strict\n  function configFromString(config) {\n    var matched = aspNetJsonRegex.exec(config._i);\n    if (matched !== null) {\n      config._d = new Date(+matched[1]);\n      return;\n    }\n    configFromISO(config);\n    if (config._isValid === false) {\n      delete config._isValid;\n    } else {\n      return;\n    }\n    configFromRFC2822(config);\n    if (config._isValid === false) {\n      delete config._isValid;\n    } else {\n      return;\n    }\n    if (config._strict) {\n      config._isValid = false;\n    } else {\n      // Final attempt, use Input Fallback\n      hooks.createFromInputFallback(config);\n    }\n  }\n  hooks.createFromInputFallback = deprecate('value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), ' + 'which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are ' + 'discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.', function (config) {\n    config._d = new Date(config._i + (config._useUTC ? ' UTC' : ''));\n  });\n\n  // Pick the first defined of two or three arguments.\n  function defaults(a, b, c) {\n    if (a != null) {\n      return a;\n    }\n    if (b != null) {\n      return b;\n    }\n    return c;\n  }\n  function currentDateArray(config) {\n    // hooks is actually the exported moment object\n    var nowValue = new Date(hooks.now());\n    if (config._useUTC) {\n      return [nowValue.getUTCFullYear(), nowValue.getUTCMonth(), nowValue.getUTCDate()];\n    }\n    return [nowValue.getFullYear(), nowValue.getMonth(), nowValue.getDate()];\n  }\n\n  // convert an array to a date.\n  // the array should mirror the parameters below\n  // note: all values past the year are optional and will default to the lowest possible value.\n  // [year, month, day , hour, minute, second, millisecond]\n  function configFromArray(config) {\n    var i,\n      date,\n      input = [],\n      currentDate,\n      expectedWeekday,\n      yearToUse;\n    if (config._d) {\n      return;\n    }\n    currentDate = currentDateArray(config);\n\n    //compute day of the year from weeks and weekdays\n    if (config._w && config._a[DATE] == null && config._a[MONTH] == null) {\n      dayOfYearFromWeekInfo(config);\n    }\n\n    //if the day of the year is set, figure out what it is\n    if (config._dayOfYear != null) {\n      yearToUse = defaults(config._a[YEAR], currentDate[YEAR]);\n      if (config._dayOfYear > daysInYear(yearToUse) || config._dayOfYear === 0) {\n        getParsingFlags(config)._overflowDayOfYear = true;\n      }\n      date = createUTCDate(yearToUse, 0, config._dayOfYear);\n      config._a[MONTH] = date.getUTCMonth();\n      config._a[DATE] = date.getUTCDate();\n    }\n\n    // Default to current date.\n    // * if no year, month, day of month are given, default to today\n    // * if day of month is given, default month and year\n    // * if month is given, default only year\n    // * if year is given, don't default anything\n    for (i = 0; i < 3 && config._a[i] == null; ++i) {\n      config._a[i] = input[i] = currentDate[i];\n    }\n\n    // Zero out whatever was not defaulted, including time\n    for (; i < 7; i++) {\n      config._a[i] = input[i] = config._a[i] == null ? i === 2 ? 1 : 0 : config._a[i];\n    }\n\n    // Check for 24:00:00.000\n    if (config._a[HOUR] === 24 && config._a[MINUTE] === 0 && config._a[SECOND] === 0 && config._a[MILLISECOND] === 0) {\n      config._nextDay = true;\n      config._a[HOUR] = 0;\n    }\n    config._d = (config._useUTC ? createUTCDate : createDate).apply(null, input);\n    expectedWeekday = config._useUTC ? config._d.getUTCDay() : config._d.getDay();\n\n    // Apply timezone offset from input. The actual utcOffset can be changed\n    // with parseZone.\n    if (config._tzm != null) {\n      config._d.setUTCMinutes(config._d.getUTCMinutes() - config._tzm);\n    }\n    if (config._nextDay) {\n      config._a[HOUR] = 24;\n    }\n\n    // check for mismatching day of week\n    if (config._w && typeof config._w.d !== 'undefined' && config._w.d !== expectedWeekday) {\n      getParsingFlags(config).weekdayMismatch = true;\n    }\n  }\n  function dayOfYearFromWeekInfo(config) {\n    var w, weekYear, week, weekday, dow, doy, temp, weekdayOverflow, curWeek;\n    w = config._w;\n    if (w.GG != null || w.W != null || w.E != null) {\n      dow = 1;\n      doy = 4;\n\n      // TODO: We need to take the current isoWeekYear, but that depends on\n      // how we interpret now (local, utc, fixed offset). So create\n      // a now version of current config (take local/utc/offset flags, and\n      // create now).\n      weekYear = defaults(w.GG, config._a[YEAR], weekOfYear(createLocal(), 1, 4).year);\n      week = defaults(w.W, 1);\n      weekday = defaults(w.E, 1);\n      if (weekday < 1 || weekday > 7) {\n        weekdayOverflow = true;\n      }\n    } else {\n      dow = config._locale._week.dow;\n      doy = config._locale._week.doy;\n      curWeek = weekOfYear(createLocal(), dow, doy);\n      weekYear = defaults(w.gg, config._a[YEAR], curWeek.year);\n\n      // Default to current week.\n      week = defaults(w.w, curWeek.week);\n      if (w.d != null) {\n        // weekday -- low day numbers are considered next week\n        weekday = w.d;\n        if (weekday < 0 || weekday > 6) {\n          weekdayOverflow = true;\n        }\n      } else if (w.e != null) {\n        // local weekday -- counting starts from beginning of week\n        weekday = w.e + dow;\n        if (w.e < 0 || w.e > 6) {\n          weekdayOverflow = true;\n        }\n      } else {\n        // default to beginning of week\n        weekday = dow;\n      }\n    }\n    if (week < 1 || week > weeksInYear(weekYear, dow, doy)) {\n      getParsingFlags(config)._overflowWeeks = true;\n    } else if (weekdayOverflow != null) {\n      getParsingFlags(config)._overflowWeekday = true;\n    } else {\n      temp = dayOfYearFromWeeks(weekYear, week, weekday, dow, doy);\n      config._a[YEAR] = temp.year;\n      config._dayOfYear = temp.dayOfYear;\n    }\n  }\n\n  // constant that refers to the ISO standard\n  hooks.ISO_8601 = function () {};\n\n  // constant that refers to the RFC 2822 form\n  hooks.RFC_2822 = function () {};\n\n  // date from string and format string\n  function configFromStringAndFormat(config) {\n    // TODO: Move this to another part of the creation flow to prevent circular deps\n    if (config._f === hooks.ISO_8601) {\n      configFromISO(config);\n      return;\n    }\n    if (config._f === hooks.RFC_2822) {\n      configFromRFC2822(config);\n      return;\n    }\n    config._a = [];\n    getParsingFlags(config).empty = true;\n\n    // This array is used to make a Date, either with `new Date` or `Date.UTC`\n    var string = '' + config._i,\n      i,\n      parsedInput,\n      tokens,\n      token,\n      skipped,\n      stringLength = string.length,\n      totalParsedInputLength = 0,\n      era;\n    tokens = expandFormat(config._f, config._locale).match(formattingTokens) || [];\n    for (i = 0; i < tokens.length; i++) {\n      token = tokens[i];\n      parsedInput = (string.match(getParseRegexForToken(token, config)) || [])[0];\n      if (parsedInput) {\n        skipped = string.substr(0, string.indexOf(parsedInput));\n        if (skipped.length > 0) {\n          getParsingFlags(config).unusedInput.push(skipped);\n        }\n        string = string.slice(string.indexOf(parsedInput) + parsedInput.length);\n        totalParsedInputLength += parsedInput.length;\n      }\n      // don't parse if it's not a known token\n      if (formatTokenFunctions[token]) {\n        if (parsedInput) {\n          getParsingFlags(config).empty = false;\n        } else {\n          getParsingFlags(config).unusedTokens.push(token);\n        }\n        addTimeToArrayFromToken(token, parsedInput, config);\n      } else if (config._strict && !parsedInput) {\n        getParsingFlags(config).unusedTokens.push(token);\n      }\n    }\n\n    // add remaining unparsed input length to the string\n    getParsingFlags(config).charsLeftOver = stringLength - totalParsedInputLength;\n    if (string.length > 0) {\n      getParsingFlags(config).unusedInput.push(string);\n    }\n\n    // clear _12h flag if hour is <= 12\n    if (config._a[HOUR] <= 12 && getParsingFlags(config).bigHour === true && config._a[HOUR] > 0) {\n      getParsingFlags(config).bigHour = undefined;\n    }\n    getParsingFlags(config).parsedDateParts = config._a.slice(0);\n    getParsingFlags(config).meridiem = config._meridiem;\n    // handle meridiem\n    config._a[HOUR] = meridiemFixWrap(config._locale, config._a[HOUR], config._meridiem);\n\n    // handle era\n    era = getParsingFlags(config).era;\n    if (era !== null) {\n      config._a[YEAR] = config._locale.erasConvertYear(era, config._a[YEAR]);\n    }\n    configFromArray(config);\n    checkOverflow(config);\n  }\n  function meridiemFixWrap(locale, hour, meridiem) {\n    var isPm;\n    if (meridiem == null) {\n      // nothing to do\n      return hour;\n    }\n    if (locale.meridiemHour != null) {\n      return locale.meridiemHour(hour, meridiem);\n    } else if (locale.isPM != null) {\n      // Fallback\n      isPm = locale.isPM(meridiem);\n      if (isPm && hour < 12) {\n        hour += 12;\n      }\n      if (!isPm && hour === 12) {\n        hour = 0;\n      }\n      return hour;\n    } else {\n      // this is not supposed to happen\n      return hour;\n    }\n  }\n\n  // date from string and array of format strings\n  function configFromStringAndArray(config) {\n    var tempConfig,\n      bestMoment,\n      scoreToBeat,\n      i,\n      currentScore,\n      validFormatFound,\n      bestFormatIsValid = false;\n    if (config._f.length === 0) {\n      getParsingFlags(config).invalidFormat = true;\n      config._d = new Date(NaN);\n      return;\n    }\n    for (i = 0; i < config._f.length; i++) {\n      currentScore = 0;\n      validFormatFound = false;\n      tempConfig = copyConfig({}, config);\n      if (config._useUTC != null) {\n        tempConfig._useUTC = config._useUTC;\n      }\n      tempConfig._f = config._f[i];\n      configFromStringAndFormat(tempConfig);\n      if (isValid(tempConfig)) {\n        validFormatFound = true;\n      }\n\n      // if there is any input that was not parsed add a penalty for that format\n      currentScore += getParsingFlags(tempConfig).charsLeftOver;\n\n      //or tokens\n      currentScore += getParsingFlags(tempConfig).unusedTokens.length * 10;\n      getParsingFlags(tempConfig).score = currentScore;\n      if (!bestFormatIsValid) {\n        if (scoreToBeat == null || currentScore < scoreToBeat || validFormatFound) {\n          scoreToBeat = currentScore;\n          bestMoment = tempConfig;\n          if (validFormatFound) {\n            bestFormatIsValid = true;\n          }\n        }\n      } else {\n        if (currentScore < scoreToBeat) {\n          scoreToBeat = currentScore;\n          bestMoment = tempConfig;\n        }\n      }\n    }\n    extend(config, bestMoment || tempConfig);\n  }\n  function configFromObject(config) {\n    if (config._d) {\n      return;\n    }\n    var i = normalizeObjectUnits(config._i),\n      dayOrDate = i.day === undefined ? i.date : i.day;\n    config._a = map([i.year, i.month, dayOrDate, i.hour, i.minute, i.second, i.millisecond], function (obj) {\n      return obj && parseInt(obj, 10);\n    });\n    configFromArray(config);\n  }\n  function createFromConfig(config) {\n    var res = new Moment(checkOverflow(prepareConfig(config)));\n    if (res._nextDay) {\n      // Adding is smart enough around DST\n      res.add(1, 'd');\n      res._nextDay = undefined;\n    }\n    return res;\n  }\n  function prepareConfig(config) {\n    var input = config._i,\n      format = config._f;\n    config._locale = config._locale || getLocale(config._l);\n    if (input === null || format === undefined && input === '') {\n      return createInvalid({\n        nullInput: true\n      });\n    }\n    if (typeof input === 'string') {\n      config._i = input = config._locale.preparse(input);\n    }\n    if (isMoment(input)) {\n      return new Moment(checkOverflow(input));\n    } else if (isDate(input)) {\n      config._d = input;\n    } else if (isArray(format)) {\n      configFromStringAndArray(config);\n    } else if (format) {\n      configFromStringAndFormat(config);\n    } else {\n      configFromInput(config);\n    }\n    if (!isValid(config)) {\n      config._d = null;\n    }\n    return config;\n  }\n  function configFromInput(config) {\n    var input = config._i;\n    if (isUndefined(input)) {\n      config._d = new Date(hooks.now());\n    } else if (isDate(input)) {\n      config._d = new Date(input.valueOf());\n    } else if (typeof input === 'string') {\n      configFromString(config);\n    } else if (isArray(input)) {\n      config._a = map(input.slice(0), function (obj) {\n        return parseInt(obj, 10);\n      });\n      configFromArray(config);\n    } else if (isObject(input)) {\n      configFromObject(config);\n    } else if (isNumber(input)) {\n      // from milliseconds\n      config._d = new Date(input);\n    } else {\n      hooks.createFromInputFallback(config);\n    }\n  }\n  function createLocalOrUTC(input, format, locale, strict, isUTC) {\n    var c = {};\n    if (format === true || format === false) {\n      strict = format;\n      format = undefined;\n    }\n    if (locale === true || locale === false) {\n      strict = locale;\n      locale = undefined;\n    }\n    if (isObject(input) && isObjectEmpty(input) || isArray(input) && input.length === 0) {\n      input = undefined;\n    }\n    // object construction must be done this way.\n    // https://github.com/moment/moment/issues/1423\n    c._isAMomentObject = true;\n    c._useUTC = c._isUTC = isUTC;\n    c._l = locale;\n    c._i = input;\n    c._f = format;\n    c._strict = strict;\n    return createFromConfig(c);\n  }\n  function createLocal(input, format, locale, strict) {\n    return createLocalOrUTC(input, format, locale, strict, false);\n  }\n  var prototypeMin = deprecate('moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/', function () {\n      var other = createLocal.apply(null, arguments);\n      if (this.isValid() && other.isValid()) {\n        return other < this ? this : other;\n      } else {\n        return createInvalid();\n      }\n    }),\n    prototypeMax = deprecate('moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/', function () {\n      var other = createLocal.apply(null, arguments);\n      if (this.isValid() && other.isValid()) {\n        return other > this ? this : other;\n      } else {\n        return createInvalid();\n      }\n    });\n\n  // Pick a moment m from moments so that m[fn](other) is true for all\n  // other. This relies on the function fn to be transitive.\n  //\n  // moments should either be an array of moment objects or an array, whose\n  // first element is an array of moment objects.\n  function pickBy(fn, moments) {\n    var res, i;\n    if (moments.length === 1 && isArray(moments[0])) {\n      moments = moments[0];\n    }\n    if (!moments.length) {\n      return createLocal();\n    }\n    res = moments[0];\n    for (i = 1; i < moments.length; ++i) {\n      if (!moments[i].isValid() || moments[i][fn](res)) {\n        res = moments[i];\n      }\n    }\n    return res;\n  }\n\n  // TODO: Use [].sort instead?\n  function min() {\n    var args = [].slice.call(arguments, 0);\n    return pickBy('isBefore', args);\n  }\n  function max() {\n    var args = [].slice.call(arguments, 0);\n    return pickBy('isAfter', args);\n  }\n  var now = function () {\n    return Date.now ? Date.now() : +new Date();\n  };\n  var ordering = ['year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', 'millisecond'];\n  function isDurationValid(m) {\n    var key,\n      unitHasDecimal = false,\n      i;\n    for (key in m) {\n      if (hasOwnProp(m, key) && !(indexOf.call(ordering, key) !== -1 && (m[key] == null || !isNaN(m[key])))) {\n        return false;\n      }\n    }\n    for (i = 0; i < ordering.length; ++i) {\n      if (m[ordering[i]]) {\n        if (unitHasDecimal) {\n          return false; // only allow non-integers for smallest unit\n        }\n        if (parseFloat(m[ordering[i]]) !== toInt(m[ordering[i]])) {\n          unitHasDecimal = true;\n        }\n      }\n    }\n    return true;\n  }\n  function isValid$1() {\n    return this._isValid;\n  }\n  function createInvalid$1() {\n    return createDuration(NaN);\n  }\n  function Duration(duration) {\n    var normalizedInput = normalizeObjectUnits(duration),\n      years = normalizedInput.year || 0,\n      quarters = normalizedInput.quarter || 0,\n      months = normalizedInput.month || 0,\n      weeks = normalizedInput.week || normalizedInput.isoWeek || 0,\n      days = normalizedInput.day || 0,\n      hours = normalizedInput.hour || 0,\n      minutes = normalizedInput.minute || 0,\n      seconds = normalizedInput.second || 0,\n      milliseconds = normalizedInput.millisecond || 0;\n    this._isValid = isDurationValid(normalizedInput);\n\n    // representation for dateAddRemove\n    this._milliseconds = +milliseconds + seconds * 1e3 +\n    // 1000\n    minutes * 6e4 +\n    // 1000 * 60\n    hours * 1000 * 60 * 60; //using 1000 * 60 * 60 instead of 36e5 to avoid floating point rounding errors https://github.com/moment/moment/issues/2978\n    // Because of dateAddRemove treats 24 hours as different from a\n    // day when working around DST, we need to store them separately\n    this._days = +days + weeks * 7;\n    // It is impossible to translate months into days without knowing\n    // which months you are are talking about, so we have to store\n    // it separately.\n    this._months = +months + quarters * 3 + years * 12;\n    this._data = {};\n    this._locale = getLocale();\n    this._bubble();\n  }\n  function isDuration(obj) {\n    return obj instanceof Duration;\n  }\n  function absRound(number) {\n    if (number < 0) {\n      return Math.round(-1 * number) * -1;\n    } else {\n      return Math.round(number);\n    }\n  }\n\n  // compare two arrays, return the number of differences\n  function compareArrays(array1, array2, dontConvert) {\n    var len = Math.min(array1.length, array2.length),\n      lengthDiff = Math.abs(array1.length - array2.length),\n      diffs = 0,\n      i;\n    for (i = 0; i < len; i++) {\n      if (dontConvert && array1[i] !== array2[i] || !dontConvert && toInt(array1[i]) !== toInt(array2[i])) {\n        diffs++;\n      }\n    }\n    return diffs + lengthDiff;\n  }\n\n  // FORMATTING\n\n  function offset(token, separator) {\n    addFormatToken(token, 0, 0, function () {\n      var offset = this.utcOffset(),\n        sign = '+';\n      if (offset < 0) {\n        offset = -offset;\n        sign = '-';\n      }\n      return sign + zeroFill(~~(offset / 60), 2) + separator + zeroFill(~~offset % 60, 2);\n    });\n  }\n  offset('Z', ':');\n  offset('ZZ', '');\n\n  // PARSING\n\n  addRegexToken('Z', matchShortOffset);\n  addRegexToken('ZZ', matchShortOffset);\n  addParseToken(['Z', 'ZZ'], function (input, array, config) {\n    config._useUTC = true;\n    config._tzm = offsetFromString(matchShortOffset, input);\n  });\n\n  // HELPERS\n\n  // timezone chunker\n  // '+10:00' > ['10',  '00']\n  // '-1530'  > ['-15', '30']\n  var chunkOffset = /([\\+\\-]|\\d\\d)/gi;\n  function offsetFromString(matcher, string) {\n    var matches = (string || '').match(matcher),\n      chunk,\n      parts,\n      minutes;\n    if (matches === null) {\n      return null;\n    }\n    chunk = matches[matches.length - 1] || [];\n    parts = (chunk + '').match(chunkOffset) || ['-', 0, 0];\n    minutes = +(parts[1] * 60) + toInt(parts[2]);\n    return minutes === 0 ? 0 : parts[0] === '+' ? minutes : -minutes;\n  }\n\n  // Return a moment from input, that is local/utc/zone equivalent to model.\n  function cloneWithOffset(input, model) {\n    var res, diff;\n    if (model._isUTC) {\n      res = model.clone();\n      diff = (isMoment(input) || isDate(input) ? input.valueOf() : createLocal(input).valueOf()) - res.valueOf();\n      // Use low-level api, because this fn is low-level api.\n      res._d.setTime(res._d.valueOf() + diff);\n      hooks.updateOffset(res, false);\n      return res;\n    } else {\n      return createLocal(input).local();\n    }\n  }\n  function getDateOffset(m) {\n    // On Firefox.24 Date#getTimezoneOffset returns a floating point.\n    // https://github.com/moment/moment/pull/1871\n    return -Math.round(m._d.getTimezoneOffset());\n  }\n\n  // HOOKS\n\n  // This function will be called whenever a moment is mutated.\n  // It is intended to keep the offset in sync with the timezone.\n  hooks.updateOffset = function () {};\n\n  // MOMENTS\n\n  // keepLocalTime = true means only change the timezone, without\n  // affecting the local hour. So 5:31:26 +0300 --[utcOffset(2, true)]-->\n  // 5:31:26 +0200 It is possible that 5:31:26 doesn't exist with offset\n  // +0200, so we adjust the time as needed, to be valid.\n  //\n  // Keeping the time actually adds/subtracts (one hour)\n  // from the actual represented time. That is why we call updateOffset\n  // a second time. In case it wants us to change the offset again\n  // _changeInProgress == true case, then we have to adjust, because\n  // there is no such time in the given timezone.\n  function getSetOffset(input, keepLocalTime, keepMinutes) {\n    var offset = this._offset || 0,\n      localAdjust;\n    if (!this.isValid()) {\n      return input != null ? this : NaN;\n    }\n    if (input != null) {\n      if (typeof input === 'string') {\n        input = offsetFromString(matchShortOffset, input);\n        if (input === null) {\n          return this;\n        }\n      } else if (Math.abs(input) < 16 && !keepMinutes) {\n        input = input * 60;\n      }\n      if (!this._isUTC && keepLocalTime) {\n        localAdjust = getDateOffset(this);\n      }\n      this._offset = input;\n      this._isUTC = true;\n      if (localAdjust != null) {\n        this.add(localAdjust, 'm');\n      }\n      if (offset !== input) {\n        if (!keepLocalTime || this._changeInProgress) {\n          addSubtract(this, createDuration(input - offset, 'm'), 1, false);\n        } else if (!this._changeInProgress) {\n          this._changeInProgress = true;\n          hooks.updateOffset(this, true);\n          this._changeInProgress = null;\n        }\n      }\n      return this;\n    } else {\n      return this._isUTC ? offset : getDateOffset(this);\n    }\n  }\n  function getSetZone(input, keepLocalTime) {\n    if (input != null) {\n      if (typeof input !== 'string') {\n        input = -input;\n      }\n      this.utcOffset(input, keepLocalTime);\n      return this;\n    } else {\n      return -this.utcOffset();\n    }\n  }\n  function setOffsetToUTC(keepLocalTime) {\n    return this.utcOffset(0, keepLocalTime);\n  }\n  function setOffsetToLocal(keepLocalTime) {\n    if (this._isUTC) {\n      this.utcOffset(0, keepLocalTime);\n      this._isUTC = false;\n      if (keepLocalTime) {\n        this.subtract(getDateOffset(this), 'm');\n      }\n    }\n    return this;\n  }\n  function setOffsetToParsedOffset() {\n    if (this._tzm != null) {\n      this.utcOffset(this._tzm, false, true);\n    } else if (typeof this._i === 'string') {\n      var tZone = offsetFromString(matchOffset, this._i);\n      if (tZone != null) {\n        this.utcOffset(tZone);\n      } else {\n        this.utcOffset(0, true);\n      }\n    }\n    return this;\n  }\n  function hasAlignedHourOffset(input) {\n    if (!this.isValid()) {\n      return false;\n    }\n    input = input ? createLocal(input).utcOffset() : 0;\n    return (this.utcOffset() - input) % 60 === 0;\n  }\n  function isDaylightSavingTime() {\n    return this.utcOffset() > this.clone().month(0).utcOffset() || this.utcOffset() > this.clone().month(5).utcOffset();\n  }\n  function isDaylightSavingTimeShifted() {\n    if (!isUndefined(this._isDSTShifted)) {\n      return this._isDSTShifted;\n    }\n    var c = {},\n      other;\n    copyConfig(c, this);\n    c = prepareConfig(c);\n    if (c._a) {\n      other = c._isUTC ? createUTC(c._a) : createLocal(c._a);\n      this._isDSTShifted = this.isValid() && compareArrays(c._a, other.toArray()) > 0;\n    } else {\n      this._isDSTShifted = false;\n    }\n    return this._isDSTShifted;\n  }\n  function isLocal() {\n    return this.isValid() ? !this._isUTC : false;\n  }\n  function isUtcOffset() {\n    return this.isValid() ? this._isUTC : false;\n  }\n  function isUtc() {\n    return this.isValid() ? this._isUTC && this._offset === 0 : false;\n  }\n\n  // ASP.NET json date format regex\n  var aspNetRegex = /^(-|\\+)?(?:(\\d*)[. ])?(\\d+):(\\d+)(?::(\\d+)(\\.\\d*)?)?$/,\n    // from http://docs.closure-library.googlecode.com/git/closure_goog_date_date.js.source.html\n    // somewhat more in line with 4.4.3.2 2004 spec, but allows decimal anywhere\n    // and further modified to allow for strings containing both week and day\n    isoRegex = /^(-|\\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;\n  function createDuration(input, key) {\n    var duration = input,\n      // matching against regexp is expensive, do it on demand\n      match = null,\n      sign,\n      ret,\n      diffRes;\n    if (isDuration(input)) {\n      duration = {\n        ms: input._milliseconds,\n        d: input._days,\n        M: input._months\n      };\n    } else if (isNumber(input) || !isNaN(+input)) {\n      duration = {};\n      if (key) {\n        duration[key] = +input;\n      } else {\n        duration.milliseconds = +input;\n      }\n    } else if (match = aspNetRegex.exec(input)) {\n      sign = match[1] === '-' ? -1 : 1;\n      duration = {\n        y: 0,\n        d: toInt(match[DATE]) * sign,\n        h: toInt(match[HOUR]) * sign,\n        m: toInt(match[MINUTE]) * sign,\n        s: toInt(match[SECOND]) * sign,\n        ms: toInt(absRound(match[MILLISECOND] * 1000)) * sign // the millisecond decimal point is included in the match\n      };\n    } else if (match = isoRegex.exec(input)) {\n      sign = match[1] === '-' ? -1 : 1;\n      duration = {\n        y: parseIso(match[2], sign),\n        M: parseIso(match[3], sign),\n        w: parseIso(match[4], sign),\n        d: parseIso(match[5], sign),\n        h: parseIso(match[6], sign),\n        m: parseIso(match[7], sign),\n        s: parseIso(match[8], sign)\n      };\n    } else if (duration == null) {\n      // checks for null or undefined\n      duration = {};\n    } else if (typeof duration === 'object' && ('from' in duration || 'to' in duration)) {\n      diffRes = momentsDifference(createLocal(duration.from), createLocal(duration.to));\n      duration = {};\n      duration.ms = diffRes.milliseconds;\n      duration.M = diffRes.months;\n    }\n    ret = new Duration(duration);\n    if (isDuration(input) && hasOwnProp(input, '_locale')) {\n      ret._locale = input._locale;\n    }\n    if (isDuration(input) && hasOwnProp(input, '_isValid')) {\n      ret._isValid = input._isValid;\n    }\n    return ret;\n  }\n  createDuration.fn = Duration.prototype;\n  createDuration.invalid = createInvalid$1;\n  function parseIso(inp, sign) {\n    // We'd normally use ~~inp for this, but unfortunately it also\n    // converts floats to ints.\n    // inp may be undefined, so careful calling replace on it.\n    var res = inp && parseFloat(inp.replace(',', '.'));\n    // apply sign while we're at it\n    return (isNaN(res) ? 0 : res) * sign;\n  }\n  function positiveMomentsDifference(base, other) {\n    var res = {};\n    res.months = other.month() - base.month() + (other.year() - base.year()) * 12;\n    if (base.clone().add(res.months, 'M').isAfter(other)) {\n      --res.months;\n    }\n    res.milliseconds = +other - +base.clone().add(res.months, 'M');\n    return res;\n  }\n  function momentsDifference(base, other) {\n    var res;\n    if (!(base.isValid() && other.isValid())) {\n      return {\n        milliseconds: 0,\n        months: 0\n      };\n    }\n    other = cloneWithOffset(other, base);\n    if (base.isBefore(other)) {\n      res = positiveMomentsDifference(base, other);\n    } else {\n      res = positiveMomentsDifference(other, base);\n      res.milliseconds = -res.milliseconds;\n      res.months = -res.months;\n    }\n    return res;\n  }\n\n  // TODO: remove 'name' arg after deprecation is removed\n  function createAdder(direction, name) {\n    return function (val, period) {\n      var dur, tmp;\n      //invert the arguments, but complain about it\n      if (period !== null && !isNaN(+period)) {\n        deprecateSimple(name, 'moment().' + name + '(period, number) is deprecated. Please use moment().' + name + '(number, period). ' + 'See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info.');\n        tmp = val;\n        val = period;\n        period = tmp;\n      }\n      dur = createDuration(val, period);\n      addSubtract(this, dur, direction);\n      return this;\n    };\n  }\n  function addSubtract(mom, duration, isAdding, updateOffset) {\n    var milliseconds = duration._milliseconds,\n      days = absRound(duration._days),\n      months = absRound(duration._months);\n    if (!mom.isValid()) {\n      // No op\n      return;\n    }\n    updateOffset = updateOffset == null ? true : updateOffset;\n    if (months) {\n      setMonth(mom, get(mom, 'Month') + months * isAdding);\n    }\n    if (days) {\n      set$1(mom, 'Date', get(mom, 'Date') + days * isAdding);\n    }\n    if (milliseconds) {\n      mom._d.setTime(mom._d.valueOf() + milliseconds * isAdding);\n    }\n    if (updateOffset) {\n      hooks.updateOffset(mom, days || months);\n    }\n  }\n  var add = createAdder(1, 'add'),\n    subtract = createAdder(-1, 'subtract');\n  function isString(input) {\n    return typeof input === 'string' || input instanceof String;\n  }\n\n  // type MomentInput = Moment | Date | string | number | (number | string)[] | MomentInputObject | void; // null | undefined\n  function isMomentInput(input) {\n    return isMoment(input) || isDate(input) || isString(input) || isNumber(input) || isNumberOrStringArray(input) || isMomentInputObject(input) || input === null || input === undefined;\n  }\n  function isMomentInputObject(input) {\n    var objectTest = isObject(input) && !isObjectEmpty(input),\n      propertyTest = false,\n      properties = ['years', 'year', 'y', 'months', 'month', 'M', 'days', 'day', 'd', 'dates', 'date', 'D', 'hours', 'hour', 'h', 'minutes', 'minute', 'm', 'seconds', 'second', 's', 'milliseconds', 'millisecond', 'ms'],\n      i,\n      property;\n    for (i = 0; i < properties.length; i += 1) {\n      property = properties[i];\n      propertyTest = propertyTest || hasOwnProp(input, property);\n    }\n    return objectTest && propertyTest;\n  }\n  function isNumberOrStringArray(input) {\n    var arrayTest = isArray(input),\n      dataTypeTest = false;\n    if (arrayTest) {\n      dataTypeTest = input.filter(function (item) {\n        return !isNumber(item) && isString(input);\n      }).length === 0;\n    }\n    return arrayTest && dataTypeTest;\n  }\n  function isCalendarSpec(input) {\n    var objectTest = isObject(input) && !isObjectEmpty(input),\n      propertyTest = false,\n      properties = ['sameDay', 'nextDay', 'lastDay', 'nextWeek', 'lastWeek', 'sameElse'],\n      i,\n      property;\n    for (i = 0; i < properties.length; i += 1) {\n      property = properties[i];\n      propertyTest = propertyTest || hasOwnProp(input, property);\n    }\n    return objectTest && propertyTest;\n  }\n  function getCalendarFormat(myMoment, now) {\n    var diff = myMoment.diff(now, 'days', true);\n    return diff < -6 ? 'sameElse' : diff < -1 ? 'lastWeek' : diff < 0 ? 'lastDay' : diff < 1 ? 'sameDay' : diff < 2 ? 'nextDay' : diff < 7 ? 'nextWeek' : 'sameElse';\n  }\n  function calendar$1(time, formats) {\n    // Support for single parameter, formats only overload to the calendar function\n    if (arguments.length === 1) {\n      if (!arguments[0]) {\n        time = undefined;\n        formats = undefined;\n      } else if (isMomentInput(arguments[0])) {\n        time = arguments[0];\n        formats = undefined;\n      } else if (isCalendarSpec(arguments[0])) {\n        formats = arguments[0];\n        time = undefined;\n      }\n    }\n    // We want to compare the start of today, vs this.\n    // Getting start-of-today depends on whether we're local/utc/offset or not.\n    var now = time || createLocal(),\n      sod = cloneWithOffset(now, this).startOf('day'),\n      format = hooks.calendarFormat(this, sod) || 'sameElse',\n      output = formats && (isFunction(formats[format]) ? formats[format].call(this, now) : formats[format]);\n    return this.format(output || this.localeData().calendar(format, this, createLocal(now)));\n  }\n  function clone() {\n    return new Moment(this);\n  }\n  function isAfter(input, units) {\n    var localInput = isMoment(input) ? input : createLocal(input);\n    if (!(this.isValid() && localInput.isValid())) {\n      return false;\n    }\n    units = normalizeUnits(units) || 'millisecond';\n    if (units === 'millisecond') {\n      return this.valueOf() > localInput.valueOf();\n    } else {\n      return localInput.valueOf() < this.clone().startOf(units).valueOf();\n    }\n  }\n  function isBefore(input, units) {\n    var localInput = isMoment(input) ? input : createLocal(input);\n    if (!(this.isValid() && localInput.isValid())) {\n      return false;\n    }\n    units = normalizeUnits(units) || 'millisecond';\n    if (units === 'millisecond') {\n      return this.valueOf() < localInput.valueOf();\n    } else {\n      return this.clone().endOf(units).valueOf() < localInput.valueOf();\n    }\n  }\n  function isBetween(from, to, units, inclusivity) {\n    var localFrom = isMoment(from) ? from : createLocal(from),\n      localTo = isMoment(to) ? to : createLocal(to);\n    if (!(this.isValid() && localFrom.isValid() && localTo.isValid())) {\n      return false;\n    }\n    inclusivity = inclusivity || '()';\n    return (inclusivity[0] === '(' ? this.isAfter(localFrom, units) : !this.isBefore(localFrom, units)) && (inclusivity[1] === ')' ? this.isBefore(localTo, units) : !this.isAfter(localTo, units));\n  }\n  function isSame(input, units) {\n    var localInput = isMoment(input) ? input : createLocal(input),\n      inputMs;\n    if (!(this.isValid() && localInput.isValid())) {\n      return false;\n    }\n    units = normalizeUnits(units) || 'millisecond';\n    if (units === 'millisecond') {\n      return this.valueOf() === localInput.valueOf();\n    } else {\n      inputMs = localInput.valueOf();\n      return this.clone().startOf(units).valueOf() <= inputMs && inputMs <= this.clone().endOf(units).valueOf();\n    }\n  }\n  function isSameOrAfter(input, units) {\n    return this.isSame(input, units) || this.isAfter(input, units);\n  }\n  function isSameOrBefore(input, units) {\n    return this.isSame(input, units) || this.isBefore(input, units);\n  }\n  function diff(input, units, asFloat) {\n    var that, zoneDelta, output;\n    if (!this.isValid()) {\n      return NaN;\n    }\n    that = cloneWithOffset(input, this);\n    if (!that.isValid()) {\n      return NaN;\n    }\n    zoneDelta = (that.utcOffset() - this.utcOffset()) * 6e4;\n    units = normalizeUnits(units);\n    switch (units) {\n      case 'year':\n        output = monthDiff(this, that) / 12;\n        break;\n      case 'month':\n        output = monthDiff(this, that);\n        break;\n      case 'quarter':\n        output = monthDiff(this, that) / 3;\n        break;\n      case 'second':\n        output = (this - that) / 1e3;\n        break;\n      // 1000\n      case 'minute':\n        output = (this - that) / 6e4;\n        break;\n      // 1000 * 60\n      case 'hour':\n        output = (this - that) / 36e5;\n        break;\n      // 1000 * 60 * 60\n      case 'day':\n        output = (this - that - zoneDelta) / 864e5;\n        break;\n      // 1000 * 60 * 60 * 24, negate dst\n      case 'week':\n        output = (this - that - zoneDelta) / 6048e5;\n        break;\n      // 1000 * 60 * 60 * 24 * 7, negate dst\n      default:\n        output = this - that;\n    }\n    return asFloat ? output : absFloor(output);\n  }\n  function monthDiff(a, b) {\n    if (a.date() < b.date()) {\n      // end-of-month calculations work correct when the start month has more\n      // days than the end month.\n      return -monthDiff(b, a);\n    }\n    // difference in months\n    var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month()),\n      // b is in (anchor - 1 month, anchor + 1 month)\n      anchor = a.clone().add(wholeMonthDiff, 'months'),\n      anchor2,\n      adjust;\n    if (b - anchor < 0) {\n      anchor2 = a.clone().add(wholeMonthDiff - 1, 'months');\n      // linear across the month\n      adjust = (b - anchor) / (anchor - anchor2);\n    } else {\n      anchor2 = a.clone().add(wholeMonthDiff + 1, 'months');\n      // linear across the month\n      adjust = (b - anchor) / (anchor2 - anchor);\n    }\n\n    //check for negative zero, return zero if negative zero\n    return -(wholeMonthDiff + adjust) || 0;\n  }\n  hooks.defaultFormat = 'YYYY-MM-DDTHH:mm:ssZ';\n  hooks.defaultFormatUtc = 'YYYY-MM-DDTHH:mm:ss[Z]';\n  function toString() {\n    return this.clone().locale('en').format('ddd MMM DD YYYY HH:mm:ss [GMT]ZZ');\n  }\n  function toISOString(keepOffset) {\n    if (!this.isValid()) {\n      return null;\n    }\n    var utc = keepOffset !== true,\n      m = utc ? this.clone().utc() : this;\n    if (m.year() < 0 || m.year() > 9999) {\n      return formatMoment(m, utc ? 'YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]' : 'YYYYYY-MM-DD[T]HH:mm:ss.SSSZ');\n    }\n    if (isFunction(Date.prototype.toISOString)) {\n      // native implementation is ~50x faster, use it when we can\n      if (utc) {\n        return this.toDate().toISOString();\n      } else {\n        return new Date(this.valueOf() + this.utcOffset() * 60 * 1000).toISOString().replace('Z', formatMoment(m, 'Z'));\n      }\n    }\n    return formatMoment(m, utc ? 'YYYY-MM-DD[T]HH:mm:ss.SSS[Z]' : 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');\n  }\n\n  /**\n   * Return a human readable representation of a moment that can\n   * also be evaluated to get a new moment which is the same\n   *\n   * @link https://nodejs.org/dist/latest/docs/api/util.html#util_custom_inspect_function_on_objects\n   */\n  function inspect() {\n    if (!this.isValid()) {\n      return 'moment.invalid(/* ' + this._i + ' */)';\n    }\n    var func = 'moment',\n      zone = '',\n      prefix,\n      year,\n      datetime,\n      suffix;\n    if (!this.isLocal()) {\n      func = this.utcOffset() === 0 ? 'moment.utc' : 'moment.parseZone';\n      zone = 'Z';\n    }\n    prefix = '[' + func + '(\"]';\n    year = 0 <= this.year() && this.year() <= 9999 ? 'YYYY' : 'YYYYYY';\n    datetime = '-MM-DD[T]HH:mm:ss.SSS';\n    suffix = zone + '[\")]';\n    return this.format(prefix + year + datetime + suffix);\n  }\n  function format(inputString) {\n    if (!inputString) {\n      inputString = this.isUtc() ? hooks.defaultFormatUtc : hooks.defaultFormat;\n    }\n    var output = formatMoment(this, inputString);\n    return this.localeData().postformat(output);\n  }\n  function from(time, withoutSuffix) {\n    if (this.isValid() && (isMoment(time) && time.isValid() || createLocal(time).isValid())) {\n      return createDuration({\n        to: this,\n        from: time\n      }).locale(this.locale()).humanize(!withoutSuffix);\n    } else {\n      return this.localeData().invalidDate();\n    }\n  }\n  function fromNow(withoutSuffix) {\n    return this.from(createLocal(), withoutSuffix);\n  }\n  function to(time, withoutSuffix) {\n    if (this.isValid() && (isMoment(time) && time.isValid() || createLocal(time).isValid())) {\n      return createDuration({\n        from: this,\n        to: time\n      }).locale(this.locale()).humanize(!withoutSuffix);\n    } else {\n      return this.localeData().invalidDate();\n    }\n  }\n  function toNow(withoutSuffix) {\n    return this.to(createLocal(), withoutSuffix);\n  }\n\n  // If passed a locale key, it will set the locale for this\n  // instance.  Otherwise, it will return the locale configuration\n  // variables for this instance.\n  function locale(key) {\n    var newLocaleData;\n    if (key === undefined) {\n      return this._locale._abbr;\n    } else {\n      newLocaleData = getLocale(key);\n      if (newLocaleData != null) {\n        this._locale = newLocaleData;\n      }\n      return this;\n    }\n  }\n  var lang = deprecate('moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.', function (key) {\n    if (key === undefined) {\n      return this.localeData();\n    } else {\n      return this.locale(key);\n    }\n  });\n  function localeData() {\n    return this._locale;\n  }\n  var MS_PER_SECOND = 1000,\n    MS_PER_MINUTE = 60 * MS_PER_SECOND,\n    MS_PER_HOUR = 60 * MS_PER_MINUTE,\n    MS_PER_400_YEARS = (365 * 400 + 97) * 24 * MS_PER_HOUR;\n\n  // actual modulo - handles negative numbers (for dates before 1970):\n  function mod$1(dividend, divisor) {\n    return (dividend % divisor + divisor) % divisor;\n  }\n  function localStartOfDate(y, m, d) {\n    // the date constructor remaps years 0-99 to 1900-1999\n    if (y < 100 && y >= 0) {\n      // preserve leap years using a full 400 year cycle, then reset\n      return new Date(y + 400, m, d) - MS_PER_400_YEARS;\n    } else {\n      return new Date(y, m, d).valueOf();\n    }\n  }\n  function utcStartOfDate(y, m, d) {\n    // Date.UTC remaps years 0-99 to 1900-1999\n    if (y < 100 && y >= 0) {\n      // preserve leap years using a full 400 year cycle, then reset\n      return Date.UTC(y + 400, m, d) - MS_PER_400_YEARS;\n    } else {\n      return Date.UTC(y, m, d);\n    }\n  }\n  function startOf(units) {\n    var time, startOfDate;\n    units = normalizeUnits(units);\n    if (units === undefined || units === 'millisecond' || !this.isValid()) {\n      return this;\n    }\n    startOfDate = this._isUTC ? utcStartOfDate : localStartOfDate;\n    switch (units) {\n      case 'year':\n        time = startOfDate(this.year(), 0, 1);\n        break;\n      case 'quarter':\n        time = startOfDate(this.year(), this.month() - this.month() % 3, 1);\n        break;\n      case 'month':\n        time = startOfDate(this.year(), this.month(), 1);\n        break;\n      case 'week':\n        time = startOfDate(this.year(), this.month(), this.date() - this.weekday());\n        break;\n      case 'isoWeek':\n        time = startOfDate(this.year(), this.month(), this.date() - (this.isoWeekday() - 1));\n        break;\n      case 'day':\n      case 'date':\n        time = startOfDate(this.year(), this.month(), this.date());\n        break;\n      case 'hour':\n        time = this._d.valueOf();\n        time -= mod$1(time + (this._isUTC ? 0 : this.utcOffset() * MS_PER_MINUTE), MS_PER_HOUR);\n        break;\n      case 'minute':\n        time = this._d.valueOf();\n        time -= mod$1(time, MS_PER_MINUTE);\n        break;\n      case 'second':\n        time = this._d.valueOf();\n        time -= mod$1(time, MS_PER_SECOND);\n        break;\n    }\n    this._d.setTime(time);\n    hooks.updateOffset(this, true);\n    return this;\n  }\n  function endOf(units) {\n    var time, startOfDate;\n    units = normalizeUnits(units);\n    if (units === undefined || units === 'millisecond' || !this.isValid()) {\n      return this;\n    }\n    startOfDate = this._isUTC ? utcStartOfDate : localStartOfDate;\n    switch (units) {\n      case 'year':\n        time = startOfDate(this.year() + 1, 0, 1) - 1;\n        break;\n      case 'quarter':\n        time = startOfDate(this.year(), this.month() - this.month() % 3 + 3, 1) - 1;\n        break;\n      case 'month':\n        time = startOfDate(this.year(), this.month() + 1, 1) - 1;\n        break;\n      case 'week':\n        time = startOfDate(this.year(), this.month(), this.date() - this.weekday() + 7) - 1;\n        break;\n      case 'isoWeek':\n        time = startOfDate(this.year(), this.month(), this.date() - (this.isoWeekday() - 1) + 7) - 1;\n        break;\n      case 'day':\n      case 'date':\n        time = startOfDate(this.year(), this.month(), this.date() + 1) - 1;\n        break;\n      case 'hour':\n        time = this._d.valueOf();\n        time += MS_PER_HOUR - mod$1(time + (this._isUTC ? 0 : this.utcOffset() * MS_PER_MINUTE), MS_PER_HOUR) - 1;\n        break;\n      case 'minute':\n        time = this._d.valueOf();\n        time += MS_PER_MINUTE - mod$1(time, MS_PER_MINUTE) - 1;\n        break;\n      case 'second':\n        time = this._d.valueOf();\n        time += MS_PER_SECOND - mod$1(time, MS_PER_SECOND) - 1;\n        break;\n    }\n    this._d.setTime(time);\n    hooks.updateOffset(this, true);\n    return this;\n  }\n  function valueOf() {\n    return this._d.valueOf() - (this._offset || 0) * 60000;\n  }\n  function unix() {\n    return Math.floor(this.valueOf() / 1000);\n  }\n  function toDate() {\n    return new Date(this.valueOf());\n  }\n  function toArray() {\n    var m = this;\n    return [m.year(), m.month(), m.date(), m.hour(), m.minute(), m.second(), m.millisecond()];\n  }\n  function toObject() {\n    var m = this;\n    return {\n      years: m.year(),\n      months: m.month(),\n      date: m.date(),\n      hours: m.hours(),\n      minutes: m.minutes(),\n      seconds: m.seconds(),\n      milliseconds: m.milliseconds()\n    };\n  }\n  function toJSON() {\n    // new Date(NaN).toJSON() === null\n    return this.isValid() ? this.toISOString() : null;\n  }\n  function isValid$2() {\n    return isValid(this);\n  }\n  function parsingFlags() {\n    return extend({}, getParsingFlags(this));\n  }\n  function invalidAt() {\n    return getParsingFlags(this).overflow;\n  }\n  function creationData() {\n    return {\n      input: this._i,\n      format: this._f,\n      locale: this._locale,\n      isUTC: this._isUTC,\n      strict: this._strict\n    };\n  }\n  addFormatToken('N', 0, 0, 'eraAbbr');\n  addFormatToken('NN', 0, 0, 'eraAbbr');\n  addFormatToken('NNN', 0, 0, 'eraAbbr');\n  addFormatToken('NNNN', 0, 0, 'eraName');\n  addFormatToken('NNNNN', 0, 0, 'eraNarrow');\n  addFormatToken('y', ['y', 1], 'yo', 'eraYear');\n  addFormatToken('y', ['yy', 2], 0, 'eraYear');\n  addFormatToken('y', ['yyy', 3], 0, 'eraYear');\n  addFormatToken('y', ['yyyy', 4], 0, 'eraYear');\n  addRegexToken('N', matchEraAbbr);\n  addRegexToken('NN', matchEraAbbr);\n  addRegexToken('NNN', matchEraAbbr);\n  addRegexToken('NNNN', matchEraName);\n  addRegexToken('NNNNN', matchEraNarrow);\n  addParseToken(['N', 'NN', 'NNN', 'NNNN', 'NNNNN'], function (input, array, config, token) {\n    var era = config._locale.erasParse(input, token, config._strict);\n    if (era) {\n      getParsingFlags(config).era = era;\n    } else {\n      getParsingFlags(config).invalidEra = input;\n    }\n  });\n  addRegexToken('y', matchUnsigned);\n  addRegexToken('yy', matchUnsigned);\n  addRegexToken('yyy', matchUnsigned);\n  addRegexToken('yyyy', matchUnsigned);\n  addRegexToken('yo', matchEraYearOrdinal);\n  addParseToken(['y', 'yy', 'yyy', 'yyyy'], YEAR);\n  addParseToken(['yo'], function (input, array, config, token) {\n    var match;\n    if (config._locale._eraYearOrdinalRegex) {\n      match = input.match(config._locale._eraYearOrdinalRegex);\n    }\n    if (config._locale.eraYearOrdinalParse) {\n      array[YEAR] = config._locale.eraYearOrdinalParse(input, match);\n    } else {\n      array[YEAR] = parseInt(input, 10);\n    }\n  });\n  function localeEras(m, format) {\n    var i,\n      l,\n      date,\n      eras = this._eras || getLocale('en')._eras;\n    for (i = 0, l = eras.length; i < l; ++i) {\n      switch (typeof eras[i].since) {\n        case 'string':\n          // truncate time\n          date = hooks(eras[i].since).startOf('day');\n          eras[i].since = date.valueOf();\n          break;\n      }\n      switch (typeof eras[i].until) {\n        case 'undefined':\n          eras[i].until = +Infinity;\n          break;\n        case 'string':\n          // truncate time\n          date = hooks(eras[i].until).startOf('day').valueOf();\n          eras[i].until = date.valueOf();\n          break;\n      }\n    }\n    return eras;\n  }\n  function localeErasParse(eraName, format, strict) {\n    var i,\n      l,\n      eras = this.eras(),\n      name,\n      abbr,\n      narrow;\n    eraName = eraName.toUpperCase();\n    for (i = 0, l = eras.length; i < l; ++i) {\n      name = eras[i].name.toUpperCase();\n      abbr = eras[i].abbr.toUpperCase();\n      narrow = eras[i].narrow.toUpperCase();\n      if (strict) {\n        switch (format) {\n          case 'N':\n          case 'NN':\n          case 'NNN':\n            if (abbr === eraName) {\n              return eras[i];\n            }\n            break;\n          case 'NNNN':\n            if (name === eraName) {\n              return eras[i];\n            }\n            break;\n          case 'NNNNN':\n            if (narrow === eraName) {\n              return eras[i];\n            }\n            break;\n        }\n      } else if ([name, abbr, narrow].indexOf(eraName) >= 0) {\n        return eras[i];\n      }\n    }\n  }\n  function localeErasConvertYear(era, year) {\n    var dir = era.since <= era.until ? +1 : -1;\n    if (year === undefined) {\n      return hooks(era.since).year();\n    } else {\n      return hooks(era.since).year() + (year - era.offset) * dir;\n    }\n  }\n  function getEraName() {\n    var i,\n      l,\n      val,\n      eras = this.localeData().eras();\n    for (i = 0, l = eras.length; i < l; ++i) {\n      // truncate time\n      val = this.clone().startOf('day').valueOf();\n      if (eras[i].since <= val && val <= eras[i].until) {\n        return eras[i].name;\n      }\n      if (eras[i].until <= val && val <= eras[i].since) {\n        return eras[i].name;\n      }\n    }\n    return '';\n  }\n  function getEraNarrow() {\n    var i,\n      l,\n      val,\n      eras = this.localeData().eras();\n    for (i = 0, l = eras.length; i < l; ++i) {\n      // truncate time\n      val = this.clone().startOf('day').valueOf();\n      if (eras[i].since <= val && val <= eras[i].until) {\n        return eras[i].narrow;\n      }\n      if (eras[i].until <= val && val <= eras[i].since) {\n        return eras[i].narrow;\n      }\n    }\n    return '';\n  }\n  function getEraAbbr() {\n    var i,\n      l,\n      val,\n      eras = this.localeData().eras();\n    for (i = 0, l = eras.length; i < l; ++i) {\n      // truncate time\n      val = this.clone().startOf('day').valueOf();\n      if (eras[i].since <= val && val <= eras[i].until) {\n        return eras[i].abbr;\n      }\n      if (eras[i].until <= val && val <= eras[i].since) {\n        return eras[i].abbr;\n      }\n    }\n    return '';\n  }\n  function getEraYear() {\n    var i,\n      l,\n      dir,\n      val,\n      eras = this.localeData().eras();\n    for (i = 0, l = eras.length; i < l; ++i) {\n      dir = eras[i].since <= eras[i].until ? +1 : -1;\n\n      // truncate time\n      val = this.clone().startOf('day').valueOf();\n      if (eras[i].since <= val && val <= eras[i].until || eras[i].until <= val && val <= eras[i].since) {\n        return (this.year() - hooks(eras[i].since).year()) * dir + eras[i].offset;\n      }\n    }\n    return this.year();\n  }\n  function erasNameRegex(isStrict) {\n    if (!hasOwnProp(this, '_erasNameRegex')) {\n      computeErasParse.call(this);\n    }\n    return isStrict ? this._erasNameRegex : this._erasRegex;\n  }\n  function erasAbbrRegex(isStrict) {\n    if (!hasOwnProp(this, '_erasAbbrRegex')) {\n      computeErasParse.call(this);\n    }\n    return isStrict ? this._erasAbbrRegex : this._erasRegex;\n  }\n  function erasNarrowRegex(isStrict) {\n    if (!hasOwnProp(this, '_erasNarrowRegex')) {\n      computeErasParse.call(this);\n    }\n    return isStrict ? this._erasNarrowRegex : this._erasRegex;\n  }\n  function matchEraAbbr(isStrict, locale) {\n    return locale.erasAbbrRegex(isStrict);\n  }\n  function matchEraName(isStrict, locale) {\n    return locale.erasNameRegex(isStrict);\n  }\n  function matchEraNarrow(isStrict, locale) {\n    return locale.erasNarrowRegex(isStrict);\n  }\n  function matchEraYearOrdinal(isStrict, locale) {\n    return locale._eraYearOrdinalRegex || matchUnsigned;\n  }\n  function computeErasParse() {\n    var abbrPieces = [],\n      namePieces = [],\n      narrowPieces = [],\n      mixedPieces = [],\n      i,\n      l,\n      eras = this.eras();\n    for (i = 0, l = eras.length; i < l; ++i) {\n      namePieces.push(regexEscape(eras[i].name));\n      abbrPieces.push(regexEscape(eras[i].abbr));\n      narrowPieces.push(regexEscape(eras[i].narrow));\n      mixedPieces.push(regexEscape(eras[i].name));\n      mixedPieces.push(regexEscape(eras[i].abbr));\n      mixedPieces.push(regexEscape(eras[i].narrow));\n    }\n    this._erasRegex = new RegExp('^(' + mixedPieces.join('|') + ')', 'i');\n    this._erasNameRegex = new RegExp('^(' + namePieces.join('|') + ')', 'i');\n    this._erasAbbrRegex = new RegExp('^(' + abbrPieces.join('|') + ')', 'i');\n    this._erasNarrowRegex = new RegExp('^(' + narrowPieces.join('|') + ')', 'i');\n  }\n\n  // FORMATTING\n\n  addFormatToken(0, ['gg', 2], 0, function () {\n    return this.weekYear() % 100;\n  });\n  addFormatToken(0, ['GG', 2], 0, function () {\n    return this.isoWeekYear() % 100;\n  });\n  function addWeekYearFormatToken(token, getter) {\n    addFormatToken(0, [token, token.length], 0, getter);\n  }\n  addWeekYearFormatToken('gggg', 'weekYear');\n  addWeekYearFormatToken('ggggg', 'weekYear');\n  addWeekYearFormatToken('GGGG', 'isoWeekYear');\n  addWeekYearFormatToken('GGGGG', 'isoWeekYear');\n\n  // ALIASES\n\n  addUnitAlias('weekYear', 'gg');\n  addUnitAlias('isoWeekYear', 'GG');\n\n  // PRIORITY\n\n  addUnitPriority('weekYear', 1);\n  addUnitPriority('isoWeekYear', 1);\n\n  // PARSING\n\n  addRegexToken('G', matchSigned);\n  addRegexToken('g', matchSigned);\n  addRegexToken('GG', match1to2, match2);\n  addRegexToken('gg', match1to2, match2);\n  addRegexToken('GGGG', match1to4, match4);\n  addRegexToken('gggg', match1to4, match4);\n  addRegexToken('GGGGG', match1to6, match6);\n  addRegexToken('ggggg', match1to6, match6);\n  addWeekParseToken(['gggg', 'ggggg', 'GGGG', 'GGGGG'], function (input, week, config, token) {\n    week[token.substr(0, 2)] = toInt(input);\n  });\n  addWeekParseToken(['gg', 'GG'], function (input, week, config, token) {\n    week[token] = hooks.parseTwoDigitYear(input);\n  });\n\n  // MOMENTS\n\n  function getSetWeekYear(input) {\n    return getSetWeekYearHelper.call(this, input, this.week(), this.weekday(), this.localeData()._week.dow, this.localeData()._week.doy);\n  }\n  function getSetISOWeekYear(input) {\n    return getSetWeekYearHelper.call(this, input, this.isoWeek(), this.isoWeekday(), 1, 4);\n  }\n  function getISOWeeksInYear() {\n    return weeksInYear(this.year(), 1, 4);\n  }\n  function getISOWeeksInISOWeekYear() {\n    return weeksInYear(this.isoWeekYear(), 1, 4);\n  }\n  function getWeeksInYear() {\n    var weekInfo = this.localeData()._week;\n    return weeksInYear(this.year(), weekInfo.dow, weekInfo.doy);\n  }\n  function getWeeksInWeekYear() {\n    var weekInfo = this.localeData()._week;\n    return weeksInYear(this.weekYear(), weekInfo.dow, weekInfo.doy);\n  }\n  function getSetWeekYearHelper(input, week, weekday, dow, doy) {\n    var weeksTarget;\n    if (input == null) {\n      return weekOfYear(this, dow, doy).year;\n    } else {\n      weeksTarget = weeksInYear(input, dow, doy);\n      if (week > weeksTarget) {\n        week = weeksTarget;\n      }\n      return setWeekAll.call(this, input, week, weekday, dow, doy);\n    }\n  }\n  function setWeekAll(weekYear, week, weekday, dow, doy) {\n    var dayOfYearData = dayOfYearFromWeeks(weekYear, week, weekday, dow, doy),\n      date = createUTCDate(dayOfYearData.year, 0, dayOfYearData.dayOfYear);\n    this.year(date.getUTCFullYear());\n    this.month(date.getUTCMonth());\n    this.date(date.getUTCDate());\n    return this;\n  }\n\n  // FORMATTING\n\n  addFormatToken('Q', 0, 'Qo', 'quarter');\n\n  // ALIASES\n\n  addUnitAlias('quarter', 'Q');\n\n  // PRIORITY\n\n  addUnitPriority('quarter', 7);\n\n  // PARSING\n\n  addRegexToken('Q', match1);\n  addParseToken('Q', function (input, array) {\n    array[MONTH] = (toInt(input) - 1) * 3;\n  });\n\n  // MOMENTS\n\n  function getSetQuarter(input) {\n    return input == null ? Math.ceil((this.month() + 1) / 3) : this.month((input - 1) * 3 + this.month() % 3);\n  }\n\n  // FORMATTING\n\n  addFormatToken('D', ['DD', 2], 'Do', 'date');\n\n  // ALIASES\n\n  addUnitAlias('date', 'D');\n\n  // PRIORITY\n  addUnitPriority('date', 9);\n\n  // PARSING\n\n  addRegexToken('D', match1to2);\n  addRegexToken('DD', match1to2, match2);\n  addRegexToken('Do', function (isStrict, locale) {\n    // TODO: Remove \"ordinalParse\" fallback in next major release.\n    return isStrict ? locale._dayOfMonthOrdinalParse || locale._ordinalParse : locale._dayOfMonthOrdinalParseLenient;\n  });\n  addParseToken(['D', 'DD'], DATE);\n  addParseToken('Do', function (input, array) {\n    array[DATE] = toInt(input.match(match1to2)[0]);\n  });\n\n  // MOMENTS\n\n  var getSetDayOfMonth = makeGetSet('Date', true);\n\n  // FORMATTING\n\n  addFormatToken('DDD', ['DDDD', 3], 'DDDo', 'dayOfYear');\n\n  // ALIASES\n\n  addUnitAlias('dayOfYear', 'DDD');\n\n  // PRIORITY\n  addUnitPriority('dayOfYear', 4);\n\n  // PARSING\n\n  addRegexToken('DDD', match1to3);\n  addRegexToken('DDDD', match3);\n  addParseToken(['DDD', 'DDDD'], function (input, array, config) {\n    config._dayOfYear = toInt(input);\n  });\n\n  // HELPERS\n\n  // MOMENTS\n\n  function getSetDayOfYear(input) {\n    var dayOfYear = Math.round((this.clone().startOf('day') - this.clone().startOf('year')) / 864e5) + 1;\n    return input == null ? dayOfYear : this.add(input - dayOfYear, 'd');\n  }\n\n  // FORMATTING\n\n  addFormatToken('m', ['mm', 2], 0, 'minute');\n\n  // ALIASES\n\n  addUnitAlias('minute', 'm');\n\n  // PRIORITY\n\n  addUnitPriority('minute', 14);\n\n  // PARSING\n\n  addRegexToken('m', match1to2);\n  addRegexToken('mm', match1to2, match2);\n  addParseToken(['m', 'mm'], MINUTE);\n\n  // MOMENTS\n\n  var getSetMinute = makeGetSet('Minutes', false);\n\n  // FORMATTING\n\n  addFormatToken('s', ['ss', 2], 0, 'second');\n\n  // ALIASES\n\n  addUnitAlias('second', 's');\n\n  // PRIORITY\n\n  addUnitPriority('second', 15);\n\n  // PARSING\n\n  addRegexToken('s', match1to2);\n  addRegexToken('ss', match1to2, match2);\n  addParseToken(['s', 'ss'], SECOND);\n\n  // MOMENTS\n\n  var getSetSecond = makeGetSet('Seconds', false);\n\n  // FORMATTING\n\n  addFormatToken('S', 0, 0, function () {\n    return ~~(this.millisecond() / 100);\n  });\n  addFormatToken(0, ['SS', 2], 0, function () {\n    return ~~(this.millisecond() / 10);\n  });\n  addFormatToken(0, ['SSS', 3], 0, 'millisecond');\n  addFormatToken(0, ['SSSS', 4], 0, function () {\n    return this.millisecond() * 10;\n  });\n  addFormatToken(0, ['SSSSS', 5], 0, function () {\n    return this.millisecond() * 100;\n  });\n  addFormatToken(0, ['SSSSSS', 6], 0, function () {\n    return this.millisecond() * 1000;\n  });\n  addFormatToken(0, ['SSSSSSS', 7], 0, function () {\n    return this.millisecond() * 10000;\n  });\n  addFormatToken(0, ['SSSSSSSS', 8], 0, function () {\n    return this.millisecond() * 100000;\n  });\n  addFormatToken(0, ['SSSSSSSSS', 9], 0, function () {\n    return this.millisecond() * 1000000;\n  });\n\n  // ALIASES\n\n  addUnitAlias('millisecond', 'ms');\n\n  // PRIORITY\n\n  addUnitPriority('millisecond', 16);\n\n  // PARSING\n\n  addRegexToken('S', match1to3, match1);\n  addRegexToken('SS', match1to3, match2);\n  addRegexToken('SSS', match1to3, match3);\n  var token, getSetMillisecond;\n  for (token = 'SSSS'; token.length <= 9; token += 'S') {\n    addRegexToken(token, matchUnsigned);\n  }\n  function parseMs(input, array) {\n    array[MILLISECOND] = toInt(('0.' + input) * 1000);\n  }\n  for (token = 'S'; token.length <= 9; token += 'S') {\n    addParseToken(token, parseMs);\n  }\n  getSetMillisecond = makeGetSet('Milliseconds', false);\n\n  // FORMATTING\n\n  addFormatToken('z', 0, 0, 'zoneAbbr');\n  addFormatToken('zz', 0, 0, 'zoneName');\n\n  // MOMENTS\n\n  function getZoneAbbr() {\n    return this._isUTC ? 'UTC' : '';\n  }\n  function getZoneName() {\n    return this._isUTC ? 'Coordinated Universal Time' : '';\n  }\n  var proto = Moment.prototype;\n  proto.add = add;\n  proto.calendar = calendar$1;\n  proto.clone = clone;\n  proto.diff = diff;\n  proto.endOf = endOf;\n  proto.format = format;\n  proto.from = from;\n  proto.fromNow = fromNow;\n  proto.to = to;\n  proto.toNow = toNow;\n  proto.get = stringGet;\n  proto.invalidAt = invalidAt;\n  proto.isAfter = isAfter;\n  proto.isBefore = isBefore;\n  proto.isBetween = isBetween;\n  proto.isSame = isSame;\n  proto.isSameOrAfter = isSameOrAfter;\n  proto.isSameOrBefore = isSameOrBefore;\n  proto.isValid = isValid$2;\n  proto.lang = lang;\n  proto.locale = locale;\n  proto.localeData = localeData;\n  proto.max = prototypeMax;\n  proto.min = prototypeMin;\n  proto.parsingFlags = parsingFlags;\n  proto.set = stringSet;\n  proto.startOf = startOf;\n  proto.subtract = subtract;\n  proto.toArray = toArray;\n  proto.toObject = toObject;\n  proto.toDate = toDate;\n  proto.toISOString = toISOString;\n  proto.inspect = inspect;\n  if (typeof Symbol !== 'undefined' && Symbol.for != null) {\n    proto[Symbol.for('nodejs.util.inspect.custom')] = function () {\n      return 'Moment<' + this.format() + '>';\n    };\n  }\n  proto.toJSON = toJSON;\n  proto.toString = toString;\n  proto.unix = unix;\n  proto.valueOf = valueOf;\n  proto.creationData = creationData;\n  proto.eraName = getEraName;\n  proto.eraNarrow = getEraNarrow;\n  proto.eraAbbr = getEraAbbr;\n  proto.eraYear = getEraYear;\n  proto.year = getSetYear;\n  proto.isLeapYear = getIsLeapYear;\n  proto.weekYear = getSetWeekYear;\n  proto.isoWeekYear = getSetISOWeekYear;\n  proto.quarter = proto.quarters = getSetQuarter;\n  proto.month = getSetMonth;\n  proto.daysInMonth = getDaysInMonth;\n  proto.week = proto.weeks = getSetWeek;\n  proto.isoWeek = proto.isoWeeks = getSetISOWeek;\n  proto.weeksInYear = getWeeksInYear;\n  proto.weeksInWeekYear = getWeeksInWeekYear;\n  proto.isoWeeksInYear = getISOWeeksInYear;\n  proto.isoWeeksInISOWeekYear = getISOWeeksInISOWeekYear;\n  proto.date = getSetDayOfMonth;\n  proto.day = proto.days = getSetDayOfWeek;\n  proto.weekday = getSetLocaleDayOfWeek;\n  proto.isoWeekday = getSetISODayOfWeek;\n  proto.dayOfYear = getSetDayOfYear;\n  proto.hour = proto.hours = getSetHour;\n  proto.minute = proto.minutes = getSetMinute;\n  proto.second = proto.seconds = getSetSecond;\n  proto.millisecond = proto.milliseconds = getSetMillisecond;\n  proto.utcOffset = getSetOffset;\n  proto.utc = setOffsetToUTC;\n  proto.local = setOffsetToLocal;\n  proto.parseZone = setOffsetToParsedOffset;\n  proto.hasAlignedHourOffset = hasAlignedHourOffset;\n  proto.isDST = isDaylightSavingTime;\n  proto.isLocal = isLocal;\n  proto.isUtcOffset = isUtcOffset;\n  proto.isUtc = isUtc;\n  proto.isUTC = isUtc;\n  proto.zoneAbbr = getZoneAbbr;\n  proto.zoneName = getZoneName;\n  proto.dates = deprecate('dates accessor is deprecated. Use date instead.', getSetDayOfMonth);\n  proto.months = deprecate('months accessor is deprecated. Use month instead', getSetMonth);\n  proto.years = deprecate('years accessor is deprecated. Use year instead', getSetYear);\n  proto.zone = deprecate('moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/', getSetZone);\n  proto.isDSTShifted = deprecate('isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information', isDaylightSavingTimeShifted);\n  function createUnix(input) {\n    return createLocal(input * 1000);\n  }\n  function createInZone() {\n    return createLocal.apply(null, arguments).parseZone();\n  }\n  function preParsePostFormat(string) {\n    return string;\n  }\n  var proto$1 = Locale.prototype;\n  proto$1.calendar = calendar;\n  proto$1.longDateFormat = longDateFormat;\n  proto$1.invalidDate = invalidDate;\n  proto$1.ordinal = ordinal;\n  proto$1.preparse = preParsePostFormat;\n  proto$1.postformat = preParsePostFormat;\n  proto$1.relativeTime = relativeTime;\n  proto$1.pastFuture = pastFuture;\n  proto$1.set = set;\n  proto$1.eras = localeEras;\n  proto$1.erasParse = localeErasParse;\n  proto$1.erasConvertYear = localeErasConvertYear;\n  proto$1.erasAbbrRegex = erasAbbrRegex;\n  proto$1.erasNameRegex = erasNameRegex;\n  proto$1.erasNarrowRegex = erasNarrowRegex;\n  proto$1.months = localeMonths;\n  proto$1.monthsShort = localeMonthsShort;\n  proto$1.monthsParse = localeMonthsParse;\n  proto$1.monthsRegex = monthsRegex;\n  proto$1.monthsShortRegex = monthsShortRegex;\n  proto$1.week = localeWeek;\n  proto$1.firstDayOfYear = localeFirstDayOfYear;\n  proto$1.firstDayOfWeek = localeFirstDayOfWeek;\n  proto$1.weekdays = localeWeekdays;\n  proto$1.weekdaysMin = localeWeekdaysMin;\n  proto$1.weekdaysShort = localeWeekdaysShort;\n  proto$1.weekdaysParse = localeWeekdaysParse;\n  proto$1.weekdaysRegex = weekdaysRegex;\n  proto$1.weekdaysShortRegex = weekdaysShortRegex;\n  proto$1.weekdaysMinRegex = weekdaysMinRegex;\n  proto$1.isPM = localeIsPM;\n  proto$1.meridiem = localeMeridiem;\n  function get$1(format, index, field, setter) {\n    var locale = getLocale(),\n      utc = createUTC().set(setter, index);\n    return locale[field](utc, format);\n  }\n  function listMonthsImpl(format, index, field) {\n    if (isNumber(format)) {\n      index = format;\n      format = undefined;\n    }\n    format = format || '';\n    if (index != null) {\n      return get$1(format, index, field, 'month');\n    }\n    var i,\n      out = [];\n    for (i = 0; i < 12; i++) {\n      out[i] = get$1(format, i, field, 'month');\n    }\n    return out;\n  }\n\n  // ()\n  // (5)\n  // (fmt, 5)\n  // (fmt)\n  // (true)\n  // (true, 5)\n  // (true, fmt, 5)\n  // (true, fmt)\n  function listWeekdaysImpl(localeSorted, format, index, field) {\n    if (typeof localeSorted === 'boolean') {\n      if (isNumber(format)) {\n        index = format;\n        format = undefined;\n      }\n      format = format || '';\n    } else {\n      format = localeSorted;\n      index = format;\n      localeSorted = false;\n      if (isNumber(format)) {\n        index = format;\n        format = undefined;\n      }\n      format = format || '';\n    }\n    var locale = getLocale(),\n      shift = localeSorted ? locale._week.dow : 0,\n      i,\n      out = [];\n    if (index != null) {\n      return get$1(format, (index + shift) % 7, field, 'day');\n    }\n    for (i = 0; i < 7; i++) {\n      out[i] = get$1(format, (i + shift) % 7, field, 'day');\n    }\n    return out;\n  }\n  function listMonths(format, index) {\n    return listMonthsImpl(format, index, 'months');\n  }\n  function listMonthsShort(format, index) {\n    return listMonthsImpl(format, index, 'monthsShort');\n  }\n  function listWeekdays(localeSorted, format, index) {\n    return listWeekdaysImpl(localeSorted, format, index, 'weekdays');\n  }\n  function listWeekdaysShort(localeSorted, format, index) {\n    return listWeekdaysImpl(localeSorted, format, index, 'weekdaysShort');\n  }\n  function listWeekdaysMin(localeSorted, format, index) {\n    return listWeekdaysImpl(localeSorted, format, index, 'weekdaysMin');\n  }\n  getSetGlobalLocale('en', {\n    eras: [{\n      since: '0001-01-01',\n      until: +Infinity,\n      offset: 1,\n      name: 'Anno Domini',\n      narrow: 'AD',\n      abbr: 'AD'\n    }, {\n      since: '0000-12-31',\n      until: -Infinity,\n      offset: 1,\n      name: 'Before Christ',\n      narrow: 'BC',\n      abbr: 'BC'\n    }],\n    dayOfMonthOrdinalParse: /\\d{1,2}(th|st|nd|rd)/,\n    ordinal: function (number) {\n      var b = number % 10,\n        output = toInt(number % 100 / 10) === 1 ? 'th' : b === 1 ? 'st' : b === 2 ? 'nd' : b === 3 ? 'rd' : 'th';\n      return number + output;\n    }\n  });\n\n  // Side effect imports\n\n  hooks.lang = deprecate('moment.lang is deprecated. Use moment.locale instead.', getSetGlobalLocale);\n  hooks.langData = deprecate('moment.langData is deprecated. Use moment.localeData instead.', getLocale);\n  var mathAbs = Math.abs;\n  function abs() {\n    var data = this._data;\n    this._milliseconds = mathAbs(this._milliseconds);\n    this._days = mathAbs(this._days);\n    this._months = mathAbs(this._months);\n    data.milliseconds = mathAbs(data.milliseconds);\n    data.seconds = mathAbs(data.seconds);\n    data.minutes = mathAbs(data.minutes);\n    data.hours = mathAbs(data.hours);\n    data.months = mathAbs(data.months);\n    data.years = mathAbs(data.years);\n    return this;\n  }\n  function addSubtract$1(duration, input, value, direction) {\n    var other = createDuration(input, value);\n    duration._milliseconds += direction * other._milliseconds;\n    duration._days += direction * other._days;\n    duration._months += direction * other._months;\n    return duration._bubble();\n  }\n\n  // supports only 2.0-style add(1, 's') or add(duration)\n  function add$1(input, value) {\n    return addSubtract$1(this, input, value, 1);\n  }\n\n  // supports only 2.0-style subtract(1, 's') or subtract(duration)\n  function subtract$1(input, value) {\n    return addSubtract$1(this, input, value, -1);\n  }\n  function absCeil(number) {\n    if (number < 0) {\n      return Math.floor(number);\n    } else {\n      return Math.ceil(number);\n    }\n  }\n  function bubble() {\n    var milliseconds = this._milliseconds,\n      days = this._days,\n      months = this._months,\n      data = this._data,\n      seconds,\n      minutes,\n      hours,\n      years,\n      monthsFromDays;\n\n    // if we have a mix of positive and negative values, bubble down first\n    // check: https://github.com/moment/moment/issues/2166\n    if (!(milliseconds >= 0 && days >= 0 && months >= 0 || milliseconds <= 0 && days <= 0 && months <= 0)) {\n      milliseconds += absCeil(monthsToDays(months) + days) * 864e5;\n      days = 0;\n      months = 0;\n    }\n\n    // The following code bubbles up values, see the tests for\n    // examples of what that means.\n    data.milliseconds = milliseconds % 1000;\n    seconds = absFloor(milliseconds / 1000);\n    data.seconds = seconds % 60;\n    minutes = absFloor(seconds / 60);\n    data.minutes = minutes % 60;\n    hours = absFloor(minutes / 60);\n    data.hours = hours % 24;\n    days += absFloor(hours / 24);\n\n    // convert days to months\n    monthsFromDays = absFloor(daysToMonths(days));\n    months += monthsFromDays;\n    days -= absCeil(monthsToDays(monthsFromDays));\n\n    // 12 months -> 1 year\n    years = absFloor(months / 12);\n    months %= 12;\n    data.days = days;\n    data.months = months;\n    data.years = years;\n    return this;\n  }\n  function daysToMonths(days) {\n    // 400 years have 146097 days (taking into account leap year rules)\n    // 400 years have 12 months === 4800\n    return days * 4800 / 146097;\n  }\n  function monthsToDays(months) {\n    // the reverse of daysToMonths\n    return months * 146097 / 4800;\n  }\n  function as(units) {\n    if (!this.isValid()) {\n      return NaN;\n    }\n    var days,\n      months,\n      milliseconds = this._milliseconds;\n    units = normalizeUnits(units);\n    if (units === 'month' || units === 'quarter' || units === 'year') {\n      days = this._days + milliseconds / 864e5;\n      months = this._months + daysToMonths(days);\n      switch (units) {\n        case 'month':\n          return months;\n        case 'quarter':\n          return months / 3;\n        case 'year':\n          return months / 12;\n      }\n    } else {\n      // handle milliseconds separately because of floating point math errors (issue #1867)\n      days = this._days + Math.round(monthsToDays(this._months));\n      switch (units) {\n        case 'week':\n          return days / 7 + milliseconds / 6048e5;\n        case 'day':\n          return days + milliseconds / 864e5;\n        case 'hour':\n          return days * 24 + milliseconds / 36e5;\n        case 'minute':\n          return days * 1440 + milliseconds / 6e4;\n        case 'second':\n          return days * 86400 + milliseconds / 1000;\n        // Math.floor prevents floating point math errors here\n        case 'millisecond':\n          return Math.floor(days * 864e5) + milliseconds;\n        default:\n          throw new Error('Unknown unit ' + units);\n      }\n    }\n  }\n\n  // TODO: Use this.as('ms')?\n  function valueOf$1() {\n    if (!this.isValid()) {\n      return NaN;\n    }\n    return this._milliseconds + this._days * 864e5 + this._months % 12 * 2592e6 + toInt(this._months / 12) * 31536e6;\n  }\n  function makeAs(alias) {\n    return function () {\n      return this.as(alias);\n    };\n  }\n  var asMilliseconds = makeAs('ms'),\n    asSeconds = makeAs('s'),\n    asMinutes = makeAs('m'),\n    asHours = makeAs('h'),\n    asDays = makeAs('d'),\n    asWeeks = makeAs('w'),\n    asMonths = makeAs('M'),\n    asQuarters = makeAs('Q'),\n    asYears = makeAs('y');\n  function clone$1() {\n    return createDuration(this);\n  }\n  function get$2(units) {\n    units = normalizeUnits(units);\n    return this.isValid() ? this[units + 's']() : NaN;\n  }\n  function makeGetter(name) {\n    return function () {\n      return this.isValid() ? this._data[name] : NaN;\n    };\n  }\n  var milliseconds = makeGetter('milliseconds'),\n    seconds = makeGetter('seconds'),\n    minutes = makeGetter('minutes'),\n    hours = makeGetter('hours'),\n    days = makeGetter('days'),\n    months = makeGetter('months'),\n    years = makeGetter('years');\n  function weeks() {\n    return absFloor(this.days() / 7);\n  }\n  var round = Math.round,\n    thresholds = {\n      ss: 44,\n      // a few seconds to seconds\n      s: 45,\n      // seconds to minute\n      m: 45,\n      // minutes to hour\n      h: 22,\n      // hours to day\n      d: 26,\n      // days to month/week\n      w: null,\n      // weeks to month\n      M: 11 // months to year\n    };\n\n  // helper function for moment.fn.from, moment.fn.fromNow, and moment.duration.fn.humanize\n  function substituteTimeAgo(string, number, withoutSuffix, isFuture, locale) {\n    return locale.relativeTime(number || 1, !!withoutSuffix, string, isFuture);\n  }\n  function relativeTime$1(posNegDuration, withoutSuffix, thresholds, locale) {\n    var duration = createDuration(posNegDuration).abs(),\n      seconds = round(duration.as('s')),\n      minutes = round(duration.as('m')),\n      hours = round(duration.as('h')),\n      days = round(duration.as('d')),\n      months = round(duration.as('M')),\n      weeks = round(duration.as('w')),\n      years = round(duration.as('y')),\n      a = seconds <= thresholds.ss && ['s', seconds] || seconds < thresholds.s && ['ss', seconds] || minutes <= 1 && ['m'] || minutes < thresholds.m && ['mm', minutes] || hours <= 1 && ['h'] || hours < thresholds.h && ['hh', hours] || days <= 1 && ['d'] || days < thresholds.d && ['dd', days];\n    if (thresholds.w != null) {\n      a = a || weeks <= 1 && ['w'] || weeks < thresholds.w && ['ww', weeks];\n    }\n    a = a || months <= 1 && ['M'] || months < thresholds.M && ['MM', months] || years <= 1 && ['y'] || ['yy', years];\n    a[2] = withoutSuffix;\n    a[3] = +posNegDuration > 0;\n    a[4] = locale;\n    return substituteTimeAgo.apply(null, a);\n  }\n\n  // This function allows you to set the rounding function for relative time strings\n  function getSetRelativeTimeRounding(roundingFunction) {\n    if (roundingFunction === undefined) {\n      return round;\n    }\n    if (typeof roundingFunction === 'function') {\n      round = roundingFunction;\n      return true;\n    }\n    return false;\n  }\n\n  // This function allows you to set a threshold for relative time strings\n  function getSetRelativeTimeThreshold(threshold, limit) {\n    if (thresholds[threshold] === undefined) {\n      return false;\n    }\n    if (limit === undefined) {\n      return thresholds[threshold];\n    }\n    thresholds[threshold] = limit;\n    if (threshold === 's') {\n      thresholds.ss = limit - 1;\n    }\n    return true;\n  }\n  function humanize(argWithSuffix, argThresholds) {\n    if (!this.isValid()) {\n      return this.localeData().invalidDate();\n    }\n    var withSuffix = false,\n      th = thresholds,\n      locale,\n      output;\n    if (typeof argWithSuffix === 'object') {\n      argThresholds = argWithSuffix;\n      argWithSuffix = false;\n    }\n    if (typeof argWithSuffix === 'boolean') {\n      withSuffix = argWithSuffix;\n    }\n    if (typeof argThresholds === 'object') {\n      th = Object.assign({}, thresholds, argThresholds);\n      if (argThresholds.s != null && argThresholds.ss == null) {\n        th.ss = argThresholds.s - 1;\n      }\n    }\n    locale = this.localeData();\n    output = relativeTime$1(this, !withSuffix, th, locale);\n    if (withSuffix) {\n      output = locale.pastFuture(+this, output);\n    }\n    return locale.postformat(output);\n  }\n  var abs$1 = Math.abs;\n  function sign(x) {\n    return (x > 0) - (x < 0) || +x;\n  }\n  function toISOString$1() {\n    // for ISO strings we do not use the normal bubbling rules:\n    //  * milliseconds bubble up until they become hours\n    //  * days do not bubble at all\n    //  * months bubble up until they become years\n    // This is because there is no context-free conversion between hours and days\n    // (think of clock changes)\n    // and also not between days and months (28-31 days per month)\n    if (!this.isValid()) {\n      return this.localeData().invalidDate();\n    }\n    var seconds = abs$1(this._milliseconds) / 1000,\n      days = abs$1(this._days),\n      months = abs$1(this._months),\n      minutes,\n      hours,\n      years,\n      s,\n      total = this.asSeconds(),\n      totalSign,\n      ymSign,\n      daysSign,\n      hmsSign;\n    if (!total) {\n      // this is the same as C#'s (Noda) and python (isodate)...\n      // but not other JS (goog.date)\n      return 'P0D';\n    }\n\n    // 3600 seconds -> 60 minutes -> 1 hour\n    minutes = absFloor(seconds / 60);\n    hours = absFloor(minutes / 60);\n    seconds %= 60;\n    minutes %= 60;\n\n    // 12 months -> 1 year\n    years = absFloor(months / 12);\n    months %= 12;\n\n    // inspired by https://github.com/dordille/moment-isoduration/blob/master/moment.isoduration.js\n    s = seconds ? seconds.toFixed(3).replace(/\\.?0+$/, '') : '';\n    totalSign = total < 0 ? '-' : '';\n    ymSign = sign(this._months) !== sign(total) ? '-' : '';\n    daysSign = sign(this._days) !== sign(total) ? '-' : '';\n    hmsSign = sign(this._milliseconds) !== sign(total) ? '-' : '';\n    return totalSign + 'P' + (years ? ymSign + years + 'Y' : '') + (months ? ymSign + months + 'M' : '') + (days ? daysSign + days + 'D' : '') + (hours || minutes || seconds ? 'T' : '') + (hours ? hmsSign + hours + 'H' : '') + (minutes ? hmsSign + minutes + 'M' : '') + (seconds ? hmsSign + s + 'S' : '');\n  }\n  var proto$2 = Duration.prototype;\n  proto$2.isValid = isValid$1;\n  proto$2.abs = abs;\n  proto$2.add = add$1;\n  proto$2.subtract = subtract$1;\n  proto$2.as = as;\n  proto$2.asMilliseconds = asMilliseconds;\n  proto$2.asSeconds = asSeconds;\n  proto$2.asMinutes = asMinutes;\n  proto$2.asHours = asHours;\n  proto$2.asDays = asDays;\n  proto$2.asWeeks = asWeeks;\n  proto$2.asMonths = asMonths;\n  proto$2.asQuarters = asQuarters;\n  proto$2.asYears = asYears;\n  proto$2.valueOf = valueOf$1;\n  proto$2._bubble = bubble;\n  proto$2.clone = clone$1;\n  proto$2.get = get$2;\n  proto$2.milliseconds = milliseconds;\n  proto$2.seconds = seconds;\n  proto$2.minutes = minutes;\n  proto$2.hours = hours;\n  proto$2.days = days;\n  proto$2.weeks = weeks;\n  proto$2.months = months;\n  proto$2.years = years;\n  proto$2.humanize = humanize;\n  proto$2.toISOString = toISOString$1;\n  proto$2.toString = toISOString$1;\n  proto$2.toJSON = toISOString$1;\n  proto$2.locale = locale;\n  proto$2.localeData = localeData;\n  proto$2.toIsoString = deprecate('toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)', toISOString$1);\n  proto$2.lang = lang;\n\n  // FORMATTING\n\n  addFormatToken('X', 0, 0, 'unix');\n  addFormatToken('x', 0, 0, 'valueOf');\n\n  // PARSING\n\n  addRegexToken('x', matchSigned);\n  addRegexToken('X', matchTimestamp);\n  addParseToken('X', function (input, array, config) {\n    config._d = new Date(parseFloat(input) * 1000);\n  });\n  addParseToken('x', function (input, array, config) {\n    config._d = new Date(toInt(input));\n  });\n\n  //! moment.js\n\n  hooks.version = '2.29.1';\n  setHookCallback(createLocal);\n  hooks.fn = proto;\n  hooks.min = min;\n  hooks.max = max;\n  hooks.now = now;\n  hooks.utc = createUTC;\n  hooks.unix = createUnix;\n  hooks.months = listMonths;\n  hooks.isDate = isDate;\n  hooks.locale = getSetGlobalLocale;\n  hooks.invalid = createInvalid;\n  hooks.duration = createDuration;\n  hooks.isMoment = isMoment;\n  hooks.weekdays = listWeekdays;\n  hooks.parseZone = createInZone;\n  hooks.localeData = getLocale;\n  hooks.isDuration = isDuration;\n  hooks.monthsShort = listMonthsShort;\n  hooks.weekdaysMin = listWeekdaysMin;\n  hooks.defineLocale = defineLocale;\n  hooks.updateLocale = updateLocale;\n  hooks.locales = listLocales;\n  hooks.weekdaysShort = listWeekdaysShort;\n  hooks.normalizeUnits = normalizeUnits;\n  hooks.relativeTimeRounding = getSetRelativeTimeRounding;\n  hooks.relativeTimeThreshold = getSetRelativeTimeThreshold;\n  hooks.calendarFormat = getCalendarFormat;\n  hooks.prototype = proto;\n\n  // currently HTML5 input type only supports 24-hour formats\n  hooks.HTML5_FMT = {\n    DATETIME_LOCAL: 'YYYY-MM-DDTHH:mm',\n    // <input type=\"datetime-local\" />\n    DATETIME_LOCAL_SECONDS: 'YYYY-MM-DDTHH:mm:ss',\n    // <input type=\"datetime-local\" step=\"1\" />\n    DATETIME_LOCAL_MS: 'YYYY-MM-DDTHH:mm:ss.SSS',\n    // <input type=\"datetime-local\" step=\"0.001\" />\n    DATE: 'YYYY-MM-DD',\n    // <input type=\"date\" />\n    TIME: 'HH:mm',\n    // <input type=\"time\" />\n    TIME_SECONDS: 'HH:mm:ss',\n    // <input type=\"time\" step=\"1\" />\n    TIME_MS: 'HH:mm:ss.SSS',\n    // <input type=\"time\" step=\"0.001\" />\n    WEEK: 'GGGG-[W]WW',\n    // <input type=\"week\" />\n    MONTH: 'YYYY-MM' // <input type=\"month\" />\n  };\n  return hooks;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "define", "amd", "moment", "<PERSON><PERSON><PERSON><PERSON>", "hooks", "apply", "arguments", "setHookCallback", "callback", "isArray", "input", "Array", "Object", "prototype", "toString", "call", "isObject", "hasOwnProp", "a", "b", "hasOwnProperty", "isObjectEmpty", "obj", "getOwnPropertyNames", "length", "k", "isUndefined", "isNumber", "isDate", "Date", "map", "arr", "fn", "res", "i", "push", "extend", "valueOf", "createUTC", "format", "locale", "strict", "createLocalOrUTC", "utc", "defaultParsingFlags", "empty", "unusedTokens", "unusedInput", "overflow", "charsLeftOver", "nullInput", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "invalidFormat", "userInvalidated", "iso", "parsedDateParts", "era", "meridiem", "rfc2822", "weekdayMismatch", "getParsingFlags", "m", "_pf", "some", "fun", "t", "len", "<PERSON><PERSON><PERSON><PERSON>", "_isValid", "flags", "parsedParts", "isNowValid", "isNaN", "_d", "getTime", "invalidWeekday", "_strict", "bigHour", "undefined", "isFrozen", "createInvalid", "NaN", "momentProperties", "updateInProgress", "copyConfig", "to", "from", "prop", "val", "_isAMomentObject", "_i", "_f", "_l", "_tzm", "_isUTC", "_offset", "_locale", "Moment", "config", "updateOffset", "isMoment", "warn", "msg", "suppressDeprecationWarnings", "console", "deprecate", "firstTime", "depre<PERSON><PERSON><PERSON><PERSON>", "args", "arg", "key", "slice", "join", "Error", "stack", "deprecations", "deprecateSimple", "name", "isFunction", "Function", "set", "_config", "_dayOfMonthOrdinalParseLenient", "RegExp", "_dayOfMonthOrdinalParse", "source", "_ordinalParse", "mergeConfigs", "parentConfig", "childConfig", "Locale", "keys", "defaultCalendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "calendar", "mom", "now", "output", "_calendar", "zeroFill", "number", "targetLength", "forceSign", "absNumber", "Math", "abs", "zerosToFill", "sign", "pow", "max", "substr", "formattingTokens", "localFormattingTokens", "formatFunctions", "formatTokenFunctions", "addFormatToken", "token", "padded", "ordinal", "func", "localeData", "removeFormattingTokens", "match", "replace", "makeFormatFunction", "array", "formatMoment", "invalidDate", "expandFormat", "replaceLongDateFormatTokens", "longDateFormat", "lastIndex", "test", "defaultLongDateFormat", "LTS", "LT", "L", "LL", "LLL", "LLLL", "_longDateFormat", "formatUpper", "toUpperCase", "tok", "defaultInvalidDate", "_invalidDate", "defaultOrdinal", "defaultDayOfMonthOrdinalParse", "_ordinal", "defaultRelativeTime", "future", "past", "s", "ss", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "relativeTime", "withoutSuffix", "string", "isFuture", "_relativeTime", "pastFuture", "diff", "aliases", "addUnitAlias", "unit", "shorthand", "lowerCase", "toLowerCase", "normalizeUnits", "units", "normalizeObjectUnits", "inputObject", "normalizedInput", "normalizedProp", "priorities", "addUnitPriority", "priority", "getPrioritizedUnits", "unitsObj", "u", "sort", "isLeapYear", "year", "absFloor", "ceil", "floor", "toInt", "argumentForCoercion", "coerced<PERSON>umber", "value", "isFinite", "makeGetSet", "keepTime", "set$1", "get", "month", "date", "daysInMonth", "stringGet", "stringSet", "prioritized", "match1", "match2", "match3", "match4", "match6", "match1to2", "match3to4", "match5to6", "match1to3", "match1to4", "match1to6", "matchUnsigned", "matchSigned", "matchOffset", "matchShortOffset", "matchTimestamp", "matchWord", "regexes", "addRegexToken", "regex", "strictRegex", "isStrict", "getParseRegexForToken", "unescapeFormat", "regexEscape", "matched", "p1", "p2", "p3", "p4", "tokens", "addParseToken", "addWeekParseToken", "_w", "addTimeToArrayFromToken", "_a", "YEAR", "MONTH", "DATE", "HOUR", "MINUTE", "SECOND", "MILLISECOND", "WEEK", "WEEKDAY", "mod", "n", "x", "indexOf", "o", "mod<PERSON>onth", "monthsShort", "months", "monthsShortRegex", "monthsRegex", "<PERSON><PERSON><PERSON>e", "defaultLocaleMonths", "split", "defaultLocaleMonthsShort", "MONTHS_IN_FORMAT", "defaultMonthsShortRegex", "defaultMonthsRegex", "localeMonths", "_months", "isFormat", "localeMonthsShort", "_monthsShort", "handleStrictParse", "monthName", "ii", "llc", "toLocaleLowerCase", "_monthsParse", "_longMonthsParse", "_shortMonthsParse", "localeMonthsParse", "_monthsParseExact", "setMonth", "dayOfMonth", "min", "getSetMonth", "getDaysInMonth", "computeMonthsParse", "_monthsShortStrictRegex", "_monthsShortRegex", "_monthsStrictRegex", "_monthsRegex", "cmpLenRev", "shortPieces", "long<PERSON><PERSON><PERSON>", "mixedPieces", "parseTwoDigitYear", "parseInt", "daysInYear", "getSetYear", "getIsLeapYear", "createDate", "ms", "getFullYear", "setFullYear", "createUTCDate", "UTC", "getUTCFullYear", "setUTCFullYear", "firstWeekOffset", "dow", "doy", "fwd", "fwdlw", "getUTCDay", "dayOfYearFromWeeks", "week", "weekday", "localWeekday", "weekOffset", "dayOfYear", "resYear", "resDayOfYear", "weekOfYear", "resWeek", "weeksInYear", "weekOffsetNext", "localeWeek", "_week", "defaultLocaleWeek", "localeFirstDayOfWeek", "localeFirstDayOfYear", "getSetWeek", "add", "getSetISOWeek", "weekdaysMin", "weekdaysShort", "weekdays", "weekdaysMinRegex", "weekdaysShortRegex", "weekdaysRegex", "weekdaysParse", "parseWeekday", "parseIsoWeekday", "shiftWeekdays", "ws", "concat", "defaultLocaleWeekdays", "defaultLocaleWeekdaysShort", "defaultLocaleWeekdaysMin", "defaultWeekdaysRegex", "defaultWeekdaysShortRegex", "defaultWeekdaysMinRegex", "localeWeekdays", "_weekdays", "day", "localeWeekdaysShort", "_weekdaysShort", "localeWeekdaysMin", "_weekdaysMin", "handleStrictParse$1", "weekdayName", "_weekdaysParse", "_shortWeekdaysParse", "_minWeekdaysParse", "localeWeekdaysParse", "_weekdaysParseExact", "_fullWeekdaysParse", "getSetDayOfWeek", "getDay", "getSetLocaleDayOfWeek", "getSetISODayOfWeek", "computeWeekdaysParse", "_weekdaysStrictRegex", "_weekdaysRegex", "_weekdaysShortStrictRegex", "_weekdaysShortRegex", "_weekdaysMinStrictRegex", "_weekdaysMinRegex", "min<PERSON><PERSON>ces", "minp", "shortp", "longp", "hFormat", "hours", "kFormat", "minutes", "seconds", "lowercase", "matchMeridiem", "_meridiemParse", "kInput", "_isPm", "isPM", "_meridiem", "pos", "pos1", "pos2", "localeIsPM", "char<PERSON>t", "defaultLocaleMeridiemParse", "getSetHour", "localeMeridiem", "isLower", "baseConfig", "dayOfMonthOrdinalParse", "meridiemParse", "locales", "localeFamilies", "globalLocale", "commonPrefix", "arr1", "arr2", "minl", "normalizeLocale", "chooseLocale", "names", "j", "next", "loadLocale", "oldLocale", "alias<PERSON><PERSON><PERSON><PERSON>", "_abbr", "require", "getSetGlobalLocale", "e", "values", "data", "getLocale", "defineLocale", "abbr", "parentLocale", "for<PERSON>ach", "updateLocale", "tmpLocale", "listLocales", "checkOverflow", "_overflowDayOfYear", "_overflowWeeks", "_overflowWeekday", "extendedIsoRegex", "basicIsoRegex", "tzRegex", "isoDates", "isoTimes", "aspNetJsonRegex", "obsOffsets", "UT", "GMT", "EDT", "EST", "CDT", "CST", "MDT", "MST", "PDT", "PST", "configFromISO", "l", "exec", "allowTime", "dateFormat", "timeFormat", "tzFormat", "configFromStringAndFormat", "extractFromRFC2822Strings", "yearStr", "monthStr", "dayStr", "hourStr", "minuteStr", "secondStr", "result", "untruncateYear", "preprocessRFC2822", "checkWeekday", "weekdayStr", "parsedInput", "weekdayProvided", "weekdayActual", "calculateOffset", "obsOffset", "militaryOffset", "numOffset", "hm", "configFromRFC2822", "parsed<PERSON><PERSON><PERSON>", "setUTCMinutes", "getUTCMinutes", "configFromString", "createFromInputFallback", "_useUTC", "defaults", "c", "currentDateArray", "nowValue", "getUTCMonth", "getUTCDate", "getMonth", "getDate", "config<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentDate", "expectedWeekday", "yearToUse", "dayOfYearFromWeekInfo", "_dayOfYear", "_nextDay", "weekYear", "temp", "weekdayOverflow", "curWeek", "GG", "W", "E", "createLocal", "gg", "ISO_8601", "RFC_2822", "skipped", "stringLength", "totalParsedInputLength", "meridiemFixWrap", "erasConvertYear", "hour", "isPm", "meridiemHour", "configFromStringAndArray", "tempConfig", "bestMoment", "scoreToBeat", "currentScore", "validFormatFound", "bestFormatIsValid", "score", "configFromObject", "dayOrDate", "minute", "second", "millisecond", "createFromConfig", "prepareConfig", "preparse", "configFromInput", "isUTC", "prototypeMin", "other", "prototypeMax", "pickBy", "moments", "ordering", "isDurationValid", "unitHasDecimal", "parseFloat", "isValid$1", "createInvalid$1", "createDuration", "Duration", "duration", "years", "quarters", "quarter", "weeks", "isoWeek", "days", "milliseconds", "_milliseconds", "_days", "_data", "_bubble", "isDuration", "absRound", "round", "compareArrays", "array1", "array2", "dont<PERSON><PERSON><PERSON>", "lengthDiff", "diffs", "offset", "separator", "utcOffset", "offsetFromString", "chunkOffset", "matcher", "matches", "chunk", "parts", "cloneWithOffset", "model", "clone", "setTime", "local", "getDateOffset", "getTimezoneOffset", "getSetOffset", "keepLocalTime", "keepMinutes", "localAdjust", "_changeInProgress", "addSubtract", "getSetZone", "setOffsetToUTC", "setOffsetToLocal", "subtract", "setOffsetToParsedOffset", "tZone", "hasAlignedHourOffset", "isDaylightSavingTime", "isDaylightSavingTimeShifted", "_isDSTShifted", "toArray", "isLocal", "isUtcOffset", "isUtc", "aspNetRegex", "isoRegex", "ret", "diffRes", "parseIso", "momentsDifference", "invalid", "inp", "positiveMomentsDifference", "base", "isAfter", "isBefore", "createAdder", "direction", "period", "dur", "tmp", "isAdding", "isString", "String", "isMomentInput", "isNumberOrStringArray", "isMomentInputObject", "objectTest", "propertyTest", "properties", "property", "arrayTest", "dataTypeTest", "filter", "item", "isCalendarSpec", "getCalendarFormat", "myMoment", "calendar$1", "time", "formats", "sod", "startOf", "calendarFormat", "localInput", "endOf", "isBetween", "inclusivity", "localFrom", "localTo", "isSame", "inputMs", "isSameOrAfter", "isSameOrBefore", "asFloat", "that", "zoneDelta", "monthDiff", "wholeMonthDiff", "anchor", "anchor2", "adjust", "defaultFormat", "defaultFormatUtc", "toISOString", "keepOffset", "toDate", "inspect", "zone", "prefix", "datetime", "suffix", "inputString", "postformat", "humanize", "fromNow", "toNow", "newLocaleData", "lang", "MS_PER_SECOND", "MS_PER_MINUTE", "MS_PER_HOUR", "MS_PER_400_YEARS", "mod$1", "dividend", "divisor", "localStartOfDate", "utcStartOfDate", "startOfDate", "isoWeekday", "unix", "toObject", "toJSON", "isValid$2", "parsingFlags", "invalidAt", "creationData", "matchEraAbbr", "matchEraName", "matchEra<PERSON><PERSON>row", "erasParse", "matchEraYearOrdinal", "_eraYearOrdinalRegex", "eraYearOrdinalParse", "localeEras", "eras", "_eras", "since", "until", "Infinity", "localeErasParse", "eraName", "narrow", "localeErasConvertYear", "dir", "getEraName", "get<PERSON>ra<PERSON><PERSON><PERSON>", "getEraAbbr", "getEraYear", "erasNameRegex", "computeErasParse", "_erasNameRegex", "_erasRegex", "erasAbbrRegex", "_erasAbbrRegex", "erasNarrowRegex", "_erasNarrowRegex", "abbr<PERSON><PERSON><PERSON>", "namePieces", "narrowPieces", "isoWeekYear", "addWeekYearFormatToken", "getter", "getSetWeekYear", "getSetWeekYearHelper", "getSetISOWeekYear", "getISOWeeksInYear", "getISOWeeksInISOWeekYear", "getWeeksInYear", "weekInfo", "getWeeksInWeekYear", "<PERSON><PERSON><PERSON><PERSON>", "setWeekAll", "dayOfYearData", "getSetQuarter", "getSetDayOfMonth", "getSetDayOfYear", "getSetMinute", "getSetSecond", "getSetMillisecond", "parseMs", "getZoneAbbr", "getZoneName", "proto", "Symbol", "for", "<PERSON><PERSON><PERSON><PERSON>", "eraAbbr", "eraYear", "isoWeeks", "weeksInWeekYear", "isoWeeksInYear", "isoWeeksInISOWeekYear", "parseZone", "isDST", "zoneAbbr", "zoneName", "dates", "isDSTShifted", "createUnix", "createInZone", "preParsePostFormat", "proto$1", "firstDayOfYear", "firstDayOfWeek", "get$1", "index", "field", "setter", "listMonthsImpl", "out", "listWeekdaysImpl", "localeSorted", "shift", "listMonths", "listMonthsShort", "listWeekdays", "listWeekdaysShort", "listWeekdaysMin", "langData", "mathAbs", "addSubtract$1", "add$1", "subtract$1", "absCeil", "bubble", "monthsFromDays", "monthsToDays", "daysToMonths", "as", "valueOf$1", "makeAs", "alias", "asMilliseconds", "asSeconds", "asMinutes", "asHours", "asDays", "asWeeks", "asMonths", "asQuarters", "as<PERSON><PERSON>s", "clone$1", "get$2", "makeGetter", "thresholds", "substituteTimeAgo", "relativeTime$1", "posNegDuration", "getSetRelativeTimeRounding", "roundingFunction", "getSetRelativeTimeThreshold", "threshold", "limit", "argWithSuffix", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "withSuffix", "th", "assign", "abs$1", "toISOString$1", "total", "totalSign", "ymSign", "daysSign", "hmsSign", "toFixed", "proto$2", "toIsoString", "version", "relativeTimeRounding", "relativeTimeThreshold", "HTML5_FMT", "DATETIME_LOCAL", "DATETIME_LOCAL_SECONDS", "DATETIME_LOCAL_MS", "TIME", "TIME_SECONDS", "TIME_MS"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/moment/moment.js"], "sourcesContent": ["//! moment.js\n//! version : 2.29.1\n//! authors : <PERSON>, <PERSON><PERSON><PERSON>, Moment.js contributors\n//! license : MIT\n//! momentjs.com\n\n;(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n    typeof define === 'function' && define.amd ? define(factory) :\n    global.moment = factory()\n}(this, (function () { 'use strict';\n\n    var hookCallback;\n\n    function hooks() {\n        return hookCallback.apply(null, arguments);\n    }\n\n    // This is done to register the method called with moment()\n    // without creating circular dependencies.\n    function setHookCallback(callback) {\n        hookCallback = callback;\n    }\n\n    function isArray(input) {\n        return (\n            input instanceof Array ||\n            Object.prototype.toString.call(input) === '[object Array]'\n        );\n    }\n\n    function isObject(input) {\n        // IE8 will treat undefined and null as object if it wasn't for\n        // input != null\n        return (\n            input != null &&\n            Object.prototype.toString.call(input) === '[object Object]'\n        );\n    }\n\n    function hasOwnProp(a, b) {\n        return Object.prototype.hasOwnProperty.call(a, b);\n    }\n\n    function isObjectEmpty(obj) {\n        if (Object.getOwnPropertyNames) {\n            return Object.getOwnPropertyNames(obj).length === 0;\n        } else {\n            var k;\n            for (k in obj) {\n                if (hasOwnProp(obj, k)) {\n                    return false;\n                }\n            }\n            return true;\n        }\n    }\n\n    function isUndefined(input) {\n        return input === void 0;\n    }\n\n    function isNumber(input) {\n        return (\n            typeof input === 'number' ||\n            Object.prototype.toString.call(input) === '[object Number]'\n        );\n    }\n\n    function isDate(input) {\n        return (\n            input instanceof Date ||\n            Object.prototype.toString.call(input) === '[object Date]'\n        );\n    }\n\n    function map(arr, fn) {\n        var res = [],\n            i;\n        for (i = 0; i < arr.length; ++i) {\n            res.push(fn(arr[i], i));\n        }\n        return res;\n    }\n\n    function extend(a, b) {\n        for (var i in b) {\n            if (hasOwnProp(b, i)) {\n                a[i] = b[i];\n            }\n        }\n\n        if (hasOwnProp(b, 'toString')) {\n            a.toString = b.toString;\n        }\n\n        if (hasOwnProp(b, 'valueOf')) {\n            a.valueOf = b.valueOf;\n        }\n\n        return a;\n    }\n\n    function createUTC(input, format, locale, strict) {\n        return createLocalOrUTC(input, format, locale, strict, true).utc();\n    }\n\n    function defaultParsingFlags() {\n        // We need to deep clone this object.\n        return {\n            empty: false,\n            unusedTokens: [],\n            unusedInput: [],\n            overflow: -2,\n            charsLeftOver: 0,\n            nullInput: false,\n            invalidEra: null,\n            invalidMonth: null,\n            invalidFormat: false,\n            userInvalidated: false,\n            iso: false,\n            parsedDateParts: [],\n            era: null,\n            meridiem: null,\n            rfc2822: false,\n            weekdayMismatch: false,\n        };\n    }\n\n    function getParsingFlags(m) {\n        if (m._pf == null) {\n            m._pf = defaultParsingFlags();\n        }\n        return m._pf;\n    }\n\n    var some;\n    if (Array.prototype.some) {\n        some = Array.prototype.some;\n    } else {\n        some = function (fun) {\n            var t = Object(this),\n                len = t.length >>> 0,\n                i;\n\n            for (i = 0; i < len; i++) {\n                if (i in t && fun.call(this, t[i], i, t)) {\n                    return true;\n                }\n            }\n\n            return false;\n        };\n    }\n\n    function isValid(m) {\n        if (m._isValid == null) {\n            var flags = getParsingFlags(m),\n                parsedParts = some.call(flags.parsedDateParts, function (i) {\n                    return i != null;\n                }),\n                isNowValid =\n                    !isNaN(m._d.getTime()) &&\n                    flags.overflow < 0 &&\n                    !flags.empty &&\n                    !flags.invalidEra &&\n                    !flags.invalidMonth &&\n                    !flags.invalidWeekday &&\n                    !flags.weekdayMismatch &&\n                    !flags.nullInput &&\n                    !flags.invalidFormat &&\n                    !flags.userInvalidated &&\n                    (!flags.meridiem || (flags.meridiem && parsedParts));\n\n            if (m._strict) {\n                isNowValid =\n                    isNowValid &&\n                    flags.charsLeftOver === 0 &&\n                    flags.unusedTokens.length === 0 &&\n                    flags.bigHour === undefined;\n            }\n\n            if (Object.isFrozen == null || !Object.isFrozen(m)) {\n                m._isValid = isNowValid;\n            } else {\n                return isNowValid;\n            }\n        }\n        return m._isValid;\n    }\n\n    function createInvalid(flags) {\n        var m = createUTC(NaN);\n        if (flags != null) {\n            extend(getParsingFlags(m), flags);\n        } else {\n            getParsingFlags(m).userInvalidated = true;\n        }\n\n        return m;\n    }\n\n    // Plugins that add properties should also add the key here (null value),\n    // so we can properly clone ourselves.\n    var momentProperties = (hooks.momentProperties = []),\n        updateInProgress = false;\n\n    function copyConfig(to, from) {\n        var i, prop, val;\n\n        if (!isUndefined(from._isAMomentObject)) {\n            to._isAMomentObject = from._isAMomentObject;\n        }\n        if (!isUndefined(from._i)) {\n            to._i = from._i;\n        }\n        if (!isUndefined(from._f)) {\n            to._f = from._f;\n        }\n        if (!isUndefined(from._l)) {\n            to._l = from._l;\n        }\n        if (!isUndefined(from._strict)) {\n            to._strict = from._strict;\n        }\n        if (!isUndefined(from._tzm)) {\n            to._tzm = from._tzm;\n        }\n        if (!isUndefined(from._isUTC)) {\n            to._isUTC = from._isUTC;\n        }\n        if (!isUndefined(from._offset)) {\n            to._offset = from._offset;\n        }\n        if (!isUndefined(from._pf)) {\n            to._pf = getParsingFlags(from);\n        }\n        if (!isUndefined(from._locale)) {\n            to._locale = from._locale;\n        }\n\n        if (momentProperties.length > 0) {\n            for (i = 0; i < momentProperties.length; i++) {\n                prop = momentProperties[i];\n                val = from[prop];\n                if (!isUndefined(val)) {\n                    to[prop] = val;\n                }\n            }\n        }\n\n        return to;\n    }\n\n    // Moment prototype object\n    function Moment(config) {\n        copyConfig(this, config);\n        this._d = new Date(config._d != null ? config._d.getTime() : NaN);\n        if (!this.isValid()) {\n            this._d = new Date(NaN);\n        }\n        // Prevent infinite loop in case updateOffset creates new moment\n        // objects.\n        if (updateInProgress === false) {\n            updateInProgress = true;\n            hooks.updateOffset(this);\n            updateInProgress = false;\n        }\n    }\n\n    function isMoment(obj) {\n        return (\n            obj instanceof Moment || (obj != null && obj._isAMomentObject != null)\n        );\n    }\n\n    function warn(msg) {\n        if (\n            hooks.suppressDeprecationWarnings === false &&\n            typeof console !== 'undefined' &&\n            console.warn\n        ) {\n            console.warn('Deprecation warning: ' + msg);\n        }\n    }\n\n    function deprecate(msg, fn) {\n        var firstTime = true;\n\n        return extend(function () {\n            if (hooks.deprecationHandler != null) {\n                hooks.deprecationHandler(null, msg);\n            }\n            if (firstTime) {\n                var args = [],\n                    arg,\n                    i,\n                    key;\n                for (i = 0; i < arguments.length; i++) {\n                    arg = '';\n                    if (typeof arguments[i] === 'object') {\n                        arg += '\\n[' + i + '] ';\n                        for (key in arguments[0]) {\n                            if (hasOwnProp(arguments[0], key)) {\n                                arg += key + ': ' + arguments[0][key] + ', ';\n                            }\n                        }\n                        arg = arg.slice(0, -2); // Remove trailing comma and space\n                    } else {\n                        arg = arguments[i];\n                    }\n                    args.push(arg);\n                }\n                warn(\n                    msg +\n                        '\\nArguments: ' +\n                        Array.prototype.slice.call(args).join('') +\n                        '\\n' +\n                        new Error().stack\n                );\n                firstTime = false;\n            }\n            return fn.apply(this, arguments);\n        }, fn);\n    }\n\n    var deprecations = {};\n\n    function deprecateSimple(name, msg) {\n        if (hooks.deprecationHandler != null) {\n            hooks.deprecationHandler(name, msg);\n        }\n        if (!deprecations[name]) {\n            warn(msg);\n            deprecations[name] = true;\n        }\n    }\n\n    hooks.suppressDeprecationWarnings = false;\n    hooks.deprecationHandler = null;\n\n    function isFunction(input) {\n        return (\n            (typeof Function !== 'undefined' && input instanceof Function) ||\n            Object.prototype.toString.call(input) === '[object Function]'\n        );\n    }\n\n    function set(config) {\n        var prop, i;\n        for (i in config) {\n            if (hasOwnProp(config, i)) {\n                prop = config[i];\n                if (isFunction(prop)) {\n                    this[i] = prop;\n                } else {\n                    this['_' + i] = prop;\n                }\n            }\n        }\n        this._config = config;\n        // Lenient ordinal parsing accepts just a number in addition to\n        // number + (possibly) stuff coming from _dayOfMonthOrdinalParse.\n        // TODO: Remove \"ordinalParse\" fallback in next major release.\n        this._dayOfMonthOrdinalParseLenient = new RegExp(\n            (this._dayOfMonthOrdinalParse.source || this._ordinalParse.source) +\n                '|' +\n                /\\d{1,2}/.source\n        );\n    }\n\n    function mergeConfigs(parentConfig, childConfig) {\n        var res = extend({}, parentConfig),\n            prop;\n        for (prop in childConfig) {\n            if (hasOwnProp(childConfig, prop)) {\n                if (isObject(parentConfig[prop]) && isObject(childConfig[prop])) {\n                    res[prop] = {};\n                    extend(res[prop], parentConfig[prop]);\n                    extend(res[prop], childConfig[prop]);\n                } else if (childConfig[prop] != null) {\n                    res[prop] = childConfig[prop];\n                } else {\n                    delete res[prop];\n                }\n            }\n        }\n        for (prop in parentConfig) {\n            if (\n                hasOwnProp(parentConfig, prop) &&\n                !hasOwnProp(childConfig, prop) &&\n                isObject(parentConfig[prop])\n            ) {\n                // make sure changes to properties don't modify parent config\n                res[prop] = extend({}, res[prop]);\n            }\n        }\n        return res;\n    }\n\n    function Locale(config) {\n        if (config != null) {\n            this.set(config);\n        }\n    }\n\n    var keys;\n\n    if (Object.keys) {\n        keys = Object.keys;\n    } else {\n        keys = function (obj) {\n            var i,\n                res = [];\n            for (i in obj) {\n                if (hasOwnProp(obj, i)) {\n                    res.push(i);\n                }\n            }\n            return res;\n        };\n    }\n\n    var defaultCalendar = {\n        sameDay: '[Today at] LT',\n        nextDay: '[Tomorrow at] LT',\n        nextWeek: 'dddd [at] LT',\n        lastDay: '[Yesterday at] LT',\n        lastWeek: '[Last] dddd [at] LT',\n        sameElse: 'L',\n    };\n\n    function calendar(key, mom, now) {\n        var output = this._calendar[key] || this._calendar['sameElse'];\n        return isFunction(output) ? output.call(mom, now) : output;\n    }\n\n    function zeroFill(number, targetLength, forceSign) {\n        var absNumber = '' + Math.abs(number),\n            zerosToFill = targetLength - absNumber.length,\n            sign = number >= 0;\n        return (\n            (sign ? (forceSign ? '+' : '') : '-') +\n            Math.pow(10, Math.max(0, zerosToFill)).toString().substr(1) +\n            absNumber\n        );\n    }\n\n    var formattingTokens = /(\\[[^\\[]*\\])|(\\\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,\n        localFormattingTokens = /(\\[[^\\[]*\\])|(\\\\)?(LTS|LT|LL?L?L?|l{1,4})/g,\n        formatFunctions = {},\n        formatTokenFunctions = {};\n\n    // token:    'M'\n    // padded:   ['MM', 2]\n    // ordinal:  'Mo'\n    // callback: function () { this.month() + 1 }\n    function addFormatToken(token, padded, ordinal, callback) {\n        var func = callback;\n        if (typeof callback === 'string') {\n            func = function () {\n                return this[callback]();\n            };\n        }\n        if (token) {\n            formatTokenFunctions[token] = func;\n        }\n        if (padded) {\n            formatTokenFunctions[padded[0]] = function () {\n                return zeroFill(func.apply(this, arguments), padded[1], padded[2]);\n            };\n        }\n        if (ordinal) {\n            formatTokenFunctions[ordinal] = function () {\n                return this.localeData().ordinal(\n                    func.apply(this, arguments),\n                    token\n                );\n            };\n        }\n    }\n\n    function removeFormattingTokens(input) {\n        if (input.match(/\\[[\\s\\S]/)) {\n            return input.replace(/^\\[|\\]$/g, '');\n        }\n        return input.replace(/\\\\/g, '');\n    }\n\n    function makeFormatFunction(format) {\n        var array = format.match(formattingTokens),\n            i,\n            length;\n\n        for (i = 0, length = array.length; i < length; i++) {\n            if (formatTokenFunctions[array[i]]) {\n                array[i] = formatTokenFunctions[array[i]];\n            } else {\n                array[i] = removeFormattingTokens(array[i]);\n            }\n        }\n\n        return function (mom) {\n            var output = '',\n                i;\n            for (i = 0; i < length; i++) {\n                output += isFunction(array[i])\n                    ? array[i].call(mom, format)\n                    : array[i];\n            }\n            return output;\n        };\n    }\n\n    // format date using native date object\n    function formatMoment(m, format) {\n        if (!m.isValid()) {\n            return m.localeData().invalidDate();\n        }\n\n        format = expandFormat(format, m.localeData());\n        formatFunctions[format] =\n            formatFunctions[format] || makeFormatFunction(format);\n\n        return formatFunctions[format](m);\n    }\n\n    function expandFormat(format, locale) {\n        var i = 5;\n\n        function replaceLongDateFormatTokens(input) {\n            return locale.longDateFormat(input) || input;\n        }\n\n        localFormattingTokens.lastIndex = 0;\n        while (i >= 0 && localFormattingTokens.test(format)) {\n            format = format.replace(\n                localFormattingTokens,\n                replaceLongDateFormatTokens\n            );\n            localFormattingTokens.lastIndex = 0;\n            i -= 1;\n        }\n\n        return format;\n    }\n\n    var defaultLongDateFormat = {\n        LTS: 'h:mm:ss A',\n        LT: 'h:mm A',\n        L: 'MM/DD/YYYY',\n        LL: 'MMMM D, YYYY',\n        LLL: 'MMMM D, YYYY h:mm A',\n        LLLL: 'dddd, MMMM D, YYYY h:mm A',\n    };\n\n    function longDateFormat(key) {\n        var format = this._longDateFormat[key],\n            formatUpper = this._longDateFormat[key.toUpperCase()];\n\n        if (format || !formatUpper) {\n            return format;\n        }\n\n        this._longDateFormat[key] = formatUpper\n            .match(formattingTokens)\n            .map(function (tok) {\n                if (\n                    tok === 'MMMM' ||\n                    tok === 'MM' ||\n                    tok === 'DD' ||\n                    tok === 'dddd'\n                ) {\n                    return tok.slice(1);\n                }\n                return tok;\n            })\n            .join('');\n\n        return this._longDateFormat[key];\n    }\n\n    var defaultInvalidDate = 'Invalid date';\n\n    function invalidDate() {\n        return this._invalidDate;\n    }\n\n    var defaultOrdinal = '%d',\n        defaultDayOfMonthOrdinalParse = /\\d{1,2}/;\n\n    function ordinal(number) {\n        return this._ordinal.replace('%d', number);\n    }\n\n    var defaultRelativeTime = {\n        future: 'in %s',\n        past: '%s ago',\n        s: 'a few seconds',\n        ss: '%d seconds',\n        m: 'a minute',\n        mm: '%d minutes',\n        h: 'an hour',\n        hh: '%d hours',\n        d: 'a day',\n        dd: '%d days',\n        w: 'a week',\n        ww: '%d weeks',\n        M: 'a month',\n        MM: '%d months',\n        y: 'a year',\n        yy: '%d years',\n    };\n\n    function relativeTime(number, withoutSuffix, string, isFuture) {\n        var output = this._relativeTime[string];\n        return isFunction(output)\n            ? output(number, withoutSuffix, string, isFuture)\n            : output.replace(/%d/i, number);\n    }\n\n    function pastFuture(diff, output) {\n        var format = this._relativeTime[diff > 0 ? 'future' : 'past'];\n        return isFunction(format) ? format(output) : format.replace(/%s/i, output);\n    }\n\n    var aliases = {};\n\n    function addUnitAlias(unit, shorthand) {\n        var lowerCase = unit.toLowerCase();\n        aliases[lowerCase] = aliases[lowerCase + 's'] = aliases[shorthand] = unit;\n    }\n\n    function normalizeUnits(units) {\n        return typeof units === 'string'\n            ? aliases[units] || aliases[units.toLowerCase()]\n            : undefined;\n    }\n\n    function normalizeObjectUnits(inputObject) {\n        var normalizedInput = {},\n            normalizedProp,\n            prop;\n\n        for (prop in inputObject) {\n            if (hasOwnProp(inputObject, prop)) {\n                normalizedProp = normalizeUnits(prop);\n                if (normalizedProp) {\n                    normalizedInput[normalizedProp] = inputObject[prop];\n                }\n            }\n        }\n\n        return normalizedInput;\n    }\n\n    var priorities = {};\n\n    function addUnitPriority(unit, priority) {\n        priorities[unit] = priority;\n    }\n\n    function getPrioritizedUnits(unitsObj) {\n        var units = [],\n            u;\n        for (u in unitsObj) {\n            if (hasOwnProp(unitsObj, u)) {\n                units.push({ unit: u, priority: priorities[u] });\n            }\n        }\n        units.sort(function (a, b) {\n            return a.priority - b.priority;\n        });\n        return units;\n    }\n\n    function isLeapYear(year) {\n        return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;\n    }\n\n    function absFloor(number) {\n        if (number < 0) {\n            // -0 -> 0\n            return Math.ceil(number) || 0;\n        } else {\n            return Math.floor(number);\n        }\n    }\n\n    function toInt(argumentForCoercion) {\n        var coercedNumber = +argumentForCoercion,\n            value = 0;\n\n        if (coercedNumber !== 0 && isFinite(coercedNumber)) {\n            value = absFloor(coercedNumber);\n        }\n\n        return value;\n    }\n\n    function makeGetSet(unit, keepTime) {\n        return function (value) {\n            if (value != null) {\n                set$1(this, unit, value);\n                hooks.updateOffset(this, keepTime);\n                return this;\n            } else {\n                return get(this, unit);\n            }\n        };\n    }\n\n    function get(mom, unit) {\n        return mom.isValid()\n            ? mom._d['get' + (mom._isUTC ? 'UTC' : '') + unit]()\n            : NaN;\n    }\n\n    function set$1(mom, unit, value) {\n        if (mom.isValid() && !isNaN(value)) {\n            if (\n                unit === 'FullYear' &&\n                isLeapYear(mom.year()) &&\n                mom.month() === 1 &&\n                mom.date() === 29\n            ) {\n                value = toInt(value);\n                mom._d['set' + (mom._isUTC ? 'UTC' : '') + unit](\n                    value,\n                    mom.month(),\n                    daysInMonth(value, mom.month())\n                );\n            } else {\n                mom._d['set' + (mom._isUTC ? 'UTC' : '') + unit](value);\n            }\n        }\n    }\n\n    // MOMENTS\n\n    function stringGet(units) {\n        units = normalizeUnits(units);\n        if (isFunction(this[units])) {\n            return this[units]();\n        }\n        return this;\n    }\n\n    function stringSet(units, value) {\n        if (typeof units === 'object') {\n            units = normalizeObjectUnits(units);\n            var prioritized = getPrioritizedUnits(units),\n                i;\n            for (i = 0; i < prioritized.length; i++) {\n                this[prioritized[i].unit](units[prioritized[i].unit]);\n            }\n        } else {\n            units = normalizeUnits(units);\n            if (isFunction(this[units])) {\n                return this[units](value);\n            }\n        }\n        return this;\n    }\n\n    var match1 = /\\d/, //       0 - 9\n        match2 = /\\d\\d/, //      00 - 99\n        match3 = /\\d{3}/, //     000 - 999\n        match4 = /\\d{4}/, //    0000 - 9999\n        match6 = /[+-]?\\d{6}/, // -999999 - 999999\n        match1to2 = /\\d\\d?/, //       0 - 99\n        match3to4 = /\\d\\d\\d\\d?/, //     999 - 9999\n        match5to6 = /\\d\\d\\d\\d\\d\\d?/, //   99999 - 999999\n        match1to3 = /\\d{1,3}/, //       0 - 999\n        match1to4 = /\\d{1,4}/, //       0 - 9999\n        match1to6 = /[+-]?\\d{1,6}/, // -999999 - 999999\n        matchUnsigned = /\\d+/, //       0 - inf\n        matchSigned = /[+-]?\\d+/, //    -inf - inf\n        matchOffset = /Z|[+-]\\d\\d:?\\d\\d/gi, // +00:00 -00:00 +0000 -0000 or Z\n        matchShortOffset = /Z|[+-]\\d\\d(?::?\\d\\d)?/gi, // +00 -00 +00:00 -00:00 +0000 -0000 or Z\n        matchTimestamp = /[+-]?\\d+(\\.\\d{1,3})?/, // 123456789 123456789.123\n        // any word (or two) characters or numbers including two/three word month in arabic.\n        // includes scottish gaelic two word and hyphenated months\n        matchWord = /[0-9]{0,256}['a-z\\u00A0-\\u05FF\\u0700-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFF07\\uFF10-\\uFFEF]{1,256}|[\\u0600-\\u06FF\\/]{1,256}(\\s*?[\\u0600-\\u06FF]{1,256}){1,2}/i,\n        regexes;\n\n    regexes = {};\n\n    function addRegexToken(token, regex, strictRegex) {\n        regexes[token] = isFunction(regex)\n            ? regex\n            : function (isStrict, localeData) {\n                  return isStrict && strictRegex ? strictRegex : regex;\n              };\n    }\n\n    function getParseRegexForToken(token, config) {\n        if (!hasOwnProp(regexes, token)) {\n            return new RegExp(unescapeFormat(token));\n        }\n\n        return regexes[token](config._strict, config._locale);\n    }\n\n    // Code from http://stackoverflow.com/questions/3561493/is-there-a-regexp-escape-function-in-javascript\n    function unescapeFormat(s) {\n        return regexEscape(\n            s\n                .replace('\\\\', '')\n                .replace(/\\\\(\\[)|\\\\(\\])|\\[([^\\]\\[]*)\\]|\\\\(.)/g, function (\n                    matched,\n                    p1,\n                    p2,\n                    p3,\n                    p4\n                ) {\n                    return p1 || p2 || p3 || p4;\n                })\n        );\n    }\n\n    function regexEscape(s) {\n        return s.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n    }\n\n    var tokens = {};\n\n    function addParseToken(token, callback) {\n        var i,\n            func = callback;\n        if (typeof token === 'string') {\n            token = [token];\n        }\n        if (isNumber(callback)) {\n            func = function (input, array) {\n                array[callback] = toInt(input);\n            };\n        }\n        for (i = 0; i < token.length; i++) {\n            tokens[token[i]] = func;\n        }\n    }\n\n    function addWeekParseToken(token, callback) {\n        addParseToken(token, function (input, array, config, token) {\n            config._w = config._w || {};\n            callback(input, config._w, config, token);\n        });\n    }\n\n    function addTimeToArrayFromToken(token, input, config) {\n        if (input != null && hasOwnProp(tokens, token)) {\n            tokens[token](input, config._a, config, token);\n        }\n    }\n\n    var YEAR = 0,\n        MONTH = 1,\n        DATE = 2,\n        HOUR = 3,\n        MINUTE = 4,\n        SECOND = 5,\n        MILLISECOND = 6,\n        WEEK = 7,\n        WEEKDAY = 8;\n\n    function mod(n, x) {\n        return ((n % x) + x) % x;\n    }\n\n    var indexOf;\n\n    if (Array.prototype.indexOf) {\n        indexOf = Array.prototype.indexOf;\n    } else {\n        indexOf = function (o) {\n            // I know\n            var i;\n            for (i = 0; i < this.length; ++i) {\n                if (this[i] === o) {\n                    return i;\n                }\n            }\n            return -1;\n        };\n    }\n\n    function daysInMonth(year, month) {\n        if (isNaN(year) || isNaN(month)) {\n            return NaN;\n        }\n        var modMonth = mod(month, 12);\n        year += (month - modMonth) / 12;\n        return modMonth === 1\n            ? isLeapYear(year)\n                ? 29\n                : 28\n            : 31 - ((modMonth % 7) % 2);\n    }\n\n    // FORMATTING\n\n    addFormatToken('M', ['MM', 2], 'Mo', function () {\n        return this.month() + 1;\n    });\n\n    addFormatToken('MMM', 0, 0, function (format) {\n        return this.localeData().monthsShort(this, format);\n    });\n\n    addFormatToken('MMMM', 0, 0, function (format) {\n        return this.localeData().months(this, format);\n    });\n\n    // ALIASES\n\n    addUnitAlias('month', 'M');\n\n    // PRIORITY\n\n    addUnitPriority('month', 8);\n\n    // PARSING\n\n    addRegexToken('M', match1to2);\n    addRegexToken('MM', match1to2, match2);\n    addRegexToken('MMM', function (isStrict, locale) {\n        return locale.monthsShortRegex(isStrict);\n    });\n    addRegexToken('MMMM', function (isStrict, locale) {\n        return locale.monthsRegex(isStrict);\n    });\n\n    addParseToken(['M', 'MM'], function (input, array) {\n        array[MONTH] = toInt(input) - 1;\n    });\n\n    addParseToken(['MMM', 'MMMM'], function (input, array, config, token) {\n        var month = config._locale.monthsParse(input, token, config._strict);\n        // if we didn't find a month name, mark the date as invalid.\n        if (month != null) {\n            array[MONTH] = month;\n        } else {\n            getParsingFlags(config).invalidMonth = input;\n        }\n    });\n\n    // LOCALES\n\n    var defaultLocaleMonths = 'January_February_March_April_May_June_July_August_September_October_November_December'.split(\n            '_'\n        ),\n        defaultLocaleMonthsShort = 'Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec'.split(\n            '_'\n        ),\n        MONTHS_IN_FORMAT = /D[oD]?(\\[[^\\[\\]]*\\]|\\s)+MMMM?/,\n        defaultMonthsShortRegex = matchWord,\n        defaultMonthsRegex = matchWord;\n\n    function localeMonths(m, format) {\n        if (!m) {\n            return isArray(this._months)\n                ? this._months\n                : this._months['standalone'];\n        }\n        return isArray(this._months)\n            ? this._months[m.month()]\n            : this._months[\n                  (this._months.isFormat || MONTHS_IN_FORMAT).test(format)\n                      ? 'format'\n                      : 'standalone'\n              ][m.month()];\n    }\n\n    function localeMonthsShort(m, format) {\n        if (!m) {\n            return isArray(this._monthsShort)\n                ? this._monthsShort\n                : this._monthsShort['standalone'];\n        }\n        return isArray(this._monthsShort)\n            ? this._monthsShort[m.month()]\n            : this._monthsShort[\n                  MONTHS_IN_FORMAT.test(format) ? 'format' : 'standalone'\n              ][m.month()];\n    }\n\n    function handleStrictParse(monthName, format, strict) {\n        var i,\n            ii,\n            mom,\n            llc = monthName.toLocaleLowerCase();\n        if (!this._monthsParse) {\n            // this is not used\n            this._monthsParse = [];\n            this._longMonthsParse = [];\n            this._shortMonthsParse = [];\n            for (i = 0; i < 12; ++i) {\n                mom = createUTC([2000, i]);\n                this._shortMonthsParse[i] = this.monthsShort(\n                    mom,\n                    ''\n                ).toLocaleLowerCase();\n                this._longMonthsParse[i] = this.months(mom, '').toLocaleLowerCase();\n            }\n        }\n\n        if (strict) {\n            if (format === 'MMM') {\n                ii = indexOf.call(this._shortMonthsParse, llc);\n                return ii !== -1 ? ii : null;\n            } else {\n                ii = indexOf.call(this._longMonthsParse, llc);\n                return ii !== -1 ? ii : null;\n            }\n        } else {\n            if (format === 'MMM') {\n                ii = indexOf.call(this._shortMonthsParse, llc);\n                if (ii !== -1) {\n                    return ii;\n                }\n                ii = indexOf.call(this._longMonthsParse, llc);\n                return ii !== -1 ? ii : null;\n            } else {\n                ii = indexOf.call(this._longMonthsParse, llc);\n                if (ii !== -1) {\n                    return ii;\n                }\n                ii = indexOf.call(this._shortMonthsParse, llc);\n                return ii !== -1 ? ii : null;\n            }\n        }\n    }\n\n    function localeMonthsParse(monthName, format, strict) {\n        var i, mom, regex;\n\n        if (this._monthsParseExact) {\n            return handleStrictParse.call(this, monthName, format, strict);\n        }\n\n        if (!this._monthsParse) {\n            this._monthsParse = [];\n            this._longMonthsParse = [];\n            this._shortMonthsParse = [];\n        }\n\n        // TODO: add sorting\n        // Sorting makes sure if one month (or abbr) is a prefix of another\n        // see sorting in computeMonthsParse\n        for (i = 0; i < 12; i++) {\n            // make the regex if we don't have it already\n            mom = createUTC([2000, i]);\n            if (strict && !this._longMonthsParse[i]) {\n                this._longMonthsParse[i] = new RegExp(\n                    '^' + this.months(mom, '').replace('.', '') + '$',\n                    'i'\n                );\n                this._shortMonthsParse[i] = new RegExp(\n                    '^' + this.monthsShort(mom, '').replace('.', '') + '$',\n                    'i'\n                );\n            }\n            if (!strict && !this._monthsParse[i]) {\n                regex =\n                    '^' + this.months(mom, '') + '|^' + this.monthsShort(mom, '');\n                this._monthsParse[i] = new RegExp(regex.replace('.', ''), 'i');\n            }\n            // test the regex\n            if (\n                strict &&\n                format === 'MMMM' &&\n                this._longMonthsParse[i].test(monthName)\n            ) {\n                return i;\n            } else if (\n                strict &&\n                format === 'MMM' &&\n                this._shortMonthsParse[i].test(monthName)\n            ) {\n                return i;\n            } else if (!strict && this._monthsParse[i].test(monthName)) {\n                return i;\n            }\n        }\n    }\n\n    // MOMENTS\n\n    function setMonth(mom, value) {\n        var dayOfMonth;\n\n        if (!mom.isValid()) {\n            // No op\n            return mom;\n        }\n\n        if (typeof value === 'string') {\n            if (/^\\d+$/.test(value)) {\n                value = toInt(value);\n            } else {\n                value = mom.localeData().monthsParse(value);\n                // TODO: Another silent failure?\n                if (!isNumber(value)) {\n                    return mom;\n                }\n            }\n        }\n\n        dayOfMonth = Math.min(mom.date(), daysInMonth(mom.year(), value));\n        mom._d['set' + (mom._isUTC ? 'UTC' : '') + 'Month'](value, dayOfMonth);\n        return mom;\n    }\n\n    function getSetMonth(value) {\n        if (value != null) {\n            setMonth(this, value);\n            hooks.updateOffset(this, true);\n            return this;\n        } else {\n            return get(this, 'Month');\n        }\n    }\n\n    function getDaysInMonth() {\n        return daysInMonth(this.year(), this.month());\n    }\n\n    function monthsShortRegex(isStrict) {\n        if (this._monthsParseExact) {\n            if (!hasOwnProp(this, '_monthsRegex')) {\n                computeMonthsParse.call(this);\n            }\n            if (isStrict) {\n                return this._monthsShortStrictRegex;\n            } else {\n                return this._monthsShortRegex;\n            }\n        } else {\n            if (!hasOwnProp(this, '_monthsShortRegex')) {\n                this._monthsShortRegex = defaultMonthsShortRegex;\n            }\n            return this._monthsShortStrictRegex && isStrict\n                ? this._monthsShortStrictRegex\n                : this._monthsShortRegex;\n        }\n    }\n\n    function monthsRegex(isStrict) {\n        if (this._monthsParseExact) {\n            if (!hasOwnProp(this, '_monthsRegex')) {\n                computeMonthsParse.call(this);\n            }\n            if (isStrict) {\n                return this._monthsStrictRegex;\n            } else {\n                return this._monthsRegex;\n            }\n        } else {\n            if (!hasOwnProp(this, '_monthsRegex')) {\n                this._monthsRegex = defaultMonthsRegex;\n            }\n            return this._monthsStrictRegex && isStrict\n                ? this._monthsStrictRegex\n                : this._monthsRegex;\n        }\n    }\n\n    function computeMonthsParse() {\n        function cmpLenRev(a, b) {\n            return b.length - a.length;\n        }\n\n        var shortPieces = [],\n            longPieces = [],\n            mixedPieces = [],\n            i,\n            mom;\n        for (i = 0; i < 12; i++) {\n            // make the regex if we don't have it already\n            mom = createUTC([2000, i]);\n            shortPieces.push(this.monthsShort(mom, ''));\n            longPieces.push(this.months(mom, ''));\n            mixedPieces.push(this.months(mom, ''));\n            mixedPieces.push(this.monthsShort(mom, ''));\n        }\n        // Sorting makes sure if one month (or abbr) is a prefix of another it\n        // will match the longer piece.\n        shortPieces.sort(cmpLenRev);\n        longPieces.sort(cmpLenRev);\n        mixedPieces.sort(cmpLenRev);\n        for (i = 0; i < 12; i++) {\n            shortPieces[i] = regexEscape(shortPieces[i]);\n            longPieces[i] = regexEscape(longPieces[i]);\n        }\n        for (i = 0; i < 24; i++) {\n            mixedPieces[i] = regexEscape(mixedPieces[i]);\n        }\n\n        this._monthsRegex = new RegExp('^(' + mixedPieces.join('|') + ')', 'i');\n        this._monthsShortRegex = this._monthsRegex;\n        this._monthsStrictRegex = new RegExp(\n            '^(' + longPieces.join('|') + ')',\n            'i'\n        );\n        this._monthsShortStrictRegex = new RegExp(\n            '^(' + shortPieces.join('|') + ')',\n            'i'\n        );\n    }\n\n    // FORMATTING\n\n    addFormatToken('Y', 0, 0, function () {\n        var y = this.year();\n        return y <= 9999 ? zeroFill(y, 4) : '+' + y;\n    });\n\n    addFormatToken(0, ['YY', 2], 0, function () {\n        return this.year() % 100;\n    });\n\n    addFormatToken(0, ['YYYY', 4], 0, 'year');\n    addFormatToken(0, ['YYYYY', 5], 0, 'year');\n    addFormatToken(0, ['YYYYYY', 6, true], 0, 'year');\n\n    // ALIASES\n\n    addUnitAlias('year', 'y');\n\n    // PRIORITIES\n\n    addUnitPriority('year', 1);\n\n    // PARSING\n\n    addRegexToken('Y', matchSigned);\n    addRegexToken('YY', match1to2, match2);\n    addRegexToken('YYYY', match1to4, match4);\n    addRegexToken('YYYYY', match1to6, match6);\n    addRegexToken('YYYYYY', match1to6, match6);\n\n    addParseToken(['YYYYY', 'YYYYYY'], YEAR);\n    addParseToken('YYYY', function (input, array) {\n        array[YEAR] =\n            input.length === 2 ? hooks.parseTwoDigitYear(input) : toInt(input);\n    });\n    addParseToken('YY', function (input, array) {\n        array[YEAR] = hooks.parseTwoDigitYear(input);\n    });\n    addParseToken('Y', function (input, array) {\n        array[YEAR] = parseInt(input, 10);\n    });\n\n    // HELPERS\n\n    function daysInYear(year) {\n        return isLeapYear(year) ? 366 : 365;\n    }\n\n    // HOOKS\n\n    hooks.parseTwoDigitYear = function (input) {\n        return toInt(input) + (toInt(input) > 68 ? 1900 : 2000);\n    };\n\n    // MOMENTS\n\n    var getSetYear = makeGetSet('FullYear', true);\n\n    function getIsLeapYear() {\n        return isLeapYear(this.year());\n    }\n\n    function createDate(y, m, d, h, M, s, ms) {\n        // can't just apply() to create a date:\n        // https://stackoverflow.com/q/181348\n        var date;\n        // the date constructor remaps years 0-99 to 1900-1999\n        if (y < 100 && y >= 0) {\n            // preserve leap years using a full 400 year cycle, then reset\n            date = new Date(y + 400, m, d, h, M, s, ms);\n            if (isFinite(date.getFullYear())) {\n                date.setFullYear(y);\n            }\n        } else {\n            date = new Date(y, m, d, h, M, s, ms);\n        }\n\n        return date;\n    }\n\n    function createUTCDate(y) {\n        var date, args;\n        // the Date.UTC function remaps years 0-99 to 1900-1999\n        if (y < 100 && y >= 0) {\n            args = Array.prototype.slice.call(arguments);\n            // preserve leap years using a full 400 year cycle, then reset\n            args[0] = y + 400;\n            date = new Date(Date.UTC.apply(null, args));\n            if (isFinite(date.getUTCFullYear())) {\n                date.setUTCFullYear(y);\n            }\n        } else {\n            date = new Date(Date.UTC.apply(null, arguments));\n        }\n\n        return date;\n    }\n\n    // start-of-first-week - start-of-year\n    function firstWeekOffset(year, dow, doy) {\n        var // first-week day -- which january is always in the first week (4 for iso, 1 for other)\n            fwd = 7 + dow - doy,\n            // first-week day local weekday -- which local weekday is fwd\n            fwdlw = (7 + createUTCDate(year, 0, fwd).getUTCDay() - dow) % 7;\n\n        return -fwdlw + fwd - 1;\n    }\n\n    // https://en.wikipedia.org/wiki/ISO_week_date#Calculating_a_date_given_the_year.2C_week_number_and_weekday\n    function dayOfYearFromWeeks(year, week, weekday, dow, doy) {\n        var localWeekday = (7 + weekday - dow) % 7,\n            weekOffset = firstWeekOffset(year, dow, doy),\n            dayOfYear = 1 + 7 * (week - 1) + localWeekday + weekOffset,\n            resYear,\n            resDayOfYear;\n\n        if (dayOfYear <= 0) {\n            resYear = year - 1;\n            resDayOfYear = daysInYear(resYear) + dayOfYear;\n        } else if (dayOfYear > daysInYear(year)) {\n            resYear = year + 1;\n            resDayOfYear = dayOfYear - daysInYear(year);\n        } else {\n            resYear = year;\n            resDayOfYear = dayOfYear;\n        }\n\n        return {\n            year: resYear,\n            dayOfYear: resDayOfYear,\n        };\n    }\n\n    function weekOfYear(mom, dow, doy) {\n        var weekOffset = firstWeekOffset(mom.year(), dow, doy),\n            week = Math.floor((mom.dayOfYear() - weekOffset - 1) / 7) + 1,\n            resWeek,\n            resYear;\n\n        if (week < 1) {\n            resYear = mom.year() - 1;\n            resWeek = week + weeksInYear(resYear, dow, doy);\n        } else if (week > weeksInYear(mom.year(), dow, doy)) {\n            resWeek = week - weeksInYear(mom.year(), dow, doy);\n            resYear = mom.year() + 1;\n        } else {\n            resYear = mom.year();\n            resWeek = week;\n        }\n\n        return {\n            week: resWeek,\n            year: resYear,\n        };\n    }\n\n    function weeksInYear(year, dow, doy) {\n        var weekOffset = firstWeekOffset(year, dow, doy),\n            weekOffsetNext = firstWeekOffset(year + 1, dow, doy);\n        return (daysInYear(year) - weekOffset + weekOffsetNext) / 7;\n    }\n\n    // FORMATTING\n\n    addFormatToken('w', ['ww', 2], 'wo', 'week');\n    addFormatToken('W', ['WW', 2], 'Wo', 'isoWeek');\n\n    // ALIASES\n\n    addUnitAlias('week', 'w');\n    addUnitAlias('isoWeek', 'W');\n\n    // PRIORITIES\n\n    addUnitPriority('week', 5);\n    addUnitPriority('isoWeek', 5);\n\n    // PARSING\n\n    addRegexToken('w', match1to2);\n    addRegexToken('ww', match1to2, match2);\n    addRegexToken('W', match1to2);\n    addRegexToken('WW', match1to2, match2);\n\n    addWeekParseToken(['w', 'ww', 'W', 'WW'], function (\n        input,\n        week,\n        config,\n        token\n    ) {\n        week[token.substr(0, 1)] = toInt(input);\n    });\n\n    // HELPERS\n\n    // LOCALES\n\n    function localeWeek(mom) {\n        return weekOfYear(mom, this._week.dow, this._week.doy).week;\n    }\n\n    var defaultLocaleWeek = {\n        dow: 0, // Sunday is the first day of the week.\n        doy: 6, // The week that contains Jan 6th is the first week of the year.\n    };\n\n    function localeFirstDayOfWeek() {\n        return this._week.dow;\n    }\n\n    function localeFirstDayOfYear() {\n        return this._week.doy;\n    }\n\n    // MOMENTS\n\n    function getSetWeek(input) {\n        var week = this.localeData().week(this);\n        return input == null ? week : this.add((input - week) * 7, 'd');\n    }\n\n    function getSetISOWeek(input) {\n        var week = weekOfYear(this, 1, 4).week;\n        return input == null ? week : this.add((input - week) * 7, 'd');\n    }\n\n    // FORMATTING\n\n    addFormatToken('d', 0, 'do', 'day');\n\n    addFormatToken('dd', 0, 0, function (format) {\n        return this.localeData().weekdaysMin(this, format);\n    });\n\n    addFormatToken('ddd', 0, 0, function (format) {\n        return this.localeData().weekdaysShort(this, format);\n    });\n\n    addFormatToken('dddd', 0, 0, function (format) {\n        return this.localeData().weekdays(this, format);\n    });\n\n    addFormatToken('e', 0, 0, 'weekday');\n    addFormatToken('E', 0, 0, 'isoWeekday');\n\n    // ALIASES\n\n    addUnitAlias('day', 'd');\n    addUnitAlias('weekday', 'e');\n    addUnitAlias('isoWeekday', 'E');\n\n    // PRIORITY\n    addUnitPriority('day', 11);\n    addUnitPriority('weekday', 11);\n    addUnitPriority('isoWeekday', 11);\n\n    // PARSING\n\n    addRegexToken('d', match1to2);\n    addRegexToken('e', match1to2);\n    addRegexToken('E', match1to2);\n    addRegexToken('dd', function (isStrict, locale) {\n        return locale.weekdaysMinRegex(isStrict);\n    });\n    addRegexToken('ddd', function (isStrict, locale) {\n        return locale.weekdaysShortRegex(isStrict);\n    });\n    addRegexToken('dddd', function (isStrict, locale) {\n        return locale.weekdaysRegex(isStrict);\n    });\n\n    addWeekParseToken(['dd', 'ddd', 'dddd'], function (input, week, config, token) {\n        var weekday = config._locale.weekdaysParse(input, token, config._strict);\n        // if we didn't get a weekday name, mark the date as invalid\n        if (weekday != null) {\n            week.d = weekday;\n        } else {\n            getParsingFlags(config).invalidWeekday = input;\n        }\n    });\n\n    addWeekParseToken(['d', 'e', 'E'], function (input, week, config, token) {\n        week[token] = toInt(input);\n    });\n\n    // HELPERS\n\n    function parseWeekday(input, locale) {\n        if (typeof input !== 'string') {\n            return input;\n        }\n\n        if (!isNaN(input)) {\n            return parseInt(input, 10);\n        }\n\n        input = locale.weekdaysParse(input);\n        if (typeof input === 'number') {\n            return input;\n        }\n\n        return null;\n    }\n\n    function parseIsoWeekday(input, locale) {\n        if (typeof input === 'string') {\n            return locale.weekdaysParse(input) % 7 || 7;\n        }\n        return isNaN(input) ? null : input;\n    }\n\n    // LOCALES\n    function shiftWeekdays(ws, n) {\n        return ws.slice(n, 7).concat(ws.slice(0, n));\n    }\n\n    var defaultLocaleWeekdays = 'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split(\n            '_'\n        ),\n        defaultLocaleWeekdaysShort = 'Sun_Mon_Tue_Wed_Thu_Fri_Sat'.split('_'),\n        defaultLocaleWeekdaysMin = 'Su_Mo_Tu_We_Th_Fr_Sa'.split('_'),\n        defaultWeekdaysRegex = matchWord,\n        defaultWeekdaysShortRegex = matchWord,\n        defaultWeekdaysMinRegex = matchWord;\n\n    function localeWeekdays(m, format) {\n        var weekdays = isArray(this._weekdays)\n            ? this._weekdays\n            : this._weekdays[\n                  m && m !== true && this._weekdays.isFormat.test(format)\n                      ? 'format'\n                      : 'standalone'\n              ];\n        return m === true\n            ? shiftWeekdays(weekdays, this._week.dow)\n            : m\n            ? weekdays[m.day()]\n            : weekdays;\n    }\n\n    function localeWeekdaysShort(m) {\n        return m === true\n            ? shiftWeekdays(this._weekdaysShort, this._week.dow)\n            : m\n            ? this._weekdaysShort[m.day()]\n            : this._weekdaysShort;\n    }\n\n    function localeWeekdaysMin(m) {\n        return m === true\n            ? shiftWeekdays(this._weekdaysMin, this._week.dow)\n            : m\n            ? this._weekdaysMin[m.day()]\n            : this._weekdaysMin;\n    }\n\n    function handleStrictParse$1(weekdayName, format, strict) {\n        var i,\n            ii,\n            mom,\n            llc = weekdayName.toLocaleLowerCase();\n        if (!this._weekdaysParse) {\n            this._weekdaysParse = [];\n            this._shortWeekdaysParse = [];\n            this._minWeekdaysParse = [];\n\n            for (i = 0; i < 7; ++i) {\n                mom = createUTC([2000, 1]).day(i);\n                this._minWeekdaysParse[i] = this.weekdaysMin(\n                    mom,\n                    ''\n                ).toLocaleLowerCase();\n                this._shortWeekdaysParse[i] = this.weekdaysShort(\n                    mom,\n                    ''\n                ).toLocaleLowerCase();\n                this._weekdaysParse[i] = this.weekdays(mom, '').toLocaleLowerCase();\n            }\n        }\n\n        if (strict) {\n            if (format === 'dddd') {\n                ii = indexOf.call(this._weekdaysParse, llc);\n                return ii !== -1 ? ii : null;\n            } else if (format === 'ddd') {\n                ii = indexOf.call(this._shortWeekdaysParse, llc);\n                return ii !== -1 ? ii : null;\n            } else {\n                ii = indexOf.call(this._minWeekdaysParse, llc);\n                return ii !== -1 ? ii : null;\n            }\n        } else {\n            if (format === 'dddd') {\n                ii = indexOf.call(this._weekdaysParse, llc);\n                if (ii !== -1) {\n                    return ii;\n                }\n                ii = indexOf.call(this._shortWeekdaysParse, llc);\n                if (ii !== -1) {\n                    return ii;\n                }\n                ii = indexOf.call(this._minWeekdaysParse, llc);\n                return ii !== -1 ? ii : null;\n            } else if (format === 'ddd') {\n                ii = indexOf.call(this._shortWeekdaysParse, llc);\n                if (ii !== -1) {\n                    return ii;\n                }\n                ii = indexOf.call(this._weekdaysParse, llc);\n                if (ii !== -1) {\n                    return ii;\n                }\n                ii = indexOf.call(this._minWeekdaysParse, llc);\n                return ii !== -1 ? ii : null;\n            } else {\n                ii = indexOf.call(this._minWeekdaysParse, llc);\n                if (ii !== -1) {\n                    return ii;\n                }\n                ii = indexOf.call(this._weekdaysParse, llc);\n                if (ii !== -1) {\n                    return ii;\n                }\n                ii = indexOf.call(this._shortWeekdaysParse, llc);\n                return ii !== -1 ? ii : null;\n            }\n        }\n    }\n\n    function localeWeekdaysParse(weekdayName, format, strict) {\n        var i, mom, regex;\n\n        if (this._weekdaysParseExact) {\n            return handleStrictParse$1.call(this, weekdayName, format, strict);\n        }\n\n        if (!this._weekdaysParse) {\n            this._weekdaysParse = [];\n            this._minWeekdaysParse = [];\n            this._shortWeekdaysParse = [];\n            this._fullWeekdaysParse = [];\n        }\n\n        for (i = 0; i < 7; i++) {\n            // make the regex if we don't have it already\n\n            mom = createUTC([2000, 1]).day(i);\n            if (strict && !this._fullWeekdaysParse[i]) {\n                this._fullWeekdaysParse[i] = new RegExp(\n                    '^' + this.weekdays(mom, '').replace('.', '\\\\.?') + '$',\n                    'i'\n                );\n                this._shortWeekdaysParse[i] = new RegExp(\n                    '^' + this.weekdaysShort(mom, '').replace('.', '\\\\.?') + '$',\n                    'i'\n                );\n                this._minWeekdaysParse[i] = new RegExp(\n                    '^' + this.weekdaysMin(mom, '').replace('.', '\\\\.?') + '$',\n                    'i'\n                );\n            }\n            if (!this._weekdaysParse[i]) {\n                regex =\n                    '^' +\n                    this.weekdays(mom, '') +\n                    '|^' +\n                    this.weekdaysShort(mom, '') +\n                    '|^' +\n                    this.weekdaysMin(mom, '');\n                this._weekdaysParse[i] = new RegExp(regex.replace('.', ''), 'i');\n            }\n            // test the regex\n            if (\n                strict &&\n                format === 'dddd' &&\n                this._fullWeekdaysParse[i].test(weekdayName)\n            ) {\n                return i;\n            } else if (\n                strict &&\n                format === 'ddd' &&\n                this._shortWeekdaysParse[i].test(weekdayName)\n            ) {\n                return i;\n            } else if (\n                strict &&\n                format === 'dd' &&\n                this._minWeekdaysParse[i].test(weekdayName)\n            ) {\n                return i;\n            } else if (!strict && this._weekdaysParse[i].test(weekdayName)) {\n                return i;\n            }\n        }\n    }\n\n    // MOMENTS\n\n    function getSetDayOfWeek(input) {\n        if (!this.isValid()) {\n            return input != null ? this : NaN;\n        }\n        var day = this._isUTC ? this._d.getUTCDay() : this._d.getDay();\n        if (input != null) {\n            input = parseWeekday(input, this.localeData());\n            return this.add(input - day, 'd');\n        } else {\n            return day;\n        }\n    }\n\n    function getSetLocaleDayOfWeek(input) {\n        if (!this.isValid()) {\n            return input != null ? this : NaN;\n        }\n        var weekday = (this.day() + 7 - this.localeData()._week.dow) % 7;\n        return input == null ? weekday : this.add(input - weekday, 'd');\n    }\n\n    function getSetISODayOfWeek(input) {\n        if (!this.isValid()) {\n            return input != null ? this : NaN;\n        }\n\n        // behaves the same as moment#day except\n        // as a getter, returns 7 instead of 0 (1-7 range instead of 0-6)\n        // as a setter, sunday should belong to the previous week.\n\n        if (input != null) {\n            var weekday = parseIsoWeekday(input, this.localeData());\n            return this.day(this.day() % 7 ? weekday : weekday - 7);\n        } else {\n            return this.day() || 7;\n        }\n    }\n\n    function weekdaysRegex(isStrict) {\n        if (this._weekdaysParseExact) {\n            if (!hasOwnProp(this, '_weekdaysRegex')) {\n                computeWeekdaysParse.call(this);\n            }\n            if (isStrict) {\n                return this._weekdaysStrictRegex;\n            } else {\n                return this._weekdaysRegex;\n            }\n        } else {\n            if (!hasOwnProp(this, '_weekdaysRegex')) {\n                this._weekdaysRegex = defaultWeekdaysRegex;\n            }\n            return this._weekdaysStrictRegex && isStrict\n                ? this._weekdaysStrictRegex\n                : this._weekdaysRegex;\n        }\n    }\n\n    function weekdaysShortRegex(isStrict) {\n        if (this._weekdaysParseExact) {\n            if (!hasOwnProp(this, '_weekdaysRegex')) {\n                computeWeekdaysParse.call(this);\n            }\n            if (isStrict) {\n                return this._weekdaysShortStrictRegex;\n            } else {\n                return this._weekdaysShortRegex;\n            }\n        } else {\n            if (!hasOwnProp(this, '_weekdaysShortRegex')) {\n                this._weekdaysShortRegex = defaultWeekdaysShortRegex;\n            }\n            return this._weekdaysShortStrictRegex && isStrict\n                ? this._weekdaysShortStrictRegex\n                : this._weekdaysShortRegex;\n        }\n    }\n\n    function weekdaysMinRegex(isStrict) {\n        if (this._weekdaysParseExact) {\n            if (!hasOwnProp(this, '_weekdaysRegex')) {\n                computeWeekdaysParse.call(this);\n            }\n            if (isStrict) {\n                return this._weekdaysMinStrictRegex;\n            } else {\n                return this._weekdaysMinRegex;\n            }\n        } else {\n            if (!hasOwnProp(this, '_weekdaysMinRegex')) {\n                this._weekdaysMinRegex = defaultWeekdaysMinRegex;\n            }\n            return this._weekdaysMinStrictRegex && isStrict\n                ? this._weekdaysMinStrictRegex\n                : this._weekdaysMinRegex;\n        }\n    }\n\n    function computeWeekdaysParse() {\n        function cmpLenRev(a, b) {\n            return b.length - a.length;\n        }\n\n        var minPieces = [],\n            shortPieces = [],\n            longPieces = [],\n            mixedPieces = [],\n            i,\n            mom,\n            minp,\n            shortp,\n            longp;\n        for (i = 0; i < 7; i++) {\n            // make the regex if we don't have it already\n            mom = createUTC([2000, 1]).day(i);\n            minp = regexEscape(this.weekdaysMin(mom, ''));\n            shortp = regexEscape(this.weekdaysShort(mom, ''));\n            longp = regexEscape(this.weekdays(mom, ''));\n            minPieces.push(minp);\n            shortPieces.push(shortp);\n            longPieces.push(longp);\n            mixedPieces.push(minp);\n            mixedPieces.push(shortp);\n            mixedPieces.push(longp);\n        }\n        // Sorting makes sure if one weekday (or abbr) is a prefix of another it\n        // will match the longer piece.\n        minPieces.sort(cmpLenRev);\n        shortPieces.sort(cmpLenRev);\n        longPieces.sort(cmpLenRev);\n        mixedPieces.sort(cmpLenRev);\n\n        this._weekdaysRegex = new RegExp('^(' + mixedPieces.join('|') + ')', 'i');\n        this._weekdaysShortRegex = this._weekdaysRegex;\n        this._weekdaysMinRegex = this._weekdaysRegex;\n\n        this._weekdaysStrictRegex = new RegExp(\n            '^(' + longPieces.join('|') + ')',\n            'i'\n        );\n        this._weekdaysShortStrictRegex = new RegExp(\n            '^(' + shortPieces.join('|') + ')',\n            'i'\n        );\n        this._weekdaysMinStrictRegex = new RegExp(\n            '^(' + minPieces.join('|') + ')',\n            'i'\n        );\n    }\n\n    // FORMATTING\n\n    function hFormat() {\n        return this.hours() % 12 || 12;\n    }\n\n    function kFormat() {\n        return this.hours() || 24;\n    }\n\n    addFormatToken('H', ['HH', 2], 0, 'hour');\n    addFormatToken('h', ['hh', 2], 0, hFormat);\n    addFormatToken('k', ['kk', 2], 0, kFormat);\n\n    addFormatToken('hmm', 0, 0, function () {\n        return '' + hFormat.apply(this) + zeroFill(this.minutes(), 2);\n    });\n\n    addFormatToken('hmmss', 0, 0, function () {\n        return (\n            '' +\n            hFormat.apply(this) +\n            zeroFill(this.minutes(), 2) +\n            zeroFill(this.seconds(), 2)\n        );\n    });\n\n    addFormatToken('Hmm', 0, 0, function () {\n        return '' + this.hours() + zeroFill(this.minutes(), 2);\n    });\n\n    addFormatToken('Hmmss', 0, 0, function () {\n        return (\n            '' +\n            this.hours() +\n            zeroFill(this.minutes(), 2) +\n            zeroFill(this.seconds(), 2)\n        );\n    });\n\n    function meridiem(token, lowercase) {\n        addFormatToken(token, 0, 0, function () {\n            return this.localeData().meridiem(\n                this.hours(),\n                this.minutes(),\n                lowercase\n            );\n        });\n    }\n\n    meridiem('a', true);\n    meridiem('A', false);\n\n    // ALIASES\n\n    addUnitAlias('hour', 'h');\n\n    // PRIORITY\n    addUnitPriority('hour', 13);\n\n    // PARSING\n\n    function matchMeridiem(isStrict, locale) {\n        return locale._meridiemParse;\n    }\n\n    addRegexToken('a', matchMeridiem);\n    addRegexToken('A', matchMeridiem);\n    addRegexToken('H', match1to2);\n    addRegexToken('h', match1to2);\n    addRegexToken('k', match1to2);\n    addRegexToken('HH', match1to2, match2);\n    addRegexToken('hh', match1to2, match2);\n    addRegexToken('kk', match1to2, match2);\n\n    addRegexToken('hmm', match3to4);\n    addRegexToken('hmmss', match5to6);\n    addRegexToken('Hmm', match3to4);\n    addRegexToken('Hmmss', match5to6);\n\n    addParseToken(['H', 'HH'], HOUR);\n    addParseToken(['k', 'kk'], function (input, array, config) {\n        var kInput = toInt(input);\n        array[HOUR] = kInput === 24 ? 0 : kInput;\n    });\n    addParseToken(['a', 'A'], function (input, array, config) {\n        config._isPm = config._locale.isPM(input);\n        config._meridiem = input;\n    });\n    addParseToken(['h', 'hh'], function (input, array, config) {\n        array[HOUR] = toInt(input);\n        getParsingFlags(config).bigHour = true;\n    });\n    addParseToken('hmm', function (input, array, config) {\n        var pos = input.length - 2;\n        array[HOUR] = toInt(input.substr(0, pos));\n        array[MINUTE] = toInt(input.substr(pos));\n        getParsingFlags(config).bigHour = true;\n    });\n    addParseToken('hmmss', function (input, array, config) {\n        var pos1 = input.length - 4,\n            pos2 = input.length - 2;\n        array[HOUR] = toInt(input.substr(0, pos1));\n        array[MINUTE] = toInt(input.substr(pos1, 2));\n        array[SECOND] = toInt(input.substr(pos2));\n        getParsingFlags(config).bigHour = true;\n    });\n    addParseToken('Hmm', function (input, array, config) {\n        var pos = input.length - 2;\n        array[HOUR] = toInt(input.substr(0, pos));\n        array[MINUTE] = toInt(input.substr(pos));\n    });\n    addParseToken('Hmmss', function (input, array, config) {\n        var pos1 = input.length - 4,\n            pos2 = input.length - 2;\n        array[HOUR] = toInt(input.substr(0, pos1));\n        array[MINUTE] = toInt(input.substr(pos1, 2));\n        array[SECOND] = toInt(input.substr(pos2));\n    });\n\n    // LOCALES\n\n    function localeIsPM(input) {\n        // IE8 Quirks Mode & IE7 Standards Mode do not allow accessing strings like arrays\n        // Using charAt should be more compatible.\n        return (input + '').toLowerCase().charAt(0) === 'p';\n    }\n\n    var defaultLocaleMeridiemParse = /[ap]\\.?m?\\.?/i,\n        // Setting the hour should keep the time, because the user explicitly\n        // specified which hour they want. So trying to maintain the same hour (in\n        // a new timezone) makes sense. Adding/subtracting hours does not follow\n        // this rule.\n        getSetHour = makeGetSet('Hours', true);\n\n    function localeMeridiem(hours, minutes, isLower) {\n        if (hours > 11) {\n            return isLower ? 'pm' : 'PM';\n        } else {\n            return isLower ? 'am' : 'AM';\n        }\n    }\n\n    var baseConfig = {\n        calendar: defaultCalendar,\n        longDateFormat: defaultLongDateFormat,\n        invalidDate: defaultInvalidDate,\n        ordinal: defaultOrdinal,\n        dayOfMonthOrdinalParse: defaultDayOfMonthOrdinalParse,\n        relativeTime: defaultRelativeTime,\n\n        months: defaultLocaleMonths,\n        monthsShort: defaultLocaleMonthsShort,\n\n        week: defaultLocaleWeek,\n\n        weekdays: defaultLocaleWeekdays,\n        weekdaysMin: defaultLocaleWeekdaysMin,\n        weekdaysShort: defaultLocaleWeekdaysShort,\n\n        meridiemParse: defaultLocaleMeridiemParse,\n    };\n\n    // internal storage for locale config files\n    var locales = {},\n        localeFamilies = {},\n        globalLocale;\n\n    function commonPrefix(arr1, arr2) {\n        var i,\n            minl = Math.min(arr1.length, arr2.length);\n        for (i = 0; i < minl; i += 1) {\n            if (arr1[i] !== arr2[i]) {\n                return i;\n            }\n        }\n        return minl;\n    }\n\n    function normalizeLocale(key) {\n        return key ? key.toLowerCase().replace('_', '-') : key;\n    }\n\n    // pick the locale from the array\n    // try ['en-au', 'en-gb'] as 'en-au', 'en-gb', 'en', as in move through the list trying each\n    // substring from most specific to least, but move to the next array item if it's a more specific variant than the current root\n    function chooseLocale(names) {\n        var i = 0,\n            j,\n            next,\n            locale,\n            split;\n\n        while (i < names.length) {\n            split = normalizeLocale(names[i]).split('-');\n            j = split.length;\n            next = normalizeLocale(names[i + 1]);\n            next = next ? next.split('-') : null;\n            while (j > 0) {\n                locale = loadLocale(split.slice(0, j).join('-'));\n                if (locale) {\n                    return locale;\n                }\n                if (\n                    next &&\n                    next.length >= j &&\n                    commonPrefix(split, next) >= j - 1\n                ) {\n                    //the next array item is better than a shallower substring of this one\n                    break;\n                }\n                j--;\n            }\n            i++;\n        }\n        return globalLocale;\n    }\n\n    function loadLocale(name) {\n        var oldLocale = null,\n            aliasedRequire;\n        // TODO: Find a better way to register and load all the locales in Node\n        if (\n            locales[name] === undefined &&\n            typeof module !== 'undefined' &&\n            module &&\n            module.exports\n        ) {\n            try {\n                oldLocale = globalLocale._abbr;\n                aliasedRequire = require;\n                aliasedRequire('./locale/' + name);\n                getSetGlobalLocale(oldLocale);\n            } catch (e) {\n                // mark as not found to avoid repeating expensive file require call causing high CPU\n                // when trying to find en-US, en_US, en-us for every format call\n                locales[name] = null; // null means not found\n            }\n        }\n        return locales[name];\n    }\n\n    // This function will load locale and then set the global locale.  If\n    // no arguments are passed in, it will simply return the current global\n    // locale key.\n    function getSetGlobalLocale(key, values) {\n        var data;\n        if (key) {\n            if (isUndefined(values)) {\n                data = getLocale(key);\n            } else {\n                data = defineLocale(key, values);\n            }\n\n            if (data) {\n                // moment.duration._locale = moment._locale = data;\n                globalLocale = data;\n            } else {\n                if (typeof console !== 'undefined' && console.warn) {\n                    //warn user if arguments are passed but the locale could not be set\n                    console.warn(\n                        'Locale ' + key + ' not found. Did you forget to load it?'\n                    );\n                }\n            }\n        }\n\n        return globalLocale._abbr;\n    }\n\n    function defineLocale(name, config) {\n        if (config !== null) {\n            var locale,\n                parentConfig = baseConfig;\n            config.abbr = name;\n            if (locales[name] != null) {\n                deprecateSimple(\n                    'defineLocaleOverride',\n                    'use moment.updateLocale(localeName, config) to change ' +\n                        'an existing locale. moment.defineLocale(localeName, ' +\n                        'config) should only be used for creating a new locale ' +\n                        'See http://momentjs.com/guides/#/warnings/define-locale/ for more info.'\n                );\n                parentConfig = locales[name]._config;\n            } else if (config.parentLocale != null) {\n                if (locales[config.parentLocale] != null) {\n                    parentConfig = locales[config.parentLocale]._config;\n                } else {\n                    locale = loadLocale(config.parentLocale);\n                    if (locale != null) {\n                        parentConfig = locale._config;\n                    } else {\n                        if (!localeFamilies[config.parentLocale]) {\n                            localeFamilies[config.parentLocale] = [];\n                        }\n                        localeFamilies[config.parentLocale].push({\n                            name: name,\n                            config: config,\n                        });\n                        return null;\n                    }\n                }\n            }\n            locales[name] = new Locale(mergeConfigs(parentConfig, config));\n\n            if (localeFamilies[name]) {\n                localeFamilies[name].forEach(function (x) {\n                    defineLocale(x.name, x.config);\n                });\n            }\n\n            // backwards compat for now: also set the locale\n            // make sure we set the locale AFTER all child locales have been\n            // created, so we won't end up with the child locale set.\n            getSetGlobalLocale(name);\n\n            return locales[name];\n        } else {\n            // useful for testing\n            delete locales[name];\n            return null;\n        }\n    }\n\n    function updateLocale(name, config) {\n        if (config != null) {\n            var locale,\n                tmpLocale,\n                parentConfig = baseConfig;\n\n            if (locales[name] != null && locales[name].parentLocale != null) {\n                // Update existing child locale in-place to avoid memory-leaks\n                locales[name].set(mergeConfigs(locales[name]._config, config));\n            } else {\n                // MERGE\n                tmpLocale = loadLocale(name);\n                if (tmpLocale != null) {\n                    parentConfig = tmpLocale._config;\n                }\n                config = mergeConfigs(parentConfig, config);\n                if (tmpLocale == null) {\n                    // updateLocale is called for creating a new locale\n                    // Set abbr so it will have a name (getters return\n                    // undefined otherwise).\n                    config.abbr = name;\n                }\n                locale = new Locale(config);\n                locale.parentLocale = locales[name];\n                locales[name] = locale;\n            }\n\n            // backwards compat for now: also set the locale\n            getSetGlobalLocale(name);\n        } else {\n            // pass null for config to unupdate, useful for tests\n            if (locales[name] != null) {\n                if (locales[name].parentLocale != null) {\n                    locales[name] = locales[name].parentLocale;\n                    if (name === getSetGlobalLocale()) {\n                        getSetGlobalLocale(name);\n                    }\n                } else if (locales[name] != null) {\n                    delete locales[name];\n                }\n            }\n        }\n        return locales[name];\n    }\n\n    // returns locale data\n    function getLocale(key) {\n        var locale;\n\n        if (key && key._locale && key._locale._abbr) {\n            key = key._locale._abbr;\n        }\n\n        if (!key) {\n            return globalLocale;\n        }\n\n        if (!isArray(key)) {\n            //short-circuit everything else\n            locale = loadLocale(key);\n            if (locale) {\n                return locale;\n            }\n            key = [key];\n        }\n\n        return chooseLocale(key);\n    }\n\n    function listLocales() {\n        return keys(locales);\n    }\n\n    function checkOverflow(m) {\n        var overflow,\n            a = m._a;\n\n        if (a && getParsingFlags(m).overflow === -2) {\n            overflow =\n                a[MONTH] < 0 || a[MONTH] > 11\n                    ? MONTH\n                    : a[DATE] < 1 || a[DATE] > daysInMonth(a[YEAR], a[MONTH])\n                    ? DATE\n                    : a[HOUR] < 0 ||\n                      a[HOUR] > 24 ||\n                      (a[HOUR] === 24 &&\n                          (a[MINUTE] !== 0 ||\n                              a[SECOND] !== 0 ||\n                              a[MILLISECOND] !== 0))\n                    ? HOUR\n                    : a[MINUTE] < 0 || a[MINUTE] > 59\n                    ? MINUTE\n                    : a[SECOND] < 0 || a[SECOND] > 59\n                    ? SECOND\n                    : a[MILLISECOND] < 0 || a[MILLISECOND] > 999\n                    ? MILLISECOND\n                    : -1;\n\n            if (\n                getParsingFlags(m)._overflowDayOfYear &&\n                (overflow < YEAR || overflow > DATE)\n            ) {\n                overflow = DATE;\n            }\n            if (getParsingFlags(m)._overflowWeeks && overflow === -1) {\n                overflow = WEEK;\n            }\n            if (getParsingFlags(m)._overflowWeekday && overflow === -1) {\n                overflow = WEEKDAY;\n            }\n\n            getParsingFlags(m).overflow = overflow;\n        }\n\n        return m;\n    }\n\n    // iso 8601 regex\n    // 0000-00-00 0000-W00 or 0000-W00-0 + T + 00 or 00:00 or 00:00:00 or 00:00:00.000 + +00:00 or +0000 or +00)\n    var extendedIsoRegex = /^\\s*((?:[+-]\\d{6}|\\d{4})-(?:\\d\\d-\\d\\d|W\\d\\d-\\d|W\\d\\d|\\d\\d\\d|\\d\\d))(?:(T| )(\\d\\d(?::\\d\\d(?::\\d\\d(?:[.,]\\d+)?)?)?)([+-]\\d\\d(?::?\\d\\d)?|\\s*Z)?)?$/,\n        basicIsoRegex = /^\\s*((?:[+-]\\d{6}|\\d{4})(?:\\d\\d\\d\\d|W\\d\\d\\d|W\\d\\d|\\d\\d\\d|\\d\\d|))(?:(T| )(\\d\\d(?:\\d\\d(?:\\d\\d(?:[.,]\\d+)?)?)?)([+-]\\d\\d(?::?\\d\\d)?|\\s*Z)?)?$/,\n        tzRegex = /Z|[+-]\\d\\d(?::?\\d\\d)?/,\n        isoDates = [\n            ['YYYYYY-MM-DD', /[+-]\\d{6}-\\d\\d-\\d\\d/],\n            ['YYYY-MM-DD', /\\d{4}-\\d\\d-\\d\\d/],\n            ['GGGG-[W]WW-E', /\\d{4}-W\\d\\d-\\d/],\n            ['GGGG-[W]WW', /\\d{4}-W\\d\\d/, false],\n            ['YYYY-DDD', /\\d{4}-\\d{3}/],\n            ['YYYY-MM', /\\d{4}-\\d\\d/, false],\n            ['YYYYYYMMDD', /[+-]\\d{10}/],\n            ['YYYYMMDD', /\\d{8}/],\n            ['GGGG[W]WWE', /\\d{4}W\\d{3}/],\n            ['GGGG[W]WW', /\\d{4}W\\d{2}/, false],\n            ['YYYYDDD', /\\d{7}/],\n            ['YYYYMM', /\\d{6}/, false],\n            ['YYYY', /\\d{4}/, false],\n        ],\n        // iso time formats and regexes\n        isoTimes = [\n            ['HH:mm:ss.SSSS', /\\d\\d:\\d\\d:\\d\\d\\.\\d+/],\n            ['HH:mm:ss,SSSS', /\\d\\d:\\d\\d:\\d\\d,\\d+/],\n            ['HH:mm:ss', /\\d\\d:\\d\\d:\\d\\d/],\n            ['HH:mm', /\\d\\d:\\d\\d/],\n            ['HHmmss.SSSS', /\\d\\d\\d\\d\\d\\d\\.\\d+/],\n            ['HHmmss,SSSS', /\\d\\d\\d\\d\\d\\d,\\d+/],\n            ['HHmmss', /\\d\\d\\d\\d\\d\\d/],\n            ['HHmm', /\\d\\d\\d\\d/],\n            ['HH', /\\d\\d/],\n        ],\n        aspNetJsonRegex = /^\\/?Date\\((-?\\d+)/i,\n        // RFC 2822 regex: For details see https://tools.ietf.org/html/rfc2822#section-3.3\n        rfc2822 = /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\\s)?(\\d{1,2})\\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s(\\d{2,4})\\s(\\d\\d):(\\d\\d)(?::(\\d\\d))?\\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\\d{4}))$/,\n        obsOffsets = {\n            UT: 0,\n            GMT: 0,\n            EDT: -4 * 60,\n            EST: -5 * 60,\n            CDT: -5 * 60,\n            CST: -6 * 60,\n            MDT: -6 * 60,\n            MST: -7 * 60,\n            PDT: -7 * 60,\n            PST: -8 * 60,\n        };\n\n    // date from iso format\n    function configFromISO(config) {\n        var i,\n            l,\n            string = config._i,\n            match = extendedIsoRegex.exec(string) || basicIsoRegex.exec(string),\n            allowTime,\n            dateFormat,\n            timeFormat,\n            tzFormat;\n\n        if (match) {\n            getParsingFlags(config).iso = true;\n\n            for (i = 0, l = isoDates.length; i < l; i++) {\n                if (isoDates[i][1].exec(match[1])) {\n                    dateFormat = isoDates[i][0];\n                    allowTime = isoDates[i][2] !== false;\n                    break;\n                }\n            }\n            if (dateFormat == null) {\n                config._isValid = false;\n                return;\n            }\n            if (match[3]) {\n                for (i = 0, l = isoTimes.length; i < l; i++) {\n                    if (isoTimes[i][1].exec(match[3])) {\n                        // match[2] should be 'T' or space\n                        timeFormat = (match[2] || ' ') + isoTimes[i][0];\n                        break;\n                    }\n                }\n                if (timeFormat == null) {\n                    config._isValid = false;\n                    return;\n                }\n            }\n            if (!allowTime && timeFormat != null) {\n                config._isValid = false;\n                return;\n            }\n            if (match[4]) {\n                if (tzRegex.exec(match[4])) {\n                    tzFormat = 'Z';\n                } else {\n                    config._isValid = false;\n                    return;\n                }\n            }\n            config._f = dateFormat + (timeFormat || '') + (tzFormat || '');\n            configFromStringAndFormat(config);\n        } else {\n            config._isValid = false;\n        }\n    }\n\n    function extractFromRFC2822Strings(\n        yearStr,\n        monthStr,\n        dayStr,\n        hourStr,\n        minuteStr,\n        secondStr\n    ) {\n        var result = [\n            untruncateYear(yearStr),\n            defaultLocaleMonthsShort.indexOf(monthStr),\n            parseInt(dayStr, 10),\n            parseInt(hourStr, 10),\n            parseInt(minuteStr, 10),\n        ];\n\n        if (secondStr) {\n            result.push(parseInt(secondStr, 10));\n        }\n\n        return result;\n    }\n\n    function untruncateYear(yearStr) {\n        var year = parseInt(yearStr, 10);\n        if (year <= 49) {\n            return 2000 + year;\n        } else if (year <= 999) {\n            return 1900 + year;\n        }\n        return year;\n    }\n\n    function preprocessRFC2822(s) {\n        // Remove comments and folding whitespace and replace multiple-spaces with a single space\n        return s\n            .replace(/\\([^)]*\\)|[\\n\\t]/g, ' ')\n            .replace(/(\\s\\s+)/g, ' ')\n            .replace(/^\\s\\s*/, '')\n            .replace(/\\s\\s*$/, '');\n    }\n\n    function checkWeekday(weekdayStr, parsedInput, config) {\n        if (weekdayStr) {\n            // TODO: Replace the vanilla JS Date object with an independent day-of-week check.\n            var weekdayProvided = defaultLocaleWeekdaysShort.indexOf(weekdayStr),\n                weekdayActual = new Date(\n                    parsedInput[0],\n                    parsedInput[1],\n                    parsedInput[2]\n                ).getDay();\n            if (weekdayProvided !== weekdayActual) {\n                getParsingFlags(config).weekdayMismatch = true;\n                config._isValid = false;\n                return false;\n            }\n        }\n        return true;\n    }\n\n    function calculateOffset(obsOffset, militaryOffset, numOffset) {\n        if (obsOffset) {\n            return obsOffsets[obsOffset];\n        } else if (militaryOffset) {\n            // the only allowed military tz is Z\n            return 0;\n        } else {\n            var hm = parseInt(numOffset, 10),\n                m = hm % 100,\n                h = (hm - m) / 100;\n            return h * 60 + m;\n        }\n    }\n\n    // date and time from ref 2822 format\n    function configFromRFC2822(config) {\n        var match = rfc2822.exec(preprocessRFC2822(config._i)),\n            parsedArray;\n        if (match) {\n            parsedArray = extractFromRFC2822Strings(\n                match[4],\n                match[3],\n                match[2],\n                match[5],\n                match[6],\n                match[7]\n            );\n            if (!checkWeekday(match[1], parsedArray, config)) {\n                return;\n            }\n\n            config._a = parsedArray;\n            config._tzm = calculateOffset(match[8], match[9], match[10]);\n\n            config._d = createUTCDate.apply(null, config._a);\n            config._d.setUTCMinutes(config._d.getUTCMinutes() - config._tzm);\n\n            getParsingFlags(config).rfc2822 = true;\n        } else {\n            config._isValid = false;\n        }\n    }\n\n    // date from 1) ASP.NET, 2) ISO, 3) RFC 2822 formats, or 4) optional fallback if parsing isn't strict\n    function configFromString(config) {\n        var matched = aspNetJsonRegex.exec(config._i);\n        if (matched !== null) {\n            config._d = new Date(+matched[1]);\n            return;\n        }\n\n        configFromISO(config);\n        if (config._isValid === false) {\n            delete config._isValid;\n        } else {\n            return;\n        }\n\n        configFromRFC2822(config);\n        if (config._isValid === false) {\n            delete config._isValid;\n        } else {\n            return;\n        }\n\n        if (config._strict) {\n            config._isValid = false;\n        } else {\n            // Final attempt, use Input Fallback\n            hooks.createFromInputFallback(config);\n        }\n    }\n\n    hooks.createFromInputFallback = deprecate(\n        'value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), ' +\n            'which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are ' +\n            'discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.',\n        function (config) {\n            config._d = new Date(config._i + (config._useUTC ? ' UTC' : ''));\n        }\n    );\n\n    // Pick the first defined of two or three arguments.\n    function defaults(a, b, c) {\n        if (a != null) {\n            return a;\n        }\n        if (b != null) {\n            return b;\n        }\n        return c;\n    }\n\n    function currentDateArray(config) {\n        // hooks is actually the exported moment object\n        var nowValue = new Date(hooks.now());\n        if (config._useUTC) {\n            return [\n                nowValue.getUTCFullYear(),\n                nowValue.getUTCMonth(),\n                nowValue.getUTCDate(),\n            ];\n        }\n        return [nowValue.getFullYear(), nowValue.getMonth(), nowValue.getDate()];\n    }\n\n    // convert an array to a date.\n    // the array should mirror the parameters below\n    // note: all values past the year are optional and will default to the lowest possible value.\n    // [year, month, day , hour, minute, second, millisecond]\n    function configFromArray(config) {\n        var i,\n            date,\n            input = [],\n            currentDate,\n            expectedWeekday,\n            yearToUse;\n\n        if (config._d) {\n            return;\n        }\n\n        currentDate = currentDateArray(config);\n\n        //compute day of the year from weeks and weekdays\n        if (config._w && config._a[DATE] == null && config._a[MONTH] == null) {\n            dayOfYearFromWeekInfo(config);\n        }\n\n        //if the day of the year is set, figure out what it is\n        if (config._dayOfYear != null) {\n            yearToUse = defaults(config._a[YEAR], currentDate[YEAR]);\n\n            if (\n                config._dayOfYear > daysInYear(yearToUse) ||\n                config._dayOfYear === 0\n            ) {\n                getParsingFlags(config)._overflowDayOfYear = true;\n            }\n\n            date = createUTCDate(yearToUse, 0, config._dayOfYear);\n            config._a[MONTH] = date.getUTCMonth();\n            config._a[DATE] = date.getUTCDate();\n        }\n\n        // Default to current date.\n        // * if no year, month, day of month are given, default to today\n        // * if day of month is given, default month and year\n        // * if month is given, default only year\n        // * if year is given, don't default anything\n        for (i = 0; i < 3 && config._a[i] == null; ++i) {\n            config._a[i] = input[i] = currentDate[i];\n        }\n\n        // Zero out whatever was not defaulted, including time\n        for (; i < 7; i++) {\n            config._a[i] = input[i] =\n                config._a[i] == null ? (i === 2 ? 1 : 0) : config._a[i];\n        }\n\n        // Check for 24:00:00.000\n        if (\n            config._a[HOUR] === 24 &&\n            config._a[MINUTE] === 0 &&\n            config._a[SECOND] === 0 &&\n            config._a[MILLISECOND] === 0\n        ) {\n            config._nextDay = true;\n            config._a[HOUR] = 0;\n        }\n\n        config._d = (config._useUTC ? createUTCDate : createDate).apply(\n            null,\n            input\n        );\n        expectedWeekday = config._useUTC\n            ? config._d.getUTCDay()\n            : config._d.getDay();\n\n        // Apply timezone offset from input. The actual utcOffset can be changed\n        // with parseZone.\n        if (config._tzm != null) {\n            config._d.setUTCMinutes(config._d.getUTCMinutes() - config._tzm);\n        }\n\n        if (config._nextDay) {\n            config._a[HOUR] = 24;\n        }\n\n        // check for mismatching day of week\n        if (\n            config._w &&\n            typeof config._w.d !== 'undefined' &&\n            config._w.d !== expectedWeekday\n        ) {\n            getParsingFlags(config).weekdayMismatch = true;\n        }\n    }\n\n    function dayOfYearFromWeekInfo(config) {\n        var w, weekYear, week, weekday, dow, doy, temp, weekdayOverflow, curWeek;\n\n        w = config._w;\n        if (w.GG != null || w.W != null || w.E != null) {\n            dow = 1;\n            doy = 4;\n\n            // TODO: We need to take the current isoWeekYear, but that depends on\n            // how we interpret now (local, utc, fixed offset). So create\n            // a now version of current config (take local/utc/offset flags, and\n            // create now).\n            weekYear = defaults(\n                w.GG,\n                config._a[YEAR],\n                weekOfYear(createLocal(), 1, 4).year\n            );\n            week = defaults(w.W, 1);\n            weekday = defaults(w.E, 1);\n            if (weekday < 1 || weekday > 7) {\n                weekdayOverflow = true;\n            }\n        } else {\n            dow = config._locale._week.dow;\n            doy = config._locale._week.doy;\n\n            curWeek = weekOfYear(createLocal(), dow, doy);\n\n            weekYear = defaults(w.gg, config._a[YEAR], curWeek.year);\n\n            // Default to current week.\n            week = defaults(w.w, curWeek.week);\n\n            if (w.d != null) {\n                // weekday -- low day numbers are considered next week\n                weekday = w.d;\n                if (weekday < 0 || weekday > 6) {\n                    weekdayOverflow = true;\n                }\n            } else if (w.e != null) {\n                // local weekday -- counting starts from beginning of week\n                weekday = w.e + dow;\n                if (w.e < 0 || w.e > 6) {\n                    weekdayOverflow = true;\n                }\n            } else {\n                // default to beginning of week\n                weekday = dow;\n            }\n        }\n        if (week < 1 || week > weeksInYear(weekYear, dow, doy)) {\n            getParsingFlags(config)._overflowWeeks = true;\n        } else if (weekdayOverflow != null) {\n            getParsingFlags(config)._overflowWeekday = true;\n        } else {\n            temp = dayOfYearFromWeeks(weekYear, week, weekday, dow, doy);\n            config._a[YEAR] = temp.year;\n            config._dayOfYear = temp.dayOfYear;\n        }\n    }\n\n    // constant that refers to the ISO standard\n    hooks.ISO_8601 = function () {};\n\n    // constant that refers to the RFC 2822 form\n    hooks.RFC_2822 = function () {};\n\n    // date from string and format string\n    function configFromStringAndFormat(config) {\n        // TODO: Move this to another part of the creation flow to prevent circular deps\n        if (config._f === hooks.ISO_8601) {\n            configFromISO(config);\n            return;\n        }\n        if (config._f === hooks.RFC_2822) {\n            configFromRFC2822(config);\n            return;\n        }\n        config._a = [];\n        getParsingFlags(config).empty = true;\n\n        // This array is used to make a Date, either with `new Date` or `Date.UTC`\n        var string = '' + config._i,\n            i,\n            parsedInput,\n            tokens,\n            token,\n            skipped,\n            stringLength = string.length,\n            totalParsedInputLength = 0,\n            era;\n\n        tokens =\n            expandFormat(config._f, config._locale).match(formattingTokens) || [];\n\n        for (i = 0; i < tokens.length; i++) {\n            token = tokens[i];\n            parsedInput = (string.match(getParseRegexForToken(token, config)) ||\n                [])[0];\n            if (parsedInput) {\n                skipped = string.substr(0, string.indexOf(parsedInput));\n                if (skipped.length > 0) {\n                    getParsingFlags(config).unusedInput.push(skipped);\n                }\n                string = string.slice(\n                    string.indexOf(parsedInput) + parsedInput.length\n                );\n                totalParsedInputLength += parsedInput.length;\n            }\n            // don't parse if it's not a known token\n            if (formatTokenFunctions[token]) {\n                if (parsedInput) {\n                    getParsingFlags(config).empty = false;\n                } else {\n                    getParsingFlags(config).unusedTokens.push(token);\n                }\n                addTimeToArrayFromToken(token, parsedInput, config);\n            } else if (config._strict && !parsedInput) {\n                getParsingFlags(config).unusedTokens.push(token);\n            }\n        }\n\n        // add remaining unparsed input length to the string\n        getParsingFlags(config).charsLeftOver =\n            stringLength - totalParsedInputLength;\n        if (string.length > 0) {\n            getParsingFlags(config).unusedInput.push(string);\n        }\n\n        // clear _12h flag if hour is <= 12\n        if (\n            config._a[HOUR] <= 12 &&\n            getParsingFlags(config).bigHour === true &&\n            config._a[HOUR] > 0\n        ) {\n            getParsingFlags(config).bigHour = undefined;\n        }\n\n        getParsingFlags(config).parsedDateParts = config._a.slice(0);\n        getParsingFlags(config).meridiem = config._meridiem;\n        // handle meridiem\n        config._a[HOUR] = meridiemFixWrap(\n            config._locale,\n            config._a[HOUR],\n            config._meridiem\n        );\n\n        // handle era\n        era = getParsingFlags(config).era;\n        if (era !== null) {\n            config._a[YEAR] = config._locale.erasConvertYear(era, config._a[YEAR]);\n        }\n\n        configFromArray(config);\n        checkOverflow(config);\n    }\n\n    function meridiemFixWrap(locale, hour, meridiem) {\n        var isPm;\n\n        if (meridiem == null) {\n            // nothing to do\n            return hour;\n        }\n        if (locale.meridiemHour != null) {\n            return locale.meridiemHour(hour, meridiem);\n        } else if (locale.isPM != null) {\n            // Fallback\n            isPm = locale.isPM(meridiem);\n            if (isPm && hour < 12) {\n                hour += 12;\n            }\n            if (!isPm && hour === 12) {\n                hour = 0;\n            }\n            return hour;\n        } else {\n            // this is not supposed to happen\n            return hour;\n        }\n    }\n\n    // date from string and array of format strings\n    function configFromStringAndArray(config) {\n        var tempConfig,\n            bestMoment,\n            scoreToBeat,\n            i,\n            currentScore,\n            validFormatFound,\n            bestFormatIsValid = false;\n\n        if (config._f.length === 0) {\n            getParsingFlags(config).invalidFormat = true;\n            config._d = new Date(NaN);\n            return;\n        }\n\n        for (i = 0; i < config._f.length; i++) {\n            currentScore = 0;\n            validFormatFound = false;\n            tempConfig = copyConfig({}, config);\n            if (config._useUTC != null) {\n                tempConfig._useUTC = config._useUTC;\n            }\n            tempConfig._f = config._f[i];\n            configFromStringAndFormat(tempConfig);\n\n            if (isValid(tempConfig)) {\n                validFormatFound = true;\n            }\n\n            // if there is any input that was not parsed add a penalty for that format\n            currentScore += getParsingFlags(tempConfig).charsLeftOver;\n\n            //or tokens\n            currentScore += getParsingFlags(tempConfig).unusedTokens.length * 10;\n\n            getParsingFlags(tempConfig).score = currentScore;\n\n            if (!bestFormatIsValid) {\n                if (\n                    scoreToBeat == null ||\n                    currentScore < scoreToBeat ||\n                    validFormatFound\n                ) {\n                    scoreToBeat = currentScore;\n                    bestMoment = tempConfig;\n                    if (validFormatFound) {\n                        bestFormatIsValid = true;\n                    }\n                }\n            } else {\n                if (currentScore < scoreToBeat) {\n                    scoreToBeat = currentScore;\n                    bestMoment = tempConfig;\n                }\n            }\n        }\n\n        extend(config, bestMoment || tempConfig);\n    }\n\n    function configFromObject(config) {\n        if (config._d) {\n            return;\n        }\n\n        var i = normalizeObjectUnits(config._i),\n            dayOrDate = i.day === undefined ? i.date : i.day;\n        config._a = map(\n            [i.year, i.month, dayOrDate, i.hour, i.minute, i.second, i.millisecond],\n            function (obj) {\n                return obj && parseInt(obj, 10);\n            }\n        );\n\n        configFromArray(config);\n    }\n\n    function createFromConfig(config) {\n        var res = new Moment(checkOverflow(prepareConfig(config)));\n        if (res._nextDay) {\n            // Adding is smart enough around DST\n            res.add(1, 'd');\n            res._nextDay = undefined;\n        }\n\n        return res;\n    }\n\n    function prepareConfig(config) {\n        var input = config._i,\n            format = config._f;\n\n        config._locale = config._locale || getLocale(config._l);\n\n        if (input === null || (format === undefined && input === '')) {\n            return createInvalid({ nullInput: true });\n        }\n\n        if (typeof input === 'string') {\n            config._i = input = config._locale.preparse(input);\n        }\n\n        if (isMoment(input)) {\n            return new Moment(checkOverflow(input));\n        } else if (isDate(input)) {\n            config._d = input;\n        } else if (isArray(format)) {\n            configFromStringAndArray(config);\n        } else if (format) {\n            configFromStringAndFormat(config);\n        } else {\n            configFromInput(config);\n        }\n\n        if (!isValid(config)) {\n            config._d = null;\n        }\n\n        return config;\n    }\n\n    function configFromInput(config) {\n        var input = config._i;\n        if (isUndefined(input)) {\n            config._d = new Date(hooks.now());\n        } else if (isDate(input)) {\n            config._d = new Date(input.valueOf());\n        } else if (typeof input === 'string') {\n            configFromString(config);\n        } else if (isArray(input)) {\n            config._a = map(input.slice(0), function (obj) {\n                return parseInt(obj, 10);\n            });\n            configFromArray(config);\n        } else if (isObject(input)) {\n            configFromObject(config);\n        } else if (isNumber(input)) {\n            // from milliseconds\n            config._d = new Date(input);\n        } else {\n            hooks.createFromInputFallback(config);\n        }\n    }\n\n    function createLocalOrUTC(input, format, locale, strict, isUTC) {\n        var c = {};\n\n        if (format === true || format === false) {\n            strict = format;\n            format = undefined;\n        }\n\n        if (locale === true || locale === false) {\n            strict = locale;\n            locale = undefined;\n        }\n\n        if (\n            (isObject(input) && isObjectEmpty(input)) ||\n            (isArray(input) && input.length === 0)\n        ) {\n            input = undefined;\n        }\n        // object construction must be done this way.\n        // https://github.com/moment/moment/issues/1423\n        c._isAMomentObject = true;\n        c._useUTC = c._isUTC = isUTC;\n        c._l = locale;\n        c._i = input;\n        c._f = format;\n        c._strict = strict;\n\n        return createFromConfig(c);\n    }\n\n    function createLocal(input, format, locale, strict) {\n        return createLocalOrUTC(input, format, locale, strict, false);\n    }\n\n    var prototypeMin = deprecate(\n            'moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/',\n            function () {\n                var other = createLocal.apply(null, arguments);\n                if (this.isValid() && other.isValid()) {\n                    return other < this ? this : other;\n                } else {\n                    return createInvalid();\n                }\n            }\n        ),\n        prototypeMax = deprecate(\n            'moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/',\n            function () {\n                var other = createLocal.apply(null, arguments);\n                if (this.isValid() && other.isValid()) {\n                    return other > this ? this : other;\n                } else {\n                    return createInvalid();\n                }\n            }\n        );\n\n    // Pick a moment m from moments so that m[fn](other) is true for all\n    // other. This relies on the function fn to be transitive.\n    //\n    // moments should either be an array of moment objects or an array, whose\n    // first element is an array of moment objects.\n    function pickBy(fn, moments) {\n        var res, i;\n        if (moments.length === 1 && isArray(moments[0])) {\n            moments = moments[0];\n        }\n        if (!moments.length) {\n            return createLocal();\n        }\n        res = moments[0];\n        for (i = 1; i < moments.length; ++i) {\n            if (!moments[i].isValid() || moments[i][fn](res)) {\n                res = moments[i];\n            }\n        }\n        return res;\n    }\n\n    // TODO: Use [].sort instead?\n    function min() {\n        var args = [].slice.call(arguments, 0);\n\n        return pickBy('isBefore', args);\n    }\n\n    function max() {\n        var args = [].slice.call(arguments, 0);\n\n        return pickBy('isAfter', args);\n    }\n\n    var now = function () {\n        return Date.now ? Date.now() : +new Date();\n    };\n\n    var ordering = [\n        'year',\n        'quarter',\n        'month',\n        'week',\n        'day',\n        'hour',\n        'minute',\n        'second',\n        'millisecond',\n    ];\n\n    function isDurationValid(m) {\n        var key,\n            unitHasDecimal = false,\n            i;\n        for (key in m) {\n            if (\n                hasOwnProp(m, key) &&\n                !(\n                    indexOf.call(ordering, key) !== -1 &&\n                    (m[key] == null || !isNaN(m[key]))\n                )\n            ) {\n                return false;\n            }\n        }\n\n        for (i = 0; i < ordering.length; ++i) {\n            if (m[ordering[i]]) {\n                if (unitHasDecimal) {\n                    return false; // only allow non-integers for smallest unit\n                }\n                if (parseFloat(m[ordering[i]]) !== toInt(m[ordering[i]])) {\n                    unitHasDecimal = true;\n                }\n            }\n        }\n\n        return true;\n    }\n\n    function isValid$1() {\n        return this._isValid;\n    }\n\n    function createInvalid$1() {\n        return createDuration(NaN);\n    }\n\n    function Duration(duration) {\n        var normalizedInput = normalizeObjectUnits(duration),\n            years = normalizedInput.year || 0,\n            quarters = normalizedInput.quarter || 0,\n            months = normalizedInput.month || 0,\n            weeks = normalizedInput.week || normalizedInput.isoWeek || 0,\n            days = normalizedInput.day || 0,\n            hours = normalizedInput.hour || 0,\n            minutes = normalizedInput.minute || 0,\n            seconds = normalizedInput.second || 0,\n            milliseconds = normalizedInput.millisecond || 0;\n\n        this._isValid = isDurationValid(normalizedInput);\n\n        // representation for dateAddRemove\n        this._milliseconds =\n            +milliseconds +\n            seconds * 1e3 + // 1000\n            minutes * 6e4 + // 1000 * 60\n            hours * 1000 * 60 * 60; //using 1000 * 60 * 60 instead of 36e5 to avoid floating point rounding errors https://github.com/moment/moment/issues/2978\n        // Because of dateAddRemove treats 24 hours as different from a\n        // day when working around DST, we need to store them separately\n        this._days = +days + weeks * 7;\n        // It is impossible to translate months into days without knowing\n        // which months you are are talking about, so we have to store\n        // it separately.\n        this._months = +months + quarters * 3 + years * 12;\n\n        this._data = {};\n\n        this._locale = getLocale();\n\n        this._bubble();\n    }\n\n    function isDuration(obj) {\n        return obj instanceof Duration;\n    }\n\n    function absRound(number) {\n        if (number < 0) {\n            return Math.round(-1 * number) * -1;\n        } else {\n            return Math.round(number);\n        }\n    }\n\n    // compare two arrays, return the number of differences\n    function compareArrays(array1, array2, dontConvert) {\n        var len = Math.min(array1.length, array2.length),\n            lengthDiff = Math.abs(array1.length - array2.length),\n            diffs = 0,\n            i;\n        for (i = 0; i < len; i++) {\n            if (\n                (dontConvert && array1[i] !== array2[i]) ||\n                (!dontConvert && toInt(array1[i]) !== toInt(array2[i]))\n            ) {\n                diffs++;\n            }\n        }\n        return diffs + lengthDiff;\n    }\n\n    // FORMATTING\n\n    function offset(token, separator) {\n        addFormatToken(token, 0, 0, function () {\n            var offset = this.utcOffset(),\n                sign = '+';\n            if (offset < 0) {\n                offset = -offset;\n                sign = '-';\n            }\n            return (\n                sign +\n                zeroFill(~~(offset / 60), 2) +\n                separator +\n                zeroFill(~~offset % 60, 2)\n            );\n        });\n    }\n\n    offset('Z', ':');\n    offset('ZZ', '');\n\n    // PARSING\n\n    addRegexToken('Z', matchShortOffset);\n    addRegexToken('ZZ', matchShortOffset);\n    addParseToken(['Z', 'ZZ'], function (input, array, config) {\n        config._useUTC = true;\n        config._tzm = offsetFromString(matchShortOffset, input);\n    });\n\n    // HELPERS\n\n    // timezone chunker\n    // '+10:00' > ['10',  '00']\n    // '-1530'  > ['-15', '30']\n    var chunkOffset = /([\\+\\-]|\\d\\d)/gi;\n\n    function offsetFromString(matcher, string) {\n        var matches = (string || '').match(matcher),\n            chunk,\n            parts,\n            minutes;\n\n        if (matches === null) {\n            return null;\n        }\n\n        chunk = matches[matches.length - 1] || [];\n        parts = (chunk + '').match(chunkOffset) || ['-', 0, 0];\n        minutes = +(parts[1] * 60) + toInt(parts[2]);\n\n        return minutes === 0 ? 0 : parts[0] === '+' ? minutes : -minutes;\n    }\n\n    // Return a moment from input, that is local/utc/zone equivalent to model.\n    function cloneWithOffset(input, model) {\n        var res, diff;\n        if (model._isUTC) {\n            res = model.clone();\n            diff =\n                (isMoment(input) || isDate(input)\n                    ? input.valueOf()\n                    : createLocal(input).valueOf()) - res.valueOf();\n            // Use low-level api, because this fn is low-level api.\n            res._d.setTime(res._d.valueOf() + diff);\n            hooks.updateOffset(res, false);\n            return res;\n        } else {\n            return createLocal(input).local();\n        }\n    }\n\n    function getDateOffset(m) {\n        // On Firefox.24 Date#getTimezoneOffset returns a floating point.\n        // https://github.com/moment/moment/pull/1871\n        return -Math.round(m._d.getTimezoneOffset());\n    }\n\n    // HOOKS\n\n    // This function will be called whenever a moment is mutated.\n    // It is intended to keep the offset in sync with the timezone.\n    hooks.updateOffset = function () {};\n\n    // MOMENTS\n\n    // keepLocalTime = true means only change the timezone, without\n    // affecting the local hour. So 5:31:26 +0300 --[utcOffset(2, true)]-->\n    // 5:31:26 +0200 It is possible that 5:31:26 doesn't exist with offset\n    // +0200, so we adjust the time as needed, to be valid.\n    //\n    // Keeping the time actually adds/subtracts (one hour)\n    // from the actual represented time. That is why we call updateOffset\n    // a second time. In case it wants us to change the offset again\n    // _changeInProgress == true case, then we have to adjust, because\n    // there is no such time in the given timezone.\n    function getSetOffset(input, keepLocalTime, keepMinutes) {\n        var offset = this._offset || 0,\n            localAdjust;\n        if (!this.isValid()) {\n            return input != null ? this : NaN;\n        }\n        if (input != null) {\n            if (typeof input === 'string') {\n                input = offsetFromString(matchShortOffset, input);\n                if (input === null) {\n                    return this;\n                }\n            } else if (Math.abs(input) < 16 && !keepMinutes) {\n                input = input * 60;\n            }\n            if (!this._isUTC && keepLocalTime) {\n                localAdjust = getDateOffset(this);\n            }\n            this._offset = input;\n            this._isUTC = true;\n            if (localAdjust != null) {\n                this.add(localAdjust, 'm');\n            }\n            if (offset !== input) {\n                if (!keepLocalTime || this._changeInProgress) {\n                    addSubtract(\n                        this,\n                        createDuration(input - offset, 'm'),\n                        1,\n                        false\n                    );\n                } else if (!this._changeInProgress) {\n                    this._changeInProgress = true;\n                    hooks.updateOffset(this, true);\n                    this._changeInProgress = null;\n                }\n            }\n            return this;\n        } else {\n            return this._isUTC ? offset : getDateOffset(this);\n        }\n    }\n\n    function getSetZone(input, keepLocalTime) {\n        if (input != null) {\n            if (typeof input !== 'string') {\n                input = -input;\n            }\n\n            this.utcOffset(input, keepLocalTime);\n\n            return this;\n        } else {\n            return -this.utcOffset();\n        }\n    }\n\n    function setOffsetToUTC(keepLocalTime) {\n        return this.utcOffset(0, keepLocalTime);\n    }\n\n    function setOffsetToLocal(keepLocalTime) {\n        if (this._isUTC) {\n            this.utcOffset(0, keepLocalTime);\n            this._isUTC = false;\n\n            if (keepLocalTime) {\n                this.subtract(getDateOffset(this), 'm');\n            }\n        }\n        return this;\n    }\n\n    function setOffsetToParsedOffset() {\n        if (this._tzm != null) {\n            this.utcOffset(this._tzm, false, true);\n        } else if (typeof this._i === 'string') {\n            var tZone = offsetFromString(matchOffset, this._i);\n            if (tZone != null) {\n                this.utcOffset(tZone);\n            } else {\n                this.utcOffset(0, true);\n            }\n        }\n        return this;\n    }\n\n    function hasAlignedHourOffset(input) {\n        if (!this.isValid()) {\n            return false;\n        }\n        input = input ? createLocal(input).utcOffset() : 0;\n\n        return (this.utcOffset() - input) % 60 === 0;\n    }\n\n    function isDaylightSavingTime() {\n        return (\n            this.utcOffset() > this.clone().month(0).utcOffset() ||\n            this.utcOffset() > this.clone().month(5).utcOffset()\n        );\n    }\n\n    function isDaylightSavingTimeShifted() {\n        if (!isUndefined(this._isDSTShifted)) {\n            return this._isDSTShifted;\n        }\n\n        var c = {},\n            other;\n\n        copyConfig(c, this);\n        c = prepareConfig(c);\n\n        if (c._a) {\n            other = c._isUTC ? createUTC(c._a) : createLocal(c._a);\n            this._isDSTShifted =\n                this.isValid() && compareArrays(c._a, other.toArray()) > 0;\n        } else {\n            this._isDSTShifted = false;\n        }\n\n        return this._isDSTShifted;\n    }\n\n    function isLocal() {\n        return this.isValid() ? !this._isUTC : false;\n    }\n\n    function isUtcOffset() {\n        return this.isValid() ? this._isUTC : false;\n    }\n\n    function isUtc() {\n        return this.isValid() ? this._isUTC && this._offset === 0 : false;\n    }\n\n    // ASP.NET json date format regex\n    var aspNetRegex = /^(-|\\+)?(?:(\\d*)[. ])?(\\d+):(\\d+)(?::(\\d+)(\\.\\d*)?)?$/,\n        // from http://docs.closure-library.googlecode.com/git/closure_goog_date_date.js.source.html\n        // somewhat more in line with 4.4.3.2 2004 spec, but allows decimal anywhere\n        // and further modified to allow for strings containing both week and day\n        isoRegex = /^(-|\\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;\n\n    function createDuration(input, key) {\n        var duration = input,\n            // matching against regexp is expensive, do it on demand\n            match = null,\n            sign,\n            ret,\n            diffRes;\n\n        if (isDuration(input)) {\n            duration = {\n                ms: input._milliseconds,\n                d: input._days,\n                M: input._months,\n            };\n        } else if (isNumber(input) || !isNaN(+input)) {\n            duration = {};\n            if (key) {\n                duration[key] = +input;\n            } else {\n                duration.milliseconds = +input;\n            }\n        } else if ((match = aspNetRegex.exec(input))) {\n            sign = match[1] === '-' ? -1 : 1;\n            duration = {\n                y: 0,\n                d: toInt(match[DATE]) * sign,\n                h: toInt(match[HOUR]) * sign,\n                m: toInt(match[MINUTE]) * sign,\n                s: toInt(match[SECOND]) * sign,\n                ms: toInt(absRound(match[MILLISECOND] * 1000)) * sign, // the millisecond decimal point is included in the match\n            };\n        } else if ((match = isoRegex.exec(input))) {\n            sign = match[1] === '-' ? -1 : 1;\n            duration = {\n                y: parseIso(match[2], sign),\n                M: parseIso(match[3], sign),\n                w: parseIso(match[4], sign),\n                d: parseIso(match[5], sign),\n                h: parseIso(match[6], sign),\n                m: parseIso(match[7], sign),\n                s: parseIso(match[8], sign),\n            };\n        } else if (duration == null) {\n            // checks for null or undefined\n            duration = {};\n        } else if (\n            typeof duration === 'object' &&\n            ('from' in duration || 'to' in duration)\n        ) {\n            diffRes = momentsDifference(\n                createLocal(duration.from),\n                createLocal(duration.to)\n            );\n\n            duration = {};\n            duration.ms = diffRes.milliseconds;\n            duration.M = diffRes.months;\n        }\n\n        ret = new Duration(duration);\n\n        if (isDuration(input) && hasOwnProp(input, '_locale')) {\n            ret._locale = input._locale;\n        }\n\n        if (isDuration(input) && hasOwnProp(input, '_isValid')) {\n            ret._isValid = input._isValid;\n        }\n\n        return ret;\n    }\n\n    createDuration.fn = Duration.prototype;\n    createDuration.invalid = createInvalid$1;\n\n    function parseIso(inp, sign) {\n        // We'd normally use ~~inp for this, but unfortunately it also\n        // converts floats to ints.\n        // inp may be undefined, so careful calling replace on it.\n        var res = inp && parseFloat(inp.replace(',', '.'));\n        // apply sign while we're at it\n        return (isNaN(res) ? 0 : res) * sign;\n    }\n\n    function positiveMomentsDifference(base, other) {\n        var res = {};\n\n        res.months =\n            other.month() - base.month() + (other.year() - base.year()) * 12;\n        if (base.clone().add(res.months, 'M').isAfter(other)) {\n            --res.months;\n        }\n\n        res.milliseconds = +other - +base.clone().add(res.months, 'M');\n\n        return res;\n    }\n\n    function momentsDifference(base, other) {\n        var res;\n        if (!(base.isValid() && other.isValid())) {\n            return { milliseconds: 0, months: 0 };\n        }\n\n        other = cloneWithOffset(other, base);\n        if (base.isBefore(other)) {\n            res = positiveMomentsDifference(base, other);\n        } else {\n            res = positiveMomentsDifference(other, base);\n            res.milliseconds = -res.milliseconds;\n            res.months = -res.months;\n        }\n\n        return res;\n    }\n\n    // TODO: remove 'name' arg after deprecation is removed\n    function createAdder(direction, name) {\n        return function (val, period) {\n            var dur, tmp;\n            //invert the arguments, but complain about it\n            if (period !== null && !isNaN(+period)) {\n                deprecateSimple(\n                    name,\n                    'moment().' +\n                        name +\n                        '(period, number) is deprecated. Please use moment().' +\n                        name +\n                        '(number, period). ' +\n                        'See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info.'\n                );\n                tmp = val;\n                val = period;\n                period = tmp;\n            }\n\n            dur = createDuration(val, period);\n            addSubtract(this, dur, direction);\n            return this;\n        };\n    }\n\n    function addSubtract(mom, duration, isAdding, updateOffset) {\n        var milliseconds = duration._milliseconds,\n            days = absRound(duration._days),\n            months = absRound(duration._months);\n\n        if (!mom.isValid()) {\n            // No op\n            return;\n        }\n\n        updateOffset = updateOffset == null ? true : updateOffset;\n\n        if (months) {\n            setMonth(mom, get(mom, 'Month') + months * isAdding);\n        }\n        if (days) {\n            set$1(mom, 'Date', get(mom, 'Date') + days * isAdding);\n        }\n        if (milliseconds) {\n            mom._d.setTime(mom._d.valueOf() + milliseconds * isAdding);\n        }\n        if (updateOffset) {\n            hooks.updateOffset(mom, days || months);\n        }\n    }\n\n    var add = createAdder(1, 'add'),\n        subtract = createAdder(-1, 'subtract');\n\n    function isString(input) {\n        return typeof input === 'string' || input instanceof String;\n    }\n\n    // type MomentInput = Moment | Date | string | number | (number | string)[] | MomentInputObject | void; // null | undefined\n    function isMomentInput(input) {\n        return (\n            isMoment(input) ||\n            isDate(input) ||\n            isString(input) ||\n            isNumber(input) ||\n            isNumberOrStringArray(input) ||\n            isMomentInputObject(input) ||\n            input === null ||\n            input === undefined\n        );\n    }\n\n    function isMomentInputObject(input) {\n        var objectTest = isObject(input) && !isObjectEmpty(input),\n            propertyTest = false,\n            properties = [\n                'years',\n                'year',\n                'y',\n                'months',\n                'month',\n                'M',\n                'days',\n                'day',\n                'd',\n                'dates',\n                'date',\n                'D',\n                'hours',\n                'hour',\n                'h',\n                'minutes',\n                'minute',\n                'm',\n                'seconds',\n                'second',\n                's',\n                'milliseconds',\n                'millisecond',\n                'ms',\n            ],\n            i,\n            property;\n\n        for (i = 0; i < properties.length; i += 1) {\n            property = properties[i];\n            propertyTest = propertyTest || hasOwnProp(input, property);\n        }\n\n        return objectTest && propertyTest;\n    }\n\n    function isNumberOrStringArray(input) {\n        var arrayTest = isArray(input),\n            dataTypeTest = false;\n        if (arrayTest) {\n            dataTypeTest =\n                input.filter(function (item) {\n                    return !isNumber(item) && isString(input);\n                }).length === 0;\n        }\n        return arrayTest && dataTypeTest;\n    }\n\n    function isCalendarSpec(input) {\n        var objectTest = isObject(input) && !isObjectEmpty(input),\n            propertyTest = false,\n            properties = [\n                'sameDay',\n                'nextDay',\n                'lastDay',\n                'nextWeek',\n                'lastWeek',\n                'sameElse',\n            ],\n            i,\n            property;\n\n        for (i = 0; i < properties.length; i += 1) {\n            property = properties[i];\n            propertyTest = propertyTest || hasOwnProp(input, property);\n        }\n\n        return objectTest && propertyTest;\n    }\n\n    function getCalendarFormat(myMoment, now) {\n        var diff = myMoment.diff(now, 'days', true);\n        return diff < -6\n            ? 'sameElse'\n            : diff < -1\n            ? 'lastWeek'\n            : diff < 0\n            ? 'lastDay'\n            : diff < 1\n            ? 'sameDay'\n            : diff < 2\n            ? 'nextDay'\n            : diff < 7\n            ? 'nextWeek'\n            : 'sameElse';\n    }\n\n    function calendar$1(time, formats) {\n        // Support for single parameter, formats only overload to the calendar function\n        if (arguments.length === 1) {\n            if (!arguments[0]) {\n                time = undefined;\n                formats = undefined;\n            } else if (isMomentInput(arguments[0])) {\n                time = arguments[0];\n                formats = undefined;\n            } else if (isCalendarSpec(arguments[0])) {\n                formats = arguments[0];\n                time = undefined;\n            }\n        }\n        // We want to compare the start of today, vs this.\n        // Getting start-of-today depends on whether we're local/utc/offset or not.\n        var now = time || createLocal(),\n            sod = cloneWithOffset(now, this).startOf('day'),\n            format = hooks.calendarFormat(this, sod) || 'sameElse',\n            output =\n                formats &&\n                (isFunction(formats[format])\n                    ? formats[format].call(this, now)\n                    : formats[format]);\n\n        return this.format(\n            output || this.localeData().calendar(format, this, createLocal(now))\n        );\n    }\n\n    function clone() {\n        return new Moment(this);\n    }\n\n    function isAfter(input, units) {\n        var localInput = isMoment(input) ? input : createLocal(input);\n        if (!(this.isValid() && localInput.isValid())) {\n            return false;\n        }\n        units = normalizeUnits(units) || 'millisecond';\n        if (units === 'millisecond') {\n            return this.valueOf() > localInput.valueOf();\n        } else {\n            return localInput.valueOf() < this.clone().startOf(units).valueOf();\n        }\n    }\n\n    function isBefore(input, units) {\n        var localInput = isMoment(input) ? input : createLocal(input);\n        if (!(this.isValid() && localInput.isValid())) {\n            return false;\n        }\n        units = normalizeUnits(units) || 'millisecond';\n        if (units === 'millisecond') {\n            return this.valueOf() < localInput.valueOf();\n        } else {\n            return this.clone().endOf(units).valueOf() < localInput.valueOf();\n        }\n    }\n\n    function isBetween(from, to, units, inclusivity) {\n        var localFrom = isMoment(from) ? from : createLocal(from),\n            localTo = isMoment(to) ? to : createLocal(to);\n        if (!(this.isValid() && localFrom.isValid() && localTo.isValid())) {\n            return false;\n        }\n        inclusivity = inclusivity || '()';\n        return (\n            (inclusivity[0] === '('\n                ? this.isAfter(localFrom, units)\n                : !this.isBefore(localFrom, units)) &&\n            (inclusivity[1] === ')'\n                ? this.isBefore(localTo, units)\n                : !this.isAfter(localTo, units))\n        );\n    }\n\n    function isSame(input, units) {\n        var localInput = isMoment(input) ? input : createLocal(input),\n            inputMs;\n        if (!(this.isValid() && localInput.isValid())) {\n            return false;\n        }\n        units = normalizeUnits(units) || 'millisecond';\n        if (units === 'millisecond') {\n            return this.valueOf() === localInput.valueOf();\n        } else {\n            inputMs = localInput.valueOf();\n            return (\n                this.clone().startOf(units).valueOf() <= inputMs &&\n                inputMs <= this.clone().endOf(units).valueOf()\n            );\n        }\n    }\n\n    function isSameOrAfter(input, units) {\n        return this.isSame(input, units) || this.isAfter(input, units);\n    }\n\n    function isSameOrBefore(input, units) {\n        return this.isSame(input, units) || this.isBefore(input, units);\n    }\n\n    function diff(input, units, asFloat) {\n        var that, zoneDelta, output;\n\n        if (!this.isValid()) {\n            return NaN;\n        }\n\n        that = cloneWithOffset(input, this);\n\n        if (!that.isValid()) {\n            return NaN;\n        }\n\n        zoneDelta = (that.utcOffset() - this.utcOffset()) * 6e4;\n\n        units = normalizeUnits(units);\n\n        switch (units) {\n            case 'year':\n                output = monthDiff(this, that) / 12;\n                break;\n            case 'month':\n                output = monthDiff(this, that);\n                break;\n            case 'quarter':\n                output = monthDiff(this, that) / 3;\n                break;\n            case 'second':\n                output = (this - that) / 1e3;\n                break; // 1000\n            case 'minute':\n                output = (this - that) / 6e4;\n                break; // 1000 * 60\n            case 'hour':\n                output = (this - that) / 36e5;\n                break; // 1000 * 60 * 60\n            case 'day':\n                output = (this - that - zoneDelta) / 864e5;\n                break; // 1000 * 60 * 60 * 24, negate dst\n            case 'week':\n                output = (this - that - zoneDelta) / 6048e5;\n                break; // 1000 * 60 * 60 * 24 * 7, negate dst\n            default:\n                output = this - that;\n        }\n\n        return asFloat ? output : absFloor(output);\n    }\n\n    function monthDiff(a, b) {\n        if (a.date() < b.date()) {\n            // end-of-month calculations work correct when the start month has more\n            // days than the end month.\n            return -monthDiff(b, a);\n        }\n        // difference in months\n        var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month()),\n            // b is in (anchor - 1 month, anchor + 1 month)\n            anchor = a.clone().add(wholeMonthDiff, 'months'),\n            anchor2,\n            adjust;\n\n        if (b - anchor < 0) {\n            anchor2 = a.clone().add(wholeMonthDiff - 1, 'months');\n            // linear across the month\n            adjust = (b - anchor) / (anchor - anchor2);\n        } else {\n            anchor2 = a.clone().add(wholeMonthDiff + 1, 'months');\n            // linear across the month\n            adjust = (b - anchor) / (anchor2 - anchor);\n        }\n\n        //check for negative zero, return zero if negative zero\n        return -(wholeMonthDiff + adjust) || 0;\n    }\n\n    hooks.defaultFormat = 'YYYY-MM-DDTHH:mm:ssZ';\n    hooks.defaultFormatUtc = 'YYYY-MM-DDTHH:mm:ss[Z]';\n\n    function toString() {\n        return this.clone().locale('en').format('ddd MMM DD YYYY HH:mm:ss [GMT]ZZ');\n    }\n\n    function toISOString(keepOffset) {\n        if (!this.isValid()) {\n            return null;\n        }\n        var utc = keepOffset !== true,\n            m = utc ? this.clone().utc() : this;\n        if (m.year() < 0 || m.year() > 9999) {\n            return formatMoment(\n                m,\n                utc\n                    ? 'YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]'\n                    : 'YYYYYY-MM-DD[T]HH:mm:ss.SSSZ'\n            );\n        }\n        if (isFunction(Date.prototype.toISOString)) {\n            // native implementation is ~50x faster, use it when we can\n            if (utc) {\n                return this.toDate().toISOString();\n            } else {\n                return new Date(this.valueOf() + this.utcOffset() * 60 * 1000)\n                    .toISOString()\n                    .replace('Z', formatMoment(m, 'Z'));\n            }\n        }\n        return formatMoment(\n            m,\n            utc ? 'YYYY-MM-DD[T]HH:mm:ss.SSS[Z]' : 'YYYY-MM-DD[T]HH:mm:ss.SSSZ'\n        );\n    }\n\n    /**\n     * Return a human readable representation of a moment that can\n     * also be evaluated to get a new moment which is the same\n     *\n     * @link https://nodejs.org/dist/latest/docs/api/util.html#util_custom_inspect_function_on_objects\n     */\n    function inspect() {\n        if (!this.isValid()) {\n            return 'moment.invalid(/* ' + this._i + ' */)';\n        }\n        var func = 'moment',\n            zone = '',\n            prefix,\n            year,\n            datetime,\n            suffix;\n        if (!this.isLocal()) {\n            func = this.utcOffset() === 0 ? 'moment.utc' : 'moment.parseZone';\n            zone = 'Z';\n        }\n        prefix = '[' + func + '(\"]';\n        year = 0 <= this.year() && this.year() <= 9999 ? 'YYYY' : 'YYYYYY';\n        datetime = '-MM-DD[T]HH:mm:ss.SSS';\n        suffix = zone + '[\")]';\n\n        return this.format(prefix + year + datetime + suffix);\n    }\n\n    function format(inputString) {\n        if (!inputString) {\n            inputString = this.isUtc()\n                ? hooks.defaultFormatUtc\n                : hooks.defaultFormat;\n        }\n        var output = formatMoment(this, inputString);\n        return this.localeData().postformat(output);\n    }\n\n    function from(time, withoutSuffix) {\n        if (\n            this.isValid() &&\n            ((isMoment(time) && time.isValid()) || createLocal(time).isValid())\n        ) {\n            return createDuration({ to: this, from: time })\n                .locale(this.locale())\n                .humanize(!withoutSuffix);\n        } else {\n            return this.localeData().invalidDate();\n        }\n    }\n\n    function fromNow(withoutSuffix) {\n        return this.from(createLocal(), withoutSuffix);\n    }\n\n    function to(time, withoutSuffix) {\n        if (\n            this.isValid() &&\n            ((isMoment(time) && time.isValid()) || createLocal(time).isValid())\n        ) {\n            return createDuration({ from: this, to: time })\n                .locale(this.locale())\n                .humanize(!withoutSuffix);\n        } else {\n            return this.localeData().invalidDate();\n        }\n    }\n\n    function toNow(withoutSuffix) {\n        return this.to(createLocal(), withoutSuffix);\n    }\n\n    // If passed a locale key, it will set the locale for this\n    // instance.  Otherwise, it will return the locale configuration\n    // variables for this instance.\n    function locale(key) {\n        var newLocaleData;\n\n        if (key === undefined) {\n            return this._locale._abbr;\n        } else {\n            newLocaleData = getLocale(key);\n            if (newLocaleData != null) {\n                this._locale = newLocaleData;\n            }\n            return this;\n        }\n    }\n\n    var lang = deprecate(\n        'moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.',\n        function (key) {\n            if (key === undefined) {\n                return this.localeData();\n            } else {\n                return this.locale(key);\n            }\n        }\n    );\n\n    function localeData() {\n        return this._locale;\n    }\n\n    var MS_PER_SECOND = 1000,\n        MS_PER_MINUTE = 60 * MS_PER_SECOND,\n        MS_PER_HOUR = 60 * MS_PER_MINUTE,\n        MS_PER_400_YEARS = (365 * 400 + 97) * 24 * MS_PER_HOUR;\n\n    // actual modulo - handles negative numbers (for dates before 1970):\n    function mod$1(dividend, divisor) {\n        return ((dividend % divisor) + divisor) % divisor;\n    }\n\n    function localStartOfDate(y, m, d) {\n        // the date constructor remaps years 0-99 to 1900-1999\n        if (y < 100 && y >= 0) {\n            // preserve leap years using a full 400 year cycle, then reset\n            return new Date(y + 400, m, d) - MS_PER_400_YEARS;\n        } else {\n            return new Date(y, m, d).valueOf();\n        }\n    }\n\n    function utcStartOfDate(y, m, d) {\n        // Date.UTC remaps years 0-99 to 1900-1999\n        if (y < 100 && y >= 0) {\n            // preserve leap years using a full 400 year cycle, then reset\n            return Date.UTC(y + 400, m, d) - MS_PER_400_YEARS;\n        } else {\n            return Date.UTC(y, m, d);\n        }\n    }\n\n    function startOf(units) {\n        var time, startOfDate;\n        units = normalizeUnits(units);\n        if (units === undefined || units === 'millisecond' || !this.isValid()) {\n            return this;\n        }\n\n        startOfDate = this._isUTC ? utcStartOfDate : localStartOfDate;\n\n        switch (units) {\n            case 'year':\n                time = startOfDate(this.year(), 0, 1);\n                break;\n            case 'quarter':\n                time = startOfDate(\n                    this.year(),\n                    this.month() - (this.month() % 3),\n                    1\n                );\n                break;\n            case 'month':\n                time = startOfDate(this.year(), this.month(), 1);\n                break;\n            case 'week':\n                time = startOfDate(\n                    this.year(),\n                    this.month(),\n                    this.date() - this.weekday()\n                );\n                break;\n            case 'isoWeek':\n                time = startOfDate(\n                    this.year(),\n                    this.month(),\n                    this.date() - (this.isoWeekday() - 1)\n                );\n                break;\n            case 'day':\n            case 'date':\n                time = startOfDate(this.year(), this.month(), this.date());\n                break;\n            case 'hour':\n                time = this._d.valueOf();\n                time -= mod$1(\n                    time + (this._isUTC ? 0 : this.utcOffset() * MS_PER_MINUTE),\n                    MS_PER_HOUR\n                );\n                break;\n            case 'minute':\n                time = this._d.valueOf();\n                time -= mod$1(time, MS_PER_MINUTE);\n                break;\n            case 'second':\n                time = this._d.valueOf();\n                time -= mod$1(time, MS_PER_SECOND);\n                break;\n        }\n\n        this._d.setTime(time);\n        hooks.updateOffset(this, true);\n        return this;\n    }\n\n    function endOf(units) {\n        var time, startOfDate;\n        units = normalizeUnits(units);\n        if (units === undefined || units === 'millisecond' || !this.isValid()) {\n            return this;\n        }\n\n        startOfDate = this._isUTC ? utcStartOfDate : localStartOfDate;\n\n        switch (units) {\n            case 'year':\n                time = startOfDate(this.year() + 1, 0, 1) - 1;\n                break;\n            case 'quarter':\n                time =\n                    startOfDate(\n                        this.year(),\n                        this.month() - (this.month() % 3) + 3,\n                        1\n                    ) - 1;\n                break;\n            case 'month':\n                time = startOfDate(this.year(), this.month() + 1, 1) - 1;\n                break;\n            case 'week':\n                time =\n                    startOfDate(\n                        this.year(),\n                        this.month(),\n                        this.date() - this.weekday() + 7\n                    ) - 1;\n                break;\n            case 'isoWeek':\n                time =\n                    startOfDate(\n                        this.year(),\n                        this.month(),\n                        this.date() - (this.isoWeekday() - 1) + 7\n                    ) - 1;\n                break;\n            case 'day':\n            case 'date':\n                time = startOfDate(this.year(), this.month(), this.date() + 1) - 1;\n                break;\n            case 'hour':\n                time = this._d.valueOf();\n                time +=\n                    MS_PER_HOUR -\n                    mod$1(\n                        time + (this._isUTC ? 0 : this.utcOffset() * MS_PER_MINUTE),\n                        MS_PER_HOUR\n                    ) -\n                    1;\n                break;\n            case 'minute':\n                time = this._d.valueOf();\n                time += MS_PER_MINUTE - mod$1(time, MS_PER_MINUTE) - 1;\n                break;\n            case 'second':\n                time = this._d.valueOf();\n                time += MS_PER_SECOND - mod$1(time, MS_PER_SECOND) - 1;\n                break;\n        }\n\n        this._d.setTime(time);\n        hooks.updateOffset(this, true);\n        return this;\n    }\n\n    function valueOf() {\n        return this._d.valueOf() - (this._offset || 0) * 60000;\n    }\n\n    function unix() {\n        return Math.floor(this.valueOf() / 1000);\n    }\n\n    function toDate() {\n        return new Date(this.valueOf());\n    }\n\n    function toArray() {\n        var m = this;\n        return [\n            m.year(),\n            m.month(),\n            m.date(),\n            m.hour(),\n            m.minute(),\n            m.second(),\n            m.millisecond(),\n        ];\n    }\n\n    function toObject() {\n        var m = this;\n        return {\n            years: m.year(),\n            months: m.month(),\n            date: m.date(),\n            hours: m.hours(),\n            minutes: m.minutes(),\n            seconds: m.seconds(),\n            milliseconds: m.milliseconds(),\n        };\n    }\n\n    function toJSON() {\n        // new Date(NaN).toJSON() === null\n        return this.isValid() ? this.toISOString() : null;\n    }\n\n    function isValid$2() {\n        return isValid(this);\n    }\n\n    function parsingFlags() {\n        return extend({}, getParsingFlags(this));\n    }\n\n    function invalidAt() {\n        return getParsingFlags(this).overflow;\n    }\n\n    function creationData() {\n        return {\n            input: this._i,\n            format: this._f,\n            locale: this._locale,\n            isUTC: this._isUTC,\n            strict: this._strict,\n        };\n    }\n\n    addFormatToken('N', 0, 0, 'eraAbbr');\n    addFormatToken('NN', 0, 0, 'eraAbbr');\n    addFormatToken('NNN', 0, 0, 'eraAbbr');\n    addFormatToken('NNNN', 0, 0, 'eraName');\n    addFormatToken('NNNNN', 0, 0, 'eraNarrow');\n\n    addFormatToken('y', ['y', 1], 'yo', 'eraYear');\n    addFormatToken('y', ['yy', 2], 0, 'eraYear');\n    addFormatToken('y', ['yyy', 3], 0, 'eraYear');\n    addFormatToken('y', ['yyyy', 4], 0, 'eraYear');\n\n    addRegexToken('N', matchEraAbbr);\n    addRegexToken('NN', matchEraAbbr);\n    addRegexToken('NNN', matchEraAbbr);\n    addRegexToken('NNNN', matchEraName);\n    addRegexToken('NNNNN', matchEraNarrow);\n\n    addParseToken(['N', 'NN', 'NNN', 'NNNN', 'NNNNN'], function (\n        input,\n        array,\n        config,\n        token\n    ) {\n        var era = config._locale.erasParse(input, token, config._strict);\n        if (era) {\n            getParsingFlags(config).era = era;\n        } else {\n            getParsingFlags(config).invalidEra = input;\n        }\n    });\n\n    addRegexToken('y', matchUnsigned);\n    addRegexToken('yy', matchUnsigned);\n    addRegexToken('yyy', matchUnsigned);\n    addRegexToken('yyyy', matchUnsigned);\n    addRegexToken('yo', matchEraYearOrdinal);\n\n    addParseToken(['y', 'yy', 'yyy', 'yyyy'], YEAR);\n    addParseToken(['yo'], function (input, array, config, token) {\n        var match;\n        if (config._locale._eraYearOrdinalRegex) {\n            match = input.match(config._locale._eraYearOrdinalRegex);\n        }\n\n        if (config._locale.eraYearOrdinalParse) {\n            array[YEAR] = config._locale.eraYearOrdinalParse(input, match);\n        } else {\n            array[YEAR] = parseInt(input, 10);\n        }\n    });\n\n    function localeEras(m, format) {\n        var i,\n            l,\n            date,\n            eras = this._eras || getLocale('en')._eras;\n        for (i = 0, l = eras.length; i < l; ++i) {\n            switch (typeof eras[i].since) {\n                case 'string':\n                    // truncate time\n                    date = hooks(eras[i].since).startOf('day');\n                    eras[i].since = date.valueOf();\n                    break;\n            }\n\n            switch (typeof eras[i].until) {\n                case 'undefined':\n                    eras[i].until = +Infinity;\n                    break;\n                case 'string':\n                    // truncate time\n                    date = hooks(eras[i].until).startOf('day').valueOf();\n                    eras[i].until = date.valueOf();\n                    break;\n            }\n        }\n        return eras;\n    }\n\n    function localeErasParse(eraName, format, strict) {\n        var i,\n            l,\n            eras = this.eras(),\n            name,\n            abbr,\n            narrow;\n        eraName = eraName.toUpperCase();\n\n        for (i = 0, l = eras.length; i < l; ++i) {\n            name = eras[i].name.toUpperCase();\n            abbr = eras[i].abbr.toUpperCase();\n            narrow = eras[i].narrow.toUpperCase();\n\n            if (strict) {\n                switch (format) {\n                    case 'N':\n                    case 'NN':\n                    case 'NNN':\n                        if (abbr === eraName) {\n                            return eras[i];\n                        }\n                        break;\n\n                    case 'NNNN':\n                        if (name === eraName) {\n                            return eras[i];\n                        }\n                        break;\n\n                    case 'NNNNN':\n                        if (narrow === eraName) {\n                            return eras[i];\n                        }\n                        break;\n                }\n            } else if ([name, abbr, narrow].indexOf(eraName) >= 0) {\n                return eras[i];\n            }\n        }\n    }\n\n    function localeErasConvertYear(era, year) {\n        var dir = era.since <= era.until ? +1 : -1;\n        if (year === undefined) {\n            return hooks(era.since).year();\n        } else {\n            return hooks(era.since).year() + (year - era.offset) * dir;\n        }\n    }\n\n    function getEraName() {\n        var i,\n            l,\n            val,\n            eras = this.localeData().eras();\n        for (i = 0, l = eras.length; i < l; ++i) {\n            // truncate time\n            val = this.clone().startOf('day').valueOf();\n\n            if (eras[i].since <= val && val <= eras[i].until) {\n                return eras[i].name;\n            }\n            if (eras[i].until <= val && val <= eras[i].since) {\n                return eras[i].name;\n            }\n        }\n\n        return '';\n    }\n\n    function getEraNarrow() {\n        var i,\n            l,\n            val,\n            eras = this.localeData().eras();\n        for (i = 0, l = eras.length; i < l; ++i) {\n            // truncate time\n            val = this.clone().startOf('day').valueOf();\n\n            if (eras[i].since <= val && val <= eras[i].until) {\n                return eras[i].narrow;\n            }\n            if (eras[i].until <= val && val <= eras[i].since) {\n                return eras[i].narrow;\n            }\n        }\n\n        return '';\n    }\n\n    function getEraAbbr() {\n        var i,\n            l,\n            val,\n            eras = this.localeData().eras();\n        for (i = 0, l = eras.length; i < l; ++i) {\n            // truncate time\n            val = this.clone().startOf('day').valueOf();\n\n            if (eras[i].since <= val && val <= eras[i].until) {\n                return eras[i].abbr;\n            }\n            if (eras[i].until <= val && val <= eras[i].since) {\n                return eras[i].abbr;\n            }\n        }\n\n        return '';\n    }\n\n    function getEraYear() {\n        var i,\n            l,\n            dir,\n            val,\n            eras = this.localeData().eras();\n        for (i = 0, l = eras.length; i < l; ++i) {\n            dir = eras[i].since <= eras[i].until ? +1 : -1;\n\n            // truncate time\n            val = this.clone().startOf('day').valueOf();\n\n            if (\n                (eras[i].since <= val && val <= eras[i].until) ||\n                (eras[i].until <= val && val <= eras[i].since)\n            ) {\n                return (\n                    (this.year() - hooks(eras[i].since).year()) * dir +\n                    eras[i].offset\n                );\n            }\n        }\n\n        return this.year();\n    }\n\n    function erasNameRegex(isStrict) {\n        if (!hasOwnProp(this, '_erasNameRegex')) {\n            computeErasParse.call(this);\n        }\n        return isStrict ? this._erasNameRegex : this._erasRegex;\n    }\n\n    function erasAbbrRegex(isStrict) {\n        if (!hasOwnProp(this, '_erasAbbrRegex')) {\n            computeErasParse.call(this);\n        }\n        return isStrict ? this._erasAbbrRegex : this._erasRegex;\n    }\n\n    function erasNarrowRegex(isStrict) {\n        if (!hasOwnProp(this, '_erasNarrowRegex')) {\n            computeErasParse.call(this);\n        }\n        return isStrict ? this._erasNarrowRegex : this._erasRegex;\n    }\n\n    function matchEraAbbr(isStrict, locale) {\n        return locale.erasAbbrRegex(isStrict);\n    }\n\n    function matchEraName(isStrict, locale) {\n        return locale.erasNameRegex(isStrict);\n    }\n\n    function matchEraNarrow(isStrict, locale) {\n        return locale.erasNarrowRegex(isStrict);\n    }\n\n    function matchEraYearOrdinal(isStrict, locale) {\n        return locale._eraYearOrdinalRegex || matchUnsigned;\n    }\n\n    function computeErasParse() {\n        var abbrPieces = [],\n            namePieces = [],\n            narrowPieces = [],\n            mixedPieces = [],\n            i,\n            l,\n            eras = this.eras();\n\n        for (i = 0, l = eras.length; i < l; ++i) {\n            namePieces.push(regexEscape(eras[i].name));\n            abbrPieces.push(regexEscape(eras[i].abbr));\n            narrowPieces.push(regexEscape(eras[i].narrow));\n\n            mixedPieces.push(regexEscape(eras[i].name));\n            mixedPieces.push(regexEscape(eras[i].abbr));\n            mixedPieces.push(regexEscape(eras[i].narrow));\n        }\n\n        this._erasRegex = new RegExp('^(' + mixedPieces.join('|') + ')', 'i');\n        this._erasNameRegex = new RegExp('^(' + namePieces.join('|') + ')', 'i');\n        this._erasAbbrRegex = new RegExp('^(' + abbrPieces.join('|') + ')', 'i');\n        this._erasNarrowRegex = new RegExp(\n            '^(' + narrowPieces.join('|') + ')',\n            'i'\n        );\n    }\n\n    // FORMATTING\n\n    addFormatToken(0, ['gg', 2], 0, function () {\n        return this.weekYear() % 100;\n    });\n\n    addFormatToken(0, ['GG', 2], 0, function () {\n        return this.isoWeekYear() % 100;\n    });\n\n    function addWeekYearFormatToken(token, getter) {\n        addFormatToken(0, [token, token.length], 0, getter);\n    }\n\n    addWeekYearFormatToken('gggg', 'weekYear');\n    addWeekYearFormatToken('ggggg', 'weekYear');\n    addWeekYearFormatToken('GGGG', 'isoWeekYear');\n    addWeekYearFormatToken('GGGGG', 'isoWeekYear');\n\n    // ALIASES\n\n    addUnitAlias('weekYear', 'gg');\n    addUnitAlias('isoWeekYear', 'GG');\n\n    // PRIORITY\n\n    addUnitPriority('weekYear', 1);\n    addUnitPriority('isoWeekYear', 1);\n\n    // PARSING\n\n    addRegexToken('G', matchSigned);\n    addRegexToken('g', matchSigned);\n    addRegexToken('GG', match1to2, match2);\n    addRegexToken('gg', match1to2, match2);\n    addRegexToken('GGGG', match1to4, match4);\n    addRegexToken('gggg', match1to4, match4);\n    addRegexToken('GGGGG', match1to6, match6);\n    addRegexToken('ggggg', match1to6, match6);\n\n    addWeekParseToken(['gggg', 'ggggg', 'GGGG', 'GGGGG'], function (\n        input,\n        week,\n        config,\n        token\n    ) {\n        week[token.substr(0, 2)] = toInt(input);\n    });\n\n    addWeekParseToken(['gg', 'GG'], function (input, week, config, token) {\n        week[token] = hooks.parseTwoDigitYear(input);\n    });\n\n    // MOMENTS\n\n    function getSetWeekYear(input) {\n        return getSetWeekYearHelper.call(\n            this,\n            input,\n            this.week(),\n            this.weekday(),\n            this.localeData()._week.dow,\n            this.localeData()._week.doy\n        );\n    }\n\n    function getSetISOWeekYear(input) {\n        return getSetWeekYearHelper.call(\n            this,\n            input,\n            this.isoWeek(),\n            this.isoWeekday(),\n            1,\n            4\n        );\n    }\n\n    function getISOWeeksInYear() {\n        return weeksInYear(this.year(), 1, 4);\n    }\n\n    function getISOWeeksInISOWeekYear() {\n        return weeksInYear(this.isoWeekYear(), 1, 4);\n    }\n\n    function getWeeksInYear() {\n        var weekInfo = this.localeData()._week;\n        return weeksInYear(this.year(), weekInfo.dow, weekInfo.doy);\n    }\n\n    function getWeeksInWeekYear() {\n        var weekInfo = this.localeData()._week;\n        return weeksInYear(this.weekYear(), weekInfo.dow, weekInfo.doy);\n    }\n\n    function getSetWeekYearHelper(input, week, weekday, dow, doy) {\n        var weeksTarget;\n        if (input == null) {\n            return weekOfYear(this, dow, doy).year;\n        } else {\n            weeksTarget = weeksInYear(input, dow, doy);\n            if (week > weeksTarget) {\n                week = weeksTarget;\n            }\n            return setWeekAll.call(this, input, week, weekday, dow, doy);\n        }\n    }\n\n    function setWeekAll(weekYear, week, weekday, dow, doy) {\n        var dayOfYearData = dayOfYearFromWeeks(weekYear, week, weekday, dow, doy),\n            date = createUTCDate(dayOfYearData.year, 0, dayOfYearData.dayOfYear);\n\n        this.year(date.getUTCFullYear());\n        this.month(date.getUTCMonth());\n        this.date(date.getUTCDate());\n        return this;\n    }\n\n    // FORMATTING\n\n    addFormatToken('Q', 0, 'Qo', 'quarter');\n\n    // ALIASES\n\n    addUnitAlias('quarter', 'Q');\n\n    // PRIORITY\n\n    addUnitPriority('quarter', 7);\n\n    // PARSING\n\n    addRegexToken('Q', match1);\n    addParseToken('Q', function (input, array) {\n        array[MONTH] = (toInt(input) - 1) * 3;\n    });\n\n    // MOMENTS\n\n    function getSetQuarter(input) {\n        return input == null\n            ? Math.ceil((this.month() + 1) / 3)\n            : this.month((input - 1) * 3 + (this.month() % 3));\n    }\n\n    // FORMATTING\n\n    addFormatToken('D', ['DD', 2], 'Do', 'date');\n\n    // ALIASES\n\n    addUnitAlias('date', 'D');\n\n    // PRIORITY\n    addUnitPriority('date', 9);\n\n    // PARSING\n\n    addRegexToken('D', match1to2);\n    addRegexToken('DD', match1to2, match2);\n    addRegexToken('Do', function (isStrict, locale) {\n        // TODO: Remove \"ordinalParse\" fallback in next major release.\n        return isStrict\n            ? locale._dayOfMonthOrdinalParse || locale._ordinalParse\n            : locale._dayOfMonthOrdinalParseLenient;\n    });\n\n    addParseToken(['D', 'DD'], DATE);\n    addParseToken('Do', function (input, array) {\n        array[DATE] = toInt(input.match(match1to2)[0]);\n    });\n\n    // MOMENTS\n\n    var getSetDayOfMonth = makeGetSet('Date', true);\n\n    // FORMATTING\n\n    addFormatToken('DDD', ['DDDD', 3], 'DDDo', 'dayOfYear');\n\n    // ALIASES\n\n    addUnitAlias('dayOfYear', 'DDD');\n\n    // PRIORITY\n    addUnitPriority('dayOfYear', 4);\n\n    // PARSING\n\n    addRegexToken('DDD', match1to3);\n    addRegexToken('DDDD', match3);\n    addParseToken(['DDD', 'DDDD'], function (input, array, config) {\n        config._dayOfYear = toInt(input);\n    });\n\n    // HELPERS\n\n    // MOMENTS\n\n    function getSetDayOfYear(input) {\n        var dayOfYear =\n            Math.round(\n                (this.clone().startOf('day') - this.clone().startOf('year')) / 864e5\n            ) + 1;\n        return input == null ? dayOfYear : this.add(input - dayOfYear, 'd');\n    }\n\n    // FORMATTING\n\n    addFormatToken('m', ['mm', 2], 0, 'minute');\n\n    // ALIASES\n\n    addUnitAlias('minute', 'm');\n\n    // PRIORITY\n\n    addUnitPriority('minute', 14);\n\n    // PARSING\n\n    addRegexToken('m', match1to2);\n    addRegexToken('mm', match1to2, match2);\n    addParseToken(['m', 'mm'], MINUTE);\n\n    // MOMENTS\n\n    var getSetMinute = makeGetSet('Minutes', false);\n\n    // FORMATTING\n\n    addFormatToken('s', ['ss', 2], 0, 'second');\n\n    // ALIASES\n\n    addUnitAlias('second', 's');\n\n    // PRIORITY\n\n    addUnitPriority('second', 15);\n\n    // PARSING\n\n    addRegexToken('s', match1to2);\n    addRegexToken('ss', match1to2, match2);\n    addParseToken(['s', 'ss'], SECOND);\n\n    // MOMENTS\n\n    var getSetSecond = makeGetSet('Seconds', false);\n\n    // FORMATTING\n\n    addFormatToken('S', 0, 0, function () {\n        return ~~(this.millisecond() / 100);\n    });\n\n    addFormatToken(0, ['SS', 2], 0, function () {\n        return ~~(this.millisecond() / 10);\n    });\n\n    addFormatToken(0, ['SSS', 3], 0, 'millisecond');\n    addFormatToken(0, ['SSSS', 4], 0, function () {\n        return this.millisecond() * 10;\n    });\n    addFormatToken(0, ['SSSSS', 5], 0, function () {\n        return this.millisecond() * 100;\n    });\n    addFormatToken(0, ['SSSSSS', 6], 0, function () {\n        return this.millisecond() * 1000;\n    });\n    addFormatToken(0, ['SSSSSSS', 7], 0, function () {\n        return this.millisecond() * 10000;\n    });\n    addFormatToken(0, ['SSSSSSSS', 8], 0, function () {\n        return this.millisecond() * 100000;\n    });\n    addFormatToken(0, ['SSSSSSSSS', 9], 0, function () {\n        return this.millisecond() * 1000000;\n    });\n\n    // ALIASES\n\n    addUnitAlias('millisecond', 'ms');\n\n    // PRIORITY\n\n    addUnitPriority('millisecond', 16);\n\n    // PARSING\n\n    addRegexToken('S', match1to3, match1);\n    addRegexToken('SS', match1to3, match2);\n    addRegexToken('SSS', match1to3, match3);\n\n    var token, getSetMillisecond;\n    for (token = 'SSSS'; token.length <= 9; token += 'S') {\n        addRegexToken(token, matchUnsigned);\n    }\n\n    function parseMs(input, array) {\n        array[MILLISECOND] = toInt(('0.' + input) * 1000);\n    }\n\n    for (token = 'S'; token.length <= 9; token += 'S') {\n        addParseToken(token, parseMs);\n    }\n\n    getSetMillisecond = makeGetSet('Milliseconds', false);\n\n    // FORMATTING\n\n    addFormatToken('z', 0, 0, 'zoneAbbr');\n    addFormatToken('zz', 0, 0, 'zoneName');\n\n    // MOMENTS\n\n    function getZoneAbbr() {\n        return this._isUTC ? 'UTC' : '';\n    }\n\n    function getZoneName() {\n        return this._isUTC ? 'Coordinated Universal Time' : '';\n    }\n\n    var proto = Moment.prototype;\n\n    proto.add = add;\n    proto.calendar = calendar$1;\n    proto.clone = clone;\n    proto.diff = diff;\n    proto.endOf = endOf;\n    proto.format = format;\n    proto.from = from;\n    proto.fromNow = fromNow;\n    proto.to = to;\n    proto.toNow = toNow;\n    proto.get = stringGet;\n    proto.invalidAt = invalidAt;\n    proto.isAfter = isAfter;\n    proto.isBefore = isBefore;\n    proto.isBetween = isBetween;\n    proto.isSame = isSame;\n    proto.isSameOrAfter = isSameOrAfter;\n    proto.isSameOrBefore = isSameOrBefore;\n    proto.isValid = isValid$2;\n    proto.lang = lang;\n    proto.locale = locale;\n    proto.localeData = localeData;\n    proto.max = prototypeMax;\n    proto.min = prototypeMin;\n    proto.parsingFlags = parsingFlags;\n    proto.set = stringSet;\n    proto.startOf = startOf;\n    proto.subtract = subtract;\n    proto.toArray = toArray;\n    proto.toObject = toObject;\n    proto.toDate = toDate;\n    proto.toISOString = toISOString;\n    proto.inspect = inspect;\n    if (typeof Symbol !== 'undefined' && Symbol.for != null) {\n        proto[Symbol.for('nodejs.util.inspect.custom')] = function () {\n            return 'Moment<' + this.format() + '>';\n        };\n    }\n    proto.toJSON = toJSON;\n    proto.toString = toString;\n    proto.unix = unix;\n    proto.valueOf = valueOf;\n    proto.creationData = creationData;\n    proto.eraName = getEraName;\n    proto.eraNarrow = getEraNarrow;\n    proto.eraAbbr = getEraAbbr;\n    proto.eraYear = getEraYear;\n    proto.year = getSetYear;\n    proto.isLeapYear = getIsLeapYear;\n    proto.weekYear = getSetWeekYear;\n    proto.isoWeekYear = getSetISOWeekYear;\n    proto.quarter = proto.quarters = getSetQuarter;\n    proto.month = getSetMonth;\n    proto.daysInMonth = getDaysInMonth;\n    proto.week = proto.weeks = getSetWeek;\n    proto.isoWeek = proto.isoWeeks = getSetISOWeek;\n    proto.weeksInYear = getWeeksInYear;\n    proto.weeksInWeekYear = getWeeksInWeekYear;\n    proto.isoWeeksInYear = getISOWeeksInYear;\n    proto.isoWeeksInISOWeekYear = getISOWeeksInISOWeekYear;\n    proto.date = getSetDayOfMonth;\n    proto.day = proto.days = getSetDayOfWeek;\n    proto.weekday = getSetLocaleDayOfWeek;\n    proto.isoWeekday = getSetISODayOfWeek;\n    proto.dayOfYear = getSetDayOfYear;\n    proto.hour = proto.hours = getSetHour;\n    proto.minute = proto.minutes = getSetMinute;\n    proto.second = proto.seconds = getSetSecond;\n    proto.millisecond = proto.milliseconds = getSetMillisecond;\n    proto.utcOffset = getSetOffset;\n    proto.utc = setOffsetToUTC;\n    proto.local = setOffsetToLocal;\n    proto.parseZone = setOffsetToParsedOffset;\n    proto.hasAlignedHourOffset = hasAlignedHourOffset;\n    proto.isDST = isDaylightSavingTime;\n    proto.isLocal = isLocal;\n    proto.isUtcOffset = isUtcOffset;\n    proto.isUtc = isUtc;\n    proto.isUTC = isUtc;\n    proto.zoneAbbr = getZoneAbbr;\n    proto.zoneName = getZoneName;\n    proto.dates = deprecate(\n        'dates accessor is deprecated. Use date instead.',\n        getSetDayOfMonth\n    );\n    proto.months = deprecate(\n        'months accessor is deprecated. Use month instead',\n        getSetMonth\n    );\n    proto.years = deprecate(\n        'years accessor is deprecated. Use year instead',\n        getSetYear\n    );\n    proto.zone = deprecate(\n        'moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/',\n        getSetZone\n    );\n    proto.isDSTShifted = deprecate(\n        'isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information',\n        isDaylightSavingTimeShifted\n    );\n\n    function createUnix(input) {\n        return createLocal(input * 1000);\n    }\n\n    function createInZone() {\n        return createLocal.apply(null, arguments).parseZone();\n    }\n\n    function preParsePostFormat(string) {\n        return string;\n    }\n\n    var proto$1 = Locale.prototype;\n\n    proto$1.calendar = calendar;\n    proto$1.longDateFormat = longDateFormat;\n    proto$1.invalidDate = invalidDate;\n    proto$1.ordinal = ordinal;\n    proto$1.preparse = preParsePostFormat;\n    proto$1.postformat = preParsePostFormat;\n    proto$1.relativeTime = relativeTime;\n    proto$1.pastFuture = pastFuture;\n    proto$1.set = set;\n    proto$1.eras = localeEras;\n    proto$1.erasParse = localeErasParse;\n    proto$1.erasConvertYear = localeErasConvertYear;\n    proto$1.erasAbbrRegex = erasAbbrRegex;\n    proto$1.erasNameRegex = erasNameRegex;\n    proto$1.erasNarrowRegex = erasNarrowRegex;\n\n    proto$1.months = localeMonths;\n    proto$1.monthsShort = localeMonthsShort;\n    proto$1.monthsParse = localeMonthsParse;\n    proto$1.monthsRegex = monthsRegex;\n    proto$1.monthsShortRegex = monthsShortRegex;\n    proto$1.week = localeWeek;\n    proto$1.firstDayOfYear = localeFirstDayOfYear;\n    proto$1.firstDayOfWeek = localeFirstDayOfWeek;\n\n    proto$1.weekdays = localeWeekdays;\n    proto$1.weekdaysMin = localeWeekdaysMin;\n    proto$1.weekdaysShort = localeWeekdaysShort;\n    proto$1.weekdaysParse = localeWeekdaysParse;\n\n    proto$1.weekdaysRegex = weekdaysRegex;\n    proto$1.weekdaysShortRegex = weekdaysShortRegex;\n    proto$1.weekdaysMinRegex = weekdaysMinRegex;\n\n    proto$1.isPM = localeIsPM;\n    proto$1.meridiem = localeMeridiem;\n\n    function get$1(format, index, field, setter) {\n        var locale = getLocale(),\n            utc = createUTC().set(setter, index);\n        return locale[field](utc, format);\n    }\n\n    function listMonthsImpl(format, index, field) {\n        if (isNumber(format)) {\n            index = format;\n            format = undefined;\n        }\n\n        format = format || '';\n\n        if (index != null) {\n            return get$1(format, index, field, 'month');\n        }\n\n        var i,\n            out = [];\n        for (i = 0; i < 12; i++) {\n            out[i] = get$1(format, i, field, 'month');\n        }\n        return out;\n    }\n\n    // ()\n    // (5)\n    // (fmt, 5)\n    // (fmt)\n    // (true)\n    // (true, 5)\n    // (true, fmt, 5)\n    // (true, fmt)\n    function listWeekdaysImpl(localeSorted, format, index, field) {\n        if (typeof localeSorted === 'boolean') {\n            if (isNumber(format)) {\n                index = format;\n                format = undefined;\n            }\n\n            format = format || '';\n        } else {\n            format = localeSorted;\n            index = format;\n            localeSorted = false;\n\n            if (isNumber(format)) {\n                index = format;\n                format = undefined;\n            }\n\n            format = format || '';\n        }\n\n        var locale = getLocale(),\n            shift = localeSorted ? locale._week.dow : 0,\n            i,\n            out = [];\n\n        if (index != null) {\n            return get$1(format, (index + shift) % 7, field, 'day');\n        }\n\n        for (i = 0; i < 7; i++) {\n            out[i] = get$1(format, (i + shift) % 7, field, 'day');\n        }\n        return out;\n    }\n\n    function listMonths(format, index) {\n        return listMonthsImpl(format, index, 'months');\n    }\n\n    function listMonthsShort(format, index) {\n        return listMonthsImpl(format, index, 'monthsShort');\n    }\n\n    function listWeekdays(localeSorted, format, index) {\n        return listWeekdaysImpl(localeSorted, format, index, 'weekdays');\n    }\n\n    function listWeekdaysShort(localeSorted, format, index) {\n        return listWeekdaysImpl(localeSorted, format, index, 'weekdaysShort');\n    }\n\n    function listWeekdaysMin(localeSorted, format, index) {\n        return listWeekdaysImpl(localeSorted, format, index, 'weekdaysMin');\n    }\n\n    getSetGlobalLocale('en', {\n        eras: [\n            {\n                since: '0001-01-01',\n                until: +Infinity,\n                offset: 1,\n                name: 'Anno Domini',\n                narrow: 'AD',\n                abbr: 'AD',\n            },\n            {\n                since: '0000-12-31',\n                until: -Infinity,\n                offset: 1,\n                name: 'Before Christ',\n                narrow: 'BC',\n                abbr: 'BC',\n            },\n        ],\n        dayOfMonthOrdinalParse: /\\d{1,2}(th|st|nd|rd)/,\n        ordinal: function (number) {\n            var b = number % 10,\n                output =\n                    toInt((number % 100) / 10) === 1\n                        ? 'th'\n                        : b === 1\n                        ? 'st'\n                        : b === 2\n                        ? 'nd'\n                        : b === 3\n                        ? 'rd'\n                        : 'th';\n            return number + output;\n        },\n    });\n\n    // Side effect imports\n\n    hooks.lang = deprecate(\n        'moment.lang is deprecated. Use moment.locale instead.',\n        getSetGlobalLocale\n    );\n    hooks.langData = deprecate(\n        'moment.langData is deprecated. Use moment.localeData instead.',\n        getLocale\n    );\n\n    var mathAbs = Math.abs;\n\n    function abs() {\n        var data = this._data;\n\n        this._milliseconds = mathAbs(this._milliseconds);\n        this._days = mathAbs(this._days);\n        this._months = mathAbs(this._months);\n\n        data.milliseconds = mathAbs(data.milliseconds);\n        data.seconds = mathAbs(data.seconds);\n        data.minutes = mathAbs(data.minutes);\n        data.hours = mathAbs(data.hours);\n        data.months = mathAbs(data.months);\n        data.years = mathAbs(data.years);\n\n        return this;\n    }\n\n    function addSubtract$1(duration, input, value, direction) {\n        var other = createDuration(input, value);\n\n        duration._milliseconds += direction * other._milliseconds;\n        duration._days += direction * other._days;\n        duration._months += direction * other._months;\n\n        return duration._bubble();\n    }\n\n    // supports only 2.0-style add(1, 's') or add(duration)\n    function add$1(input, value) {\n        return addSubtract$1(this, input, value, 1);\n    }\n\n    // supports only 2.0-style subtract(1, 's') or subtract(duration)\n    function subtract$1(input, value) {\n        return addSubtract$1(this, input, value, -1);\n    }\n\n    function absCeil(number) {\n        if (number < 0) {\n            return Math.floor(number);\n        } else {\n            return Math.ceil(number);\n        }\n    }\n\n    function bubble() {\n        var milliseconds = this._milliseconds,\n            days = this._days,\n            months = this._months,\n            data = this._data,\n            seconds,\n            minutes,\n            hours,\n            years,\n            monthsFromDays;\n\n        // if we have a mix of positive and negative values, bubble down first\n        // check: https://github.com/moment/moment/issues/2166\n        if (\n            !(\n                (milliseconds >= 0 && days >= 0 && months >= 0) ||\n                (milliseconds <= 0 && days <= 0 && months <= 0)\n            )\n        ) {\n            milliseconds += absCeil(monthsToDays(months) + days) * 864e5;\n            days = 0;\n            months = 0;\n        }\n\n        // The following code bubbles up values, see the tests for\n        // examples of what that means.\n        data.milliseconds = milliseconds % 1000;\n\n        seconds = absFloor(milliseconds / 1000);\n        data.seconds = seconds % 60;\n\n        minutes = absFloor(seconds / 60);\n        data.minutes = minutes % 60;\n\n        hours = absFloor(minutes / 60);\n        data.hours = hours % 24;\n\n        days += absFloor(hours / 24);\n\n        // convert days to months\n        monthsFromDays = absFloor(daysToMonths(days));\n        months += monthsFromDays;\n        days -= absCeil(monthsToDays(monthsFromDays));\n\n        // 12 months -> 1 year\n        years = absFloor(months / 12);\n        months %= 12;\n\n        data.days = days;\n        data.months = months;\n        data.years = years;\n\n        return this;\n    }\n\n    function daysToMonths(days) {\n        // 400 years have 146097 days (taking into account leap year rules)\n        // 400 years have 12 months === 4800\n        return (days * 4800) / 146097;\n    }\n\n    function monthsToDays(months) {\n        // the reverse of daysToMonths\n        return (months * 146097) / 4800;\n    }\n\n    function as(units) {\n        if (!this.isValid()) {\n            return NaN;\n        }\n        var days,\n            months,\n            milliseconds = this._milliseconds;\n\n        units = normalizeUnits(units);\n\n        if (units === 'month' || units === 'quarter' || units === 'year') {\n            days = this._days + milliseconds / 864e5;\n            months = this._months + daysToMonths(days);\n            switch (units) {\n                case 'month':\n                    return months;\n                case 'quarter':\n                    return months / 3;\n                case 'year':\n                    return months / 12;\n            }\n        } else {\n            // handle milliseconds separately because of floating point math errors (issue #1867)\n            days = this._days + Math.round(monthsToDays(this._months));\n            switch (units) {\n                case 'week':\n                    return days / 7 + milliseconds / 6048e5;\n                case 'day':\n                    return days + milliseconds / 864e5;\n                case 'hour':\n                    return days * 24 + milliseconds / 36e5;\n                case 'minute':\n                    return days * 1440 + milliseconds / 6e4;\n                case 'second':\n                    return days * 86400 + milliseconds / 1000;\n                // Math.floor prevents floating point math errors here\n                case 'millisecond':\n                    return Math.floor(days * 864e5) + milliseconds;\n                default:\n                    throw new Error('Unknown unit ' + units);\n            }\n        }\n    }\n\n    // TODO: Use this.as('ms')?\n    function valueOf$1() {\n        if (!this.isValid()) {\n            return NaN;\n        }\n        return (\n            this._milliseconds +\n            this._days * 864e5 +\n            (this._months % 12) * 2592e6 +\n            toInt(this._months / 12) * 31536e6\n        );\n    }\n\n    function makeAs(alias) {\n        return function () {\n            return this.as(alias);\n        };\n    }\n\n    var asMilliseconds = makeAs('ms'),\n        asSeconds = makeAs('s'),\n        asMinutes = makeAs('m'),\n        asHours = makeAs('h'),\n        asDays = makeAs('d'),\n        asWeeks = makeAs('w'),\n        asMonths = makeAs('M'),\n        asQuarters = makeAs('Q'),\n        asYears = makeAs('y');\n\n    function clone$1() {\n        return createDuration(this);\n    }\n\n    function get$2(units) {\n        units = normalizeUnits(units);\n        return this.isValid() ? this[units + 's']() : NaN;\n    }\n\n    function makeGetter(name) {\n        return function () {\n            return this.isValid() ? this._data[name] : NaN;\n        };\n    }\n\n    var milliseconds = makeGetter('milliseconds'),\n        seconds = makeGetter('seconds'),\n        minutes = makeGetter('minutes'),\n        hours = makeGetter('hours'),\n        days = makeGetter('days'),\n        months = makeGetter('months'),\n        years = makeGetter('years');\n\n    function weeks() {\n        return absFloor(this.days() / 7);\n    }\n\n    var round = Math.round,\n        thresholds = {\n            ss: 44, // a few seconds to seconds\n            s: 45, // seconds to minute\n            m: 45, // minutes to hour\n            h: 22, // hours to day\n            d: 26, // days to month/week\n            w: null, // weeks to month\n            M: 11, // months to year\n        };\n\n    // helper function for moment.fn.from, moment.fn.fromNow, and moment.duration.fn.humanize\n    function substituteTimeAgo(string, number, withoutSuffix, isFuture, locale) {\n        return locale.relativeTime(number || 1, !!withoutSuffix, string, isFuture);\n    }\n\n    function relativeTime$1(posNegDuration, withoutSuffix, thresholds, locale) {\n        var duration = createDuration(posNegDuration).abs(),\n            seconds = round(duration.as('s')),\n            minutes = round(duration.as('m')),\n            hours = round(duration.as('h')),\n            days = round(duration.as('d')),\n            months = round(duration.as('M')),\n            weeks = round(duration.as('w')),\n            years = round(duration.as('y')),\n            a =\n                (seconds <= thresholds.ss && ['s', seconds]) ||\n                (seconds < thresholds.s && ['ss', seconds]) ||\n                (minutes <= 1 && ['m']) ||\n                (minutes < thresholds.m && ['mm', minutes]) ||\n                (hours <= 1 && ['h']) ||\n                (hours < thresholds.h && ['hh', hours]) ||\n                (days <= 1 && ['d']) ||\n                (days < thresholds.d && ['dd', days]);\n\n        if (thresholds.w != null) {\n            a =\n                a ||\n                (weeks <= 1 && ['w']) ||\n                (weeks < thresholds.w && ['ww', weeks]);\n        }\n        a = a ||\n            (months <= 1 && ['M']) ||\n            (months < thresholds.M && ['MM', months]) ||\n            (years <= 1 && ['y']) || ['yy', years];\n\n        a[2] = withoutSuffix;\n        a[3] = +posNegDuration > 0;\n        a[4] = locale;\n        return substituteTimeAgo.apply(null, a);\n    }\n\n    // This function allows you to set the rounding function for relative time strings\n    function getSetRelativeTimeRounding(roundingFunction) {\n        if (roundingFunction === undefined) {\n            return round;\n        }\n        if (typeof roundingFunction === 'function') {\n            round = roundingFunction;\n            return true;\n        }\n        return false;\n    }\n\n    // This function allows you to set a threshold for relative time strings\n    function getSetRelativeTimeThreshold(threshold, limit) {\n        if (thresholds[threshold] === undefined) {\n            return false;\n        }\n        if (limit === undefined) {\n            return thresholds[threshold];\n        }\n        thresholds[threshold] = limit;\n        if (threshold === 's') {\n            thresholds.ss = limit - 1;\n        }\n        return true;\n    }\n\n    function humanize(argWithSuffix, argThresholds) {\n        if (!this.isValid()) {\n            return this.localeData().invalidDate();\n        }\n\n        var withSuffix = false,\n            th = thresholds,\n            locale,\n            output;\n\n        if (typeof argWithSuffix === 'object') {\n            argThresholds = argWithSuffix;\n            argWithSuffix = false;\n        }\n        if (typeof argWithSuffix === 'boolean') {\n            withSuffix = argWithSuffix;\n        }\n        if (typeof argThresholds === 'object') {\n            th = Object.assign({}, thresholds, argThresholds);\n            if (argThresholds.s != null && argThresholds.ss == null) {\n                th.ss = argThresholds.s - 1;\n            }\n        }\n\n        locale = this.localeData();\n        output = relativeTime$1(this, !withSuffix, th, locale);\n\n        if (withSuffix) {\n            output = locale.pastFuture(+this, output);\n        }\n\n        return locale.postformat(output);\n    }\n\n    var abs$1 = Math.abs;\n\n    function sign(x) {\n        return (x > 0) - (x < 0) || +x;\n    }\n\n    function toISOString$1() {\n        // for ISO strings we do not use the normal bubbling rules:\n        //  * milliseconds bubble up until they become hours\n        //  * days do not bubble at all\n        //  * months bubble up until they become years\n        // This is because there is no context-free conversion between hours and days\n        // (think of clock changes)\n        // and also not between days and months (28-31 days per month)\n        if (!this.isValid()) {\n            return this.localeData().invalidDate();\n        }\n\n        var seconds = abs$1(this._milliseconds) / 1000,\n            days = abs$1(this._days),\n            months = abs$1(this._months),\n            minutes,\n            hours,\n            years,\n            s,\n            total = this.asSeconds(),\n            totalSign,\n            ymSign,\n            daysSign,\n            hmsSign;\n\n        if (!total) {\n            // this is the same as C#'s (Noda) and python (isodate)...\n            // but not other JS (goog.date)\n            return 'P0D';\n        }\n\n        // 3600 seconds -> 60 minutes -> 1 hour\n        minutes = absFloor(seconds / 60);\n        hours = absFloor(minutes / 60);\n        seconds %= 60;\n        minutes %= 60;\n\n        // 12 months -> 1 year\n        years = absFloor(months / 12);\n        months %= 12;\n\n        // inspired by https://github.com/dordille/moment-isoduration/blob/master/moment.isoduration.js\n        s = seconds ? seconds.toFixed(3).replace(/\\.?0+$/, '') : '';\n\n        totalSign = total < 0 ? '-' : '';\n        ymSign = sign(this._months) !== sign(total) ? '-' : '';\n        daysSign = sign(this._days) !== sign(total) ? '-' : '';\n        hmsSign = sign(this._milliseconds) !== sign(total) ? '-' : '';\n\n        return (\n            totalSign +\n            'P' +\n            (years ? ymSign + years + 'Y' : '') +\n            (months ? ymSign + months + 'M' : '') +\n            (days ? daysSign + days + 'D' : '') +\n            (hours || minutes || seconds ? 'T' : '') +\n            (hours ? hmsSign + hours + 'H' : '') +\n            (minutes ? hmsSign + minutes + 'M' : '') +\n            (seconds ? hmsSign + s + 'S' : '')\n        );\n    }\n\n    var proto$2 = Duration.prototype;\n\n    proto$2.isValid = isValid$1;\n    proto$2.abs = abs;\n    proto$2.add = add$1;\n    proto$2.subtract = subtract$1;\n    proto$2.as = as;\n    proto$2.asMilliseconds = asMilliseconds;\n    proto$2.asSeconds = asSeconds;\n    proto$2.asMinutes = asMinutes;\n    proto$2.asHours = asHours;\n    proto$2.asDays = asDays;\n    proto$2.asWeeks = asWeeks;\n    proto$2.asMonths = asMonths;\n    proto$2.asQuarters = asQuarters;\n    proto$2.asYears = asYears;\n    proto$2.valueOf = valueOf$1;\n    proto$2._bubble = bubble;\n    proto$2.clone = clone$1;\n    proto$2.get = get$2;\n    proto$2.milliseconds = milliseconds;\n    proto$2.seconds = seconds;\n    proto$2.minutes = minutes;\n    proto$2.hours = hours;\n    proto$2.days = days;\n    proto$2.weeks = weeks;\n    proto$2.months = months;\n    proto$2.years = years;\n    proto$2.humanize = humanize;\n    proto$2.toISOString = toISOString$1;\n    proto$2.toString = toISOString$1;\n    proto$2.toJSON = toISOString$1;\n    proto$2.locale = locale;\n    proto$2.localeData = localeData;\n\n    proto$2.toIsoString = deprecate(\n        'toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)',\n        toISOString$1\n    );\n    proto$2.lang = lang;\n\n    // FORMATTING\n\n    addFormatToken('X', 0, 0, 'unix');\n    addFormatToken('x', 0, 0, 'valueOf');\n\n    // PARSING\n\n    addRegexToken('x', matchSigned);\n    addRegexToken('X', matchTimestamp);\n    addParseToken('X', function (input, array, config) {\n        config._d = new Date(parseFloat(input) * 1000);\n    });\n    addParseToken('x', function (input, array, config) {\n        config._d = new Date(toInt(input));\n    });\n\n    //! moment.js\n\n    hooks.version = '2.29.1';\n\n    setHookCallback(createLocal);\n\n    hooks.fn = proto;\n    hooks.min = min;\n    hooks.max = max;\n    hooks.now = now;\n    hooks.utc = createUTC;\n    hooks.unix = createUnix;\n    hooks.months = listMonths;\n    hooks.isDate = isDate;\n    hooks.locale = getSetGlobalLocale;\n    hooks.invalid = createInvalid;\n    hooks.duration = createDuration;\n    hooks.isMoment = isMoment;\n    hooks.weekdays = listWeekdays;\n    hooks.parseZone = createInZone;\n    hooks.localeData = getLocale;\n    hooks.isDuration = isDuration;\n    hooks.monthsShort = listMonthsShort;\n    hooks.weekdaysMin = listWeekdaysMin;\n    hooks.defineLocale = defineLocale;\n    hooks.updateLocale = updateLocale;\n    hooks.locales = listLocales;\n    hooks.weekdaysShort = listWeekdaysShort;\n    hooks.normalizeUnits = normalizeUnits;\n    hooks.relativeTimeRounding = getSetRelativeTimeRounding;\n    hooks.relativeTimeThreshold = getSetRelativeTimeThreshold;\n    hooks.calendarFormat = getCalendarFormat;\n    hooks.prototype = proto;\n\n    // currently HTML5 input type only supports 24-hour formats\n    hooks.HTML5_FMT = {\n        DATETIME_LOCAL: 'YYYY-MM-DDTHH:mm', // <input type=\"datetime-local\" />\n        DATETIME_LOCAL_SECONDS: 'YYYY-MM-DDTHH:mm:ss', // <input type=\"datetime-local\" step=\"1\" />\n        DATETIME_LOCAL_MS: 'YYYY-MM-DDTHH:mm:ss.SSS', // <input type=\"datetime-local\" step=\"0.001\" />\n        DATE: 'YYYY-MM-DD', // <input type=\"date\" />\n        TIME: 'HH:mm', // <input type=\"time\" />\n        TIME_SECONDS: 'HH:mm:ss', // <input type=\"time\" step=\"1\" />\n        TIME_MS: 'HH:mm:ss.SSS', // <input type=\"time\" step=\"0.001\" />\n        WEEK: 'GGGG-[W]WW', // <input type=\"week\" />\n        MONTH: 'YYYY-MM', // <input type=\"month\" />\n    };\n\n    return hooks;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EACzB,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACD,OAAO,GAAGD,OAAO,CAAC,CAAC,GACzF,OAAOG,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAACH,OAAO,CAAC,GAC5DD,MAAM,CAACM,MAAM,GAAGL,OAAO,CAAC,CAAC;AAC7B,CAAC,EAAC,IAAI,EAAG,YAAY;EAAE,YAAY;;EAE/B,IAAIM,YAAY;EAEhB,SAASC,KAAKA,CAAA,EAAG;IACb,OAAOD,YAAY,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC9C;;EAEA;EACA;EACA,SAASC,eAAeA,CAACC,QAAQ,EAAE;IAC/BL,YAAY,GAAGK,QAAQ;EAC3B;EAEA,SAASC,OAAOA,CAACC,KAAK,EAAE;IACpB,OACIA,KAAK,YAAYC,KAAK,IACtBC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,gBAAgB;EAElE;EAEA,SAASM,QAAQA,CAACN,KAAK,EAAE;IACrB;IACA;IACA,OACIA,KAAK,IAAI,IAAI,IACbE,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,iBAAiB;EAEnE;EAEA,SAASO,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACtB,OAAOP,MAAM,CAACC,SAAS,CAACO,cAAc,CAACL,IAAI,CAACG,CAAC,EAAEC,CAAC,CAAC;EACrD;EAEA,SAASE,aAAaA,CAACC,GAAG,EAAE;IACxB,IAAIV,MAAM,CAACW,mBAAmB,EAAE;MAC5B,OAAOX,MAAM,CAACW,mBAAmB,CAACD,GAAG,CAAC,CAACE,MAAM,KAAK,CAAC;IACvD,CAAC,MAAM;MACH,IAAIC,CAAC;MACL,KAAKA,CAAC,IAAIH,GAAG,EAAE;QACX,IAAIL,UAAU,CAACK,GAAG,EAAEG,CAAC,CAAC,EAAE;UACpB,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf;EACJ;EAEA,SAASC,WAAWA,CAAChB,KAAK,EAAE;IACxB,OAAOA,KAAK,KAAK,KAAK,CAAC;EAC3B;EAEA,SAASiB,QAAQA,CAACjB,KAAK,EAAE;IACrB,OACI,OAAOA,KAAK,KAAK,QAAQ,IACzBE,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,iBAAiB;EAEnE;EAEA,SAASkB,MAAMA,CAAClB,KAAK,EAAE;IACnB,OACIA,KAAK,YAAYmB,IAAI,IACrBjB,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,eAAe;EAEjE;EAEA,SAASoB,GAAGA,CAACC,GAAG,EAAEC,EAAE,EAAE;IAClB,IAAIC,GAAG,GAAG,EAAE;MACRC,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACP,MAAM,EAAE,EAAEU,CAAC,EAAE;MAC7BD,GAAG,CAACE,IAAI,CAACH,EAAE,CAACD,GAAG,CAACG,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC;IAC3B;IACA,OAAOD,GAAG;EACd;EAEA,SAASG,MAAMA,CAAClB,CAAC,EAAEC,CAAC,EAAE;IAClB,KAAK,IAAIe,CAAC,IAAIf,CAAC,EAAE;MACb,IAAIF,UAAU,CAACE,CAAC,EAAEe,CAAC,CAAC,EAAE;QAClBhB,CAAC,CAACgB,CAAC,CAAC,GAAGf,CAAC,CAACe,CAAC,CAAC;MACf;IACJ;IAEA,IAAIjB,UAAU,CAACE,CAAC,EAAE,UAAU,CAAC,EAAE;MAC3BD,CAAC,CAACJ,QAAQ,GAAGK,CAAC,CAACL,QAAQ;IAC3B;IAEA,IAAIG,UAAU,CAACE,CAAC,EAAE,SAAS,CAAC,EAAE;MAC1BD,CAAC,CAACmB,OAAO,GAAGlB,CAAC,CAACkB,OAAO;IACzB;IAEA,OAAOnB,CAAC;EACZ;EAEA,SAASoB,SAASA,CAAC5B,KAAK,EAAE6B,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;IAC9C,OAAOC,gBAAgB,CAAChC,KAAK,EAAE6B,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE,IAAI,CAAC,CAACE,GAAG,CAAC,CAAC;EACtE;EAEA,SAASC,mBAAmBA,CAAA,EAAG;IAC3B;IACA,OAAO;MACHC,KAAK,EAAE,KAAK;MACZC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,CAAC,CAAC;MACZC,aAAa,EAAE,CAAC;MAChBC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,KAAK;MACpBC,eAAe,EAAE,KAAK;MACtBC,GAAG,EAAE,KAAK;MACVC,eAAe,EAAE,EAAE;MACnBC,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,KAAK;MACdC,eAAe,EAAE;IACrB,CAAC;EACL;EAEA,SAASC,eAAeA,CAACC,CAAC,EAAE;IACxB,IAAIA,CAAC,CAACC,GAAG,IAAI,IAAI,EAAE;MACfD,CAAC,CAACC,GAAG,GAAGnB,mBAAmB,CAAC,CAAC;IACjC;IACA,OAAOkB,CAAC,CAACC,GAAG;EAChB;EAEA,IAAIC,IAAI;EACR,IAAIrD,KAAK,CAACE,SAAS,CAACmD,IAAI,EAAE;IACtBA,IAAI,GAAGrD,KAAK,CAACE,SAAS,CAACmD,IAAI;EAC/B,CAAC,MAAM;IACHA,IAAI,GAAG,SAAAA,CAAUC,GAAG,EAAE;MAClB,IAAIC,CAAC,GAAGtD,MAAM,CAAC,IAAI,CAAC;QAChBuD,GAAG,GAAGD,CAAC,CAAC1C,MAAM,KAAK,CAAC;QACpBU,CAAC;MAEL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,GAAG,EAAEjC,CAAC,EAAE,EAAE;QACtB,IAAIA,CAAC,IAAIgC,CAAC,IAAID,GAAG,CAAClD,IAAI,CAAC,IAAI,EAAEmD,CAAC,CAAChC,CAAC,CAAC,EAAEA,CAAC,EAAEgC,CAAC,CAAC,EAAE;UACtC,OAAO,IAAI;QACf;MACJ;MAEA,OAAO,KAAK;IAChB,CAAC;EACL;EAEA,SAASE,OAAOA,CAACN,CAAC,EAAE;IAChB,IAAIA,CAAC,CAACO,QAAQ,IAAI,IAAI,EAAE;MACpB,IAAIC,KAAK,GAAGT,eAAe,CAACC,CAAC,CAAC;QAC1BS,WAAW,GAAGP,IAAI,CAACjD,IAAI,CAACuD,KAAK,CAACd,eAAe,EAAE,UAAUtB,CAAC,EAAE;UACxD,OAAOA,CAAC,IAAI,IAAI;QACpB,CAAC,CAAC;QACFsC,UAAU,GACN,CAACC,KAAK,CAACX,CAAC,CAACY,EAAE,CAACC,OAAO,CAAC,CAAC,CAAC,IACtBL,KAAK,CAACtB,QAAQ,GAAG,CAAC,IAClB,CAACsB,KAAK,CAACzB,KAAK,IACZ,CAACyB,KAAK,CAACnB,UAAU,IACjB,CAACmB,KAAK,CAAClB,YAAY,IACnB,CAACkB,KAAK,CAACM,cAAc,IACrB,CAACN,KAAK,CAACV,eAAe,IACtB,CAACU,KAAK,CAACpB,SAAS,IAChB,CAACoB,KAAK,CAACjB,aAAa,IACpB,CAACiB,KAAK,CAAChB,eAAe,KACrB,CAACgB,KAAK,CAACZ,QAAQ,IAAKY,KAAK,CAACZ,QAAQ,IAAIa,WAAY,CAAC;MAE5D,IAAIT,CAAC,CAACe,OAAO,EAAE;QACXL,UAAU,GACNA,UAAU,IACVF,KAAK,CAACrB,aAAa,KAAK,CAAC,IACzBqB,KAAK,CAACxB,YAAY,CAACtB,MAAM,KAAK,CAAC,IAC/B8C,KAAK,CAACQ,OAAO,KAAKC,SAAS;MACnC;MAEA,IAAInE,MAAM,CAACoE,QAAQ,IAAI,IAAI,IAAI,CAACpE,MAAM,CAACoE,QAAQ,CAAClB,CAAC,CAAC,EAAE;QAChDA,CAAC,CAACO,QAAQ,GAAGG,UAAU;MAC3B,CAAC,MAAM;QACH,OAAOA,UAAU;MACrB;IACJ;IACA,OAAOV,CAAC,CAACO,QAAQ;EACrB;EAEA,SAASY,aAAaA,CAACX,KAAK,EAAE;IAC1B,IAAIR,CAAC,GAAGxB,SAAS,CAAC4C,GAAG,CAAC;IACtB,IAAIZ,KAAK,IAAI,IAAI,EAAE;MACflC,MAAM,CAACyB,eAAe,CAACC,CAAC,CAAC,EAAEQ,KAAK,CAAC;IACrC,CAAC,MAAM;MACHT,eAAe,CAACC,CAAC,CAAC,CAACR,eAAe,GAAG,IAAI;IAC7C;IAEA,OAAOQ,CAAC;EACZ;;EAEA;EACA;EACA,IAAIqB,gBAAgB,GAAI/E,KAAK,CAAC+E,gBAAgB,GAAG,EAAG;IAChDC,gBAAgB,GAAG,KAAK;EAE5B,SAASC,UAAUA,CAACC,EAAE,EAAEC,IAAI,EAAE;IAC1B,IAAIrD,CAAC,EAAEsD,IAAI,EAAEC,GAAG;IAEhB,IAAI,CAAC/D,WAAW,CAAC6D,IAAI,CAACG,gBAAgB,CAAC,EAAE;MACrCJ,EAAE,CAACI,gBAAgB,GAAGH,IAAI,CAACG,gBAAgB;IAC/C;IACA,IAAI,CAAChE,WAAW,CAAC6D,IAAI,CAACI,EAAE,CAAC,EAAE;MACvBL,EAAE,CAACK,EAAE,GAAGJ,IAAI,CAACI,EAAE;IACnB;IACA,IAAI,CAACjE,WAAW,CAAC6D,IAAI,CAACK,EAAE,CAAC,EAAE;MACvBN,EAAE,CAACM,EAAE,GAAGL,IAAI,CAACK,EAAE;IACnB;IACA,IAAI,CAAClE,WAAW,CAAC6D,IAAI,CAACM,EAAE,CAAC,EAAE;MACvBP,EAAE,CAACO,EAAE,GAAGN,IAAI,CAACM,EAAE;IACnB;IACA,IAAI,CAACnE,WAAW,CAAC6D,IAAI,CAACV,OAAO,CAAC,EAAE;MAC5BS,EAAE,CAACT,OAAO,GAAGU,IAAI,CAACV,OAAO;IAC7B;IACA,IAAI,CAACnD,WAAW,CAAC6D,IAAI,CAACO,IAAI,CAAC,EAAE;MACzBR,EAAE,CAACQ,IAAI,GAAGP,IAAI,CAACO,IAAI;IACvB;IACA,IAAI,CAACpE,WAAW,CAAC6D,IAAI,CAACQ,MAAM,CAAC,EAAE;MAC3BT,EAAE,CAACS,MAAM,GAAGR,IAAI,CAACQ,MAAM;IAC3B;IACA,IAAI,CAACrE,WAAW,CAAC6D,IAAI,CAACS,OAAO,CAAC,EAAE;MAC5BV,EAAE,CAACU,OAAO,GAAGT,IAAI,CAACS,OAAO;IAC7B;IACA,IAAI,CAACtE,WAAW,CAAC6D,IAAI,CAACxB,GAAG,CAAC,EAAE;MACxBuB,EAAE,CAACvB,GAAG,GAAGF,eAAe,CAAC0B,IAAI,CAAC;IAClC;IACA,IAAI,CAAC7D,WAAW,CAAC6D,IAAI,CAACU,OAAO,CAAC,EAAE;MAC5BX,EAAE,CAACW,OAAO,GAAGV,IAAI,CAACU,OAAO;IAC7B;IAEA,IAAId,gBAAgB,CAAC3D,MAAM,GAAG,CAAC,EAAE;MAC7B,KAAKU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,gBAAgB,CAAC3D,MAAM,EAAEU,CAAC,EAAE,EAAE;QAC1CsD,IAAI,GAAGL,gBAAgB,CAACjD,CAAC,CAAC;QAC1BuD,GAAG,GAAGF,IAAI,CAACC,IAAI,CAAC;QAChB,IAAI,CAAC9D,WAAW,CAAC+D,GAAG,CAAC,EAAE;UACnBH,EAAE,CAACE,IAAI,CAAC,GAAGC,GAAG;QAClB;MACJ;IACJ;IAEA,OAAOH,EAAE;EACb;;EAEA;EACA,SAASY,MAAMA,CAACC,MAAM,EAAE;IACpBd,UAAU,CAAC,IAAI,EAAEc,MAAM,CAAC;IACxB,IAAI,CAACzB,EAAE,GAAG,IAAI7C,IAAI,CAACsE,MAAM,CAACzB,EAAE,IAAI,IAAI,GAAGyB,MAAM,CAACzB,EAAE,CAACC,OAAO,CAAC,CAAC,GAAGO,GAAG,CAAC;IACjE,IAAI,CAAC,IAAI,CAACd,OAAO,CAAC,CAAC,EAAE;MACjB,IAAI,CAACM,EAAE,GAAG,IAAI7C,IAAI,CAACqD,GAAG,CAAC;IAC3B;IACA;IACA;IACA,IAAIE,gBAAgB,KAAK,KAAK,EAAE;MAC5BA,gBAAgB,GAAG,IAAI;MACvBhF,KAAK,CAACgG,YAAY,CAAC,IAAI,CAAC;MACxBhB,gBAAgB,GAAG,KAAK;IAC5B;EACJ;EAEA,SAASiB,QAAQA,CAAC/E,GAAG,EAAE;IACnB,OACIA,GAAG,YAAY4E,MAAM,IAAK5E,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACoE,gBAAgB,IAAI,IAAK;EAE9E;EAEA,SAASY,IAAIA,CAACC,GAAG,EAAE;IACf,IACInG,KAAK,CAACoG,2BAA2B,KAAK,KAAK,IAC3C,OAAOC,OAAO,KAAK,WAAW,IAC9BA,OAAO,CAACH,IAAI,EACd;MACEG,OAAO,CAACH,IAAI,CAAC,uBAAuB,GAAGC,GAAG,CAAC;IAC/C;EACJ;EAEA,SAASG,SAASA,CAACH,GAAG,EAAEvE,EAAE,EAAE;IACxB,IAAI2E,SAAS,GAAG,IAAI;IAEpB,OAAOvE,MAAM,CAAC,YAAY;MACtB,IAAIhC,KAAK,CAACwG,kBAAkB,IAAI,IAAI,EAAE;QAClCxG,KAAK,CAACwG,kBAAkB,CAAC,IAAI,EAAEL,GAAG,CAAC;MACvC;MACA,IAAII,SAAS,EAAE;QACX,IAAIE,IAAI,GAAG,EAAE;UACTC,GAAG;UACH5E,CAAC;UACD6E,GAAG;QACP,KAAK7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,SAAS,CAACkB,MAAM,EAAEU,CAAC,EAAE,EAAE;UACnC4E,GAAG,GAAG,EAAE;UACR,IAAI,OAAOxG,SAAS,CAAC4B,CAAC,CAAC,KAAK,QAAQ,EAAE;YAClC4E,GAAG,IAAI,KAAK,GAAG5E,CAAC,GAAG,IAAI;YACvB,KAAK6E,GAAG,IAAIzG,SAAS,CAAC,CAAC,CAAC,EAAE;cACtB,IAAIW,UAAU,CAACX,SAAS,CAAC,CAAC,CAAC,EAAEyG,GAAG,CAAC,EAAE;gBAC/BD,GAAG,IAAIC,GAAG,GAAG,IAAI,GAAGzG,SAAS,CAAC,CAAC,CAAC,CAACyG,GAAG,CAAC,GAAG,IAAI;cAChD;YACJ;YACAD,GAAG,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,MAAM;YACHF,GAAG,GAAGxG,SAAS,CAAC4B,CAAC,CAAC;UACtB;UACA2E,IAAI,CAAC1E,IAAI,CAAC2E,GAAG,CAAC;QAClB;QACAR,IAAI,CACAC,GAAG,GACC,eAAe,GACf5F,KAAK,CAACE,SAAS,CAACmG,KAAK,CAACjG,IAAI,CAAC8F,IAAI,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC,GACzC,IAAI,GACJ,IAAIC,KAAK,CAAC,CAAC,CAACC,KACpB,CAAC;QACDR,SAAS,GAAG,KAAK;MACrB;MACA,OAAO3E,EAAE,CAAC3B,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,CAAC,EAAE0B,EAAE,CAAC;EACV;EAEA,IAAIoF,YAAY,GAAG,CAAC,CAAC;EAErB,SAASC,eAAeA,CAACC,IAAI,EAAEf,GAAG,EAAE;IAChC,IAAInG,KAAK,CAACwG,kBAAkB,IAAI,IAAI,EAAE;MAClCxG,KAAK,CAACwG,kBAAkB,CAACU,IAAI,EAAEf,GAAG,CAAC;IACvC;IACA,IAAI,CAACa,YAAY,CAACE,IAAI,CAAC,EAAE;MACrBhB,IAAI,CAACC,GAAG,CAAC;MACTa,YAAY,CAACE,IAAI,CAAC,GAAG,IAAI;IAC7B;EACJ;EAEAlH,KAAK,CAACoG,2BAA2B,GAAG,KAAK;EACzCpG,KAAK,CAACwG,kBAAkB,GAAG,IAAI;EAE/B,SAASW,UAAUA,CAAC7G,KAAK,EAAE;IACvB,OACK,OAAO8G,QAAQ,KAAK,WAAW,IAAI9G,KAAK,YAAY8G,QAAQ,IAC7D5G,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,mBAAmB;EAErE;EAEA,SAAS+G,GAAGA,CAACtB,MAAM,EAAE;IACjB,IAAIX,IAAI,EAAEtD,CAAC;IACX,KAAKA,CAAC,IAAIiE,MAAM,EAAE;MACd,IAAIlF,UAAU,CAACkF,MAAM,EAAEjE,CAAC,CAAC,EAAE;QACvBsD,IAAI,GAAGW,MAAM,CAACjE,CAAC,CAAC;QAChB,IAAIqF,UAAU,CAAC/B,IAAI,CAAC,EAAE;UAClB,IAAI,CAACtD,CAAC,CAAC,GAAGsD,IAAI;QAClB,CAAC,MAAM;UACH,IAAI,CAAC,GAAG,GAAGtD,CAAC,CAAC,GAAGsD,IAAI;QACxB;MACJ;IACJ;IACA,IAAI,CAACkC,OAAO,GAAGvB,MAAM;IACrB;IACA;IACA;IACA,IAAI,CAACwB,8BAA8B,GAAG,IAAIC,MAAM,CAC5C,CAAC,IAAI,CAACC,uBAAuB,CAACC,MAAM,IAAI,IAAI,CAACC,aAAa,CAACD,MAAM,IAC7D,GAAG,GACH,SAAS,CAACA,MAClB,CAAC;EACL;EAEA,SAASE,YAAYA,CAACC,YAAY,EAAEC,WAAW,EAAE;IAC7C,IAAIjG,GAAG,GAAGG,MAAM,CAAC,CAAC,CAAC,EAAE6F,YAAY,CAAC;MAC9BzC,IAAI;IACR,KAAKA,IAAI,IAAI0C,WAAW,EAAE;MACtB,IAAIjH,UAAU,CAACiH,WAAW,EAAE1C,IAAI,CAAC,EAAE;QAC/B,IAAIxE,QAAQ,CAACiH,YAAY,CAACzC,IAAI,CAAC,CAAC,IAAIxE,QAAQ,CAACkH,WAAW,CAAC1C,IAAI,CAAC,CAAC,EAAE;UAC7DvD,GAAG,CAACuD,IAAI,CAAC,GAAG,CAAC,CAAC;UACdpD,MAAM,CAACH,GAAG,CAACuD,IAAI,CAAC,EAAEyC,YAAY,CAACzC,IAAI,CAAC,CAAC;UACrCpD,MAAM,CAACH,GAAG,CAACuD,IAAI,CAAC,EAAE0C,WAAW,CAAC1C,IAAI,CAAC,CAAC;QACxC,CAAC,MAAM,IAAI0C,WAAW,CAAC1C,IAAI,CAAC,IAAI,IAAI,EAAE;UAClCvD,GAAG,CAACuD,IAAI,CAAC,GAAG0C,WAAW,CAAC1C,IAAI,CAAC;QACjC,CAAC,MAAM;UACH,OAAOvD,GAAG,CAACuD,IAAI,CAAC;QACpB;MACJ;IACJ;IACA,KAAKA,IAAI,IAAIyC,YAAY,EAAE;MACvB,IACIhH,UAAU,CAACgH,YAAY,EAAEzC,IAAI,CAAC,IAC9B,CAACvE,UAAU,CAACiH,WAAW,EAAE1C,IAAI,CAAC,IAC9BxE,QAAQ,CAACiH,YAAY,CAACzC,IAAI,CAAC,CAAC,EAC9B;QACE;QACAvD,GAAG,CAACuD,IAAI,CAAC,GAAGpD,MAAM,CAAC,CAAC,CAAC,EAAEH,GAAG,CAACuD,IAAI,CAAC,CAAC;MACrC;IACJ;IACA,OAAOvD,GAAG;EACd;EAEA,SAASkG,MAAMA,CAAChC,MAAM,EAAE;IACpB,IAAIA,MAAM,IAAI,IAAI,EAAE;MAChB,IAAI,CAACsB,GAAG,CAACtB,MAAM,CAAC;IACpB;EACJ;EAEA,IAAIiC,IAAI;EAER,IAAIxH,MAAM,CAACwH,IAAI,EAAE;IACbA,IAAI,GAAGxH,MAAM,CAACwH,IAAI;EACtB,CAAC,MAAM;IACHA,IAAI,GAAG,SAAAA,CAAU9G,GAAG,EAAE;MAClB,IAAIY,CAAC;QACDD,GAAG,GAAG,EAAE;MACZ,KAAKC,CAAC,IAAIZ,GAAG,EAAE;QACX,IAAIL,UAAU,CAACK,GAAG,EAAEY,CAAC,CAAC,EAAE;UACpBD,GAAG,CAACE,IAAI,CAACD,CAAC,CAAC;QACf;MACJ;MACA,OAAOD,GAAG;IACd,CAAC;EACL;EAEA,IAAIoG,eAAe,GAAG;IAClBC,OAAO,EAAE,eAAe;IACxBC,OAAO,EAAE,kBAAkB;IAC3BC,QAAQ,EAAE,cAAc;IACxBC,OAAO,EAAE,mBAAmB;IAC5BC,QAAQ,EAAE,qBAAqB;IAC/BC,QAAQ,EAAE;EACd,CAAC;EAED,SAASC,QAAQA,CAAC7B,GAAG,EAAE8B,GAAG,EAAEC,GAAG,EAAE;IAC7B,IAAIC,MAAM,GAAG,IAAI,CAACC,SAAS,CAACjC,GAAG,CAAC,IAAI,IAAI,CAACiC,SAAS,CAAC,UAAU,CAAC;IAC9D,OAAOzB,UAAU,CAACwB,MAAM,CAAC,GAAGA,MAAM,CAAChI,IAAI,CAAC8H,GAAG,EAAEC,GAAG,CAAC,GAAGC,MAAM;EAC9D;EAEA,SAASE,QAAQA,CAACC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAE;IAC/C,IAAIC,SAAS,GAAG,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACL,MAAM,CAAC;MACjCM,WAAW,GAAGL,YAAY,GAAGE,SAAS,CAAC7H,MAAM;MAC7CiI,IAAI,GAAGP,MAAM,IAAI,CAAC;IACtB,OACI,CAACO,IAAI,GAAIL,SAAS,GAAG,GAAG,GAAG,EAAE,GAAI,GAAG,IACpCE,IAAI,CAACI,GAAG,CAAC,EAAE,EAAEJ,IAAI,CAACK,GAAG,CAAC,CAAC,EAAEH,WAAW,CAAC,CAAC,CAAC1I,QAAQ,CAAC,CAAC,CAAC8I,MAAM,CAAC,CAAC,CAAC,GAC3DP,SAAS;EAEjB;EAEA,IAAIQ,gBAAgB,GAAG,wMAAwM;IAC3NC,qBAAqB,GAAG,4CAA4C;IACpEC,eAAe,GAAG,CAAC,CAAC;IACpBC,oBAAoB,GAAG,CAAC,CAAC;;EAE7B;EACA;EACA;EACA;EACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAE5J,QAAQ,EAAE;IACtD,IAAI6J,IAAI,GAAG7J,QAAQ;IACnB,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC9B6J,IAAI,GAAG,SAAAA,CAAA,EAAY;QACf,OAAO,IAAI,CAAC7J,QAAQ,CAAC,CAAC,CAAC;MAC3B,CAAC;IACL;IACA,IAAI0J,KAAK,EAAE;MACPF,oBAAoB,CAACE,KAAK,CAAC,GAAGG,IAAI;IACtC;IACA,IAAIF,MAAM,EAAE;MACRH,oBAAoB,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY;QAC1C,OAAOlB,QAAQ,CAACoB,IAAI,CAAChK,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,EAAE6J,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;MACtE,CAAC;IACL;IACA,IAAIC,OAAO,EAAE;MACTJ,oBAAoB,CAACI,OAAO,CAAC,GAAG,YAAY;QACxC,OAAO,IAAI,CAACE,UAAU,CAAC,CAAC,CAACF,OAAO,CAC5BC,IAAI,CAAChK,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,EAC3B4J,KACJ,CAAC;MACL,CAAC;IACL;EACJ;EAEA,SAASK,sBAAsBA,CAAC7J,KAAK,EAAE;IACnC,IAAIA,KAAK,CAAC8J,KAAK,CAAC,UAAU,CAAC,EAAE;MACzB,OAAO9J,KAAK,CAAC+J,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IACxC;IACA,OAAO/J,KAAK,CAAC+J,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EACnC;EAEA,SAASC,kBAAkBA,CAACnI,MAAM,EAAE;IAChC,IAAIoI,KAAK,GAAGpI,MAAM,CAACiI,KAAK,CAACX,gBAAgB,CAAC;MACtC3H,CAAC;MACDV,MAAM;IAEV,KAAKU,CAAC,GAAG,CAAC,EAAEV,MAAM,GAAGmJ,KAAK,CAACnJ,MAAM,EAAEU,CAAC,GAAGV,MAAM,EAAEU,CAAC,EAAE,EAAE;MAChD,IAAI8H,oBAAoB,CAACW,KAAK,CAACzI,CAAC,CAAC,CAAC,EAAE;QAChCyI,KAAK,CAACzI,CAAC,CAAC,GAAG8H,oBAAoB,CAACW,KAAK,CAACzI,CAAC,CAAC,CAAC;MAC7C,CAAC,MAAM;QACHyI,KAAK,CAACzI,CAAC,CAAC,GAAGqI,sBAAsB,CAACI,KAAK,CAACzI,CAAC,CAAC,CAAC;MAC/C;IACJ;IAEA,OAAO,UAAU2G,GAAG,EAAE;MAClB,IAAIE,MAAM,GAAG,EAAE;QACX7G,CAAC;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,MAAM,EAAEU,CAAC,EAAE,EAAE;QACzB6G,MAAM,IAAIxB,UAAU,CAACoD,KAAK,CAACzI,CAAC,CAAC,CAAC,GACxByI,KAAK,CAACzI,CAAC,CAAC,CAACnB,IAAI,CAAC8H,GAAG,EAAEtG,MAAM,CAAC,GAC1BoI,KAAK,CAACzI,CAAC,CAAC;MAClB;MACA,OAAO6G,MAAM;IACjB,CAAC;EACL;;EAEA;EACA,SAAS6B,YAAYA,CAAC9G,CAAC,EAAEvB,MAAM,EAAE;IAC7B,IAAI,CAACuB,CAAC,CAACM,OAAO,CAAC,CAAC,EAAE;MACd,OAAON,CAAC,CAACwG,UAAU,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;IACvC;IAEAtI,MAAM,GAAGuI,YAAY,CAACvI,MAAM,EAAEuB,CAAC,CAACwG,UAAU,CAAC,CAAC,CAAC;IAC7CP,eAAe,CAACxH,MAAM,CAAC,GACnBwH,eAAe,CAACxH,MAAM,CAAC,IAAImI,kBAAkB,CAACnI,MAAM,CAAC;IAEzD,OAAOwH,eAAe,CAACxH,MAAM,CAAC,CAACuB,CAAC,CAAC;EACrC;EAEA,SAASgH,YAAYA,CAACvI,MAAM,EAAEC,MAAM,EAAE;IAClC,IAAIN,CAAC,GAAG,CAAC;IAET,SAAS6I,2BAA2BA,CAACrK,KAAK,EAAE;MACxC,OAAO8B,MAAM,CAACwI,cAAc,CAACtK,KAAK,CAAC,IAAIA,KAAK;IAChD;IAEAoJ,qBAAqB,CAACmB,SAAS,GAAG,CAAC;IACnC,OAAO/I,CAAC,IAAI,CAAC,IAAI4H,qBAAqB,CAACoB,IAAI,CAAC3I,MAAM,CAAC,EAAE;MACjDA,MAAM,GAAGA,MAAM,CAACkI,OAAO,CACnBX,qBAAqB,EACrBiB,2BACJ,CAAC;MACDjB,qBAAqB,CAACmB,SAAS,GAAG,CAAC;MACnC/I,CAAC,IAAI,CAAC;IACV;IAEA,OAAOK,MAAM;EACjB;EAEA,IAAI4I,qBAAqB,GAAG;IACxBC,GAAG,EAAE,WAAW;IAChBC,EAAE,EAAE,QAAQ;IACZC,CAAC,EAAE,YAAY;IACfC,EAAE,EAAE,cAAc;IAClBC,GAAG,EAAE,qBAAqB;IAC1BC,IAAI,EAAE;EACV,CAAC;EAED,SAAST,cAAcA,CAACjE,GAAG,EAAE;IACzB,IAAIxE,MAAM,GAAG,IAAI,CAACmJ,eAAe,CAAC3E,GAAG,CAAC;MAClC4E,WAAW,GAAG,IAAI,CAACD,eAAe,CAAC3E,GAAG,CAAC6E,WAAW,CAAC,CAAC,CAAC;IAEzD,IAAIrJ,MAAM,IAAI,CAACoJ,WAAW,EAAE;MACxB,OAAOpJ,MAAM;IACjB;IAEA,IAAI,CAACmJ,eAAe,CAAC3E,GAAG,CAAC,GAAG4E,WAAW,CAClCnB,KAAK,CAACX,gBAAgB,CAAC,CACvB/H,GAAG,CAAC,UAAU+J,GAAG,EAAE;MAChB,IACIA,GAAG,KAAK,MAAM,IACdA,GAAG,KAAK,IAAI,IACZA,GAAG,KAAK,IAAI,IACZA,GAAG,KAAK,MAAM,EAChB;QACE,OAAOA,GAAG,CAAC7E,KAAK,CAAC,CAAC,CAAC;MACvB;MACA,OAAO6E,GAAG;IACd,CAAC,CAAC,CACD5E,IAAI,CAAC,EAAE,CAAC;IAEb,OAAO,IAAI,CAACyE,eAAe,CAAC3E,GAAG,CAAC;EACpC;EAEA,IAAI+E,kBAAkB,GAAG,cAAc;EAEvC,SAASjB,WAAWA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACkB,YAAY;EAC5B;EAEA,IAAIC,cAAc,GAAG,IAAI;IACrBC,6BAA6B,GAAG,SAAS;EAE7C,SAAS7B,OAAOA,CAAClB,MAAM,EAAE;IACrB,OAAO,IAAI,CAACgD,QAAQ,CAACzB,OAAO,CAAC,IAAI,EAAEvB,MAAM,CAAC;EAC9C;EAEA,IAAIiD,mBAAmB,GAAG;IACtBC,MAAM,EAAE,OAAO;IACfC,IAAI,EAAE,QAAQ;IACdC,CAAC,EAAE,eAAe;IAClBC,EAAE,EAAE,YAAY;IAChBzI,CAAC,EAAE,UAAU;IACb0I,EAAE,EAAE,YAAY;IAChBC,CAAC,EAAE,SAAS;IACZC,EAAE,EAAE,UAAU;IACdC,CAAC,EAAE,OAAO;IACVC,EAAE,EAAE,SAAS;IACbC,CAAC,EAAE,QAAQ;IACXC,EAAE,EAAE,UAAU;IACdC,CAAC,EAAE,SAAS;IACZC,EAAE,EAAE,WAAW;IACfC,CAAC,EAAE,QAAQ;IACXC,EAAE,EAAE;EACR,CAAC;EAED,SAASC,YAAYA,CAACjE,MAAM,EAAEkE,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IAC3D,IAAIvE,MAAM,GAAG,IAAI,CAACwE,aAAa,CAACF,MAAM,CAAC;IACvC,OAAO9F,UAAU,CAACwB,MAAM,CAAC,GACnBA,MAAM,CAACG,MAAM,EAAEkE,aAAa,EAAEC,MAAM,EAAEC,QAAQ,CAAC,GAC/CvE,MAAM,CAAC0B,OAAO,CAAC,KAAK,EAAEvB,MAAM,CAAC;EACvC;EAEA,SAASsE,UAAUA,CAACC,IAAI,EAAE1E,MAAM,EAAE;IAC9B,IAAIxG,MAAM,GAAG,IAAI,CAACgL,aAAa,CAACE,IAAI,GAAG,CAAC,GAAG,QAAQ,GAAG,MAAM,CAAC;IAC7D,OAAOlG,UAAU,CAAChF,MAAM,CAAC,GAAGA,MAAM,CAACwG,MAAM,CAAC,GAAGxG,MAAM,CAACkI,OAAO,CAAC,KAAK,EAAE1B,MAAM,CAAC;EAC9E;EAEA,IAAI2E,OAAO,GAAG,CAAC,CAAC;EAEhB,SAASC,YAAYA,CAACC,IAAI,EAAEC,SAAS,EAAE;IACnC,IAAIC,SAAS,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAClCL,OAAO,CAACI,SAAS,CAAC,GAAGJ,OAAO,CAACI,SAAS,GAAG,GAAG,CAAC,GAAGJ,OAAO,CAACG,SAAS,CAAC,GAAGD,IAAI;EAC7E;EAEA,SAASI,cAAcA,CAACC,KAAK,EAAE;IAC3B,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAC1BP,OAAO,CAACO,KAAK,CAAC,IAAIP,OAAO,CAACO,KAAK,CAACF,WAAW,CAAC,CAAC,CAAC,GAC9ChJ,SAAS;EACnB;EAEA,SAASmJ,oBAAoBA,CAACC,WAAW,EAAE;IACvC,IAAIC,eAAe,GAAG,CAAC,CAAC;MACpBC,cAAc;MACd7I,IAAI;IAER,KAAKA,IAAI,IAAI2I,WAAW,EAAE;MACtB,IAAIlN,UAAU,CAACkN,WAAW,EAAE3I,IAAI,CAAC,EAAE;QAC/B6I,cAAc,GAAGL,cAAc,CAACxI,IAAI,CAAC;QACrC,IAAI6I,cAAc,EAAE;UAChBD,eAAe,CAACC,cAAc,CAAC,GAAGF,WAAW,CAAC3I,IAAI,CAAC;QACvD;MACJ;IACJ;IAEA,OAAO4I,eAAe;EAC1B;EAEA,IAAIE,UAAU,GAAG,CAAC,CAAC;EAEnB,SAASC,eAAeA,CAACX,IAAI,EAAEY,QAAQ,EAAE;IACrCF,UAAU,CAACV,IAAI,CAAC,GAAGY,QAAQ;EAC/B;EAEA,SAASC,mBAAmBA,CAACC,QAAQ,EAAE;IACnC,IAAIT,KAAK,GAAG,EAAE;MACVU,CAAC;IACL,KAAKA,CAAC,IAAID,QAAQ,EAAE;MAChB,IAAIzN,UAAU,CAACyN,QAAQ,EAAEC,CAAC,CAAC,EAAE;QACzBV,KAAK,CAAC9L,IAAI,CAAC;UAAEyL,IAAI,EAAEe,CAAC;UAAEH,QAAQ,EAAEF,UAAU,CAACK,CAAC;QAAE,CAAC,CAAC;MACpD;IACJ;IACAV,KAAK,CAACW,IAAI,CAAC,UAAU1N,CAAC,EAAEC,CAAC,EAAE;MACvB,OAAOD,CAAC,CAACsN,QAAQ,GAAGrN,CAAC,CAACqN,QAAQ;IAClC,CAAC,CAAC;IACF,OAAOP,KAAK;EAChB;EAEA,SAASY,UAAUA,CAACC,IAAI,EAAE;IACtB,OAAQA,IAAI,GAAG,CAAC,KAAK,CAAC,IAAIA,IAAI,GAAG,GAAG,KAAK,CAAC,IAAKA,IAAI,GAAG,GAAG,KAAK,CAAC;EACnE;EAEA,SAASC,QAAQA,CAAC7F,MAAM,EAAE;IACtB,IAAIA,MAAM,GAAG,CAAC,EAAE;MACZ;MACA,OAAOI,IAAI,CAAC0F,IAAI,CAAC9F,MAAM,CAAC,IAAI,CAAC;IACjC,CAAC,MAAM;MACH,OAAOI,IAAI,CAAC2F,KAAK,CAAC/F,MAAM,CAAC;IAC7B;EACJ;EAEA,SAASgG,KAAKA,CAACC,mBAAmB,EAAE;IAChC,IAAIC,aAAa,GAAG,CAACD,mBAAmB;MACpCE,KAAK,GAAG,CAAC;IAEb,IAAID,aAAa,KAAK,CAAC,IAAIE,QAAQ,CAACF,aAAa,CAAC,EAAE;MAChDC,KAAK,GAAGN,QAAQ,CAACK,aAAa,CAAC;IACnC;IAEA,OAAOC,KAAK;EAChB;EAEA,SAASE,UAAUA,CAAC3B,IAAI,EAAE4B,QAAQ,EAAE;IAChC,OAAO,UAAUH,KAAK,EAAE;MACpB,IAAIA,KAAK,IAAI,IAAI,EAAE;QACfI,KAAK,CAAC,IAAI,EAAE7B,IAAI,EAAEyB,KAAK,CAAC;QACxBjP,KAAK,CAACgG,YAAY,CAAC,IAAI,EAAEoJ,QAAQ,CAAC;QAClC,OAAO,IAAI;MACf,CAAC,MAAM;QACH,OAAOE,GAAG,CAAC,IAAI,EAAE9B,IAAI,CAAC;MAC1B;IACJ,CAAC;EACL;EAEA,SAAS8B,GAAGA,CAAC7G,GAAG,EAAE+E,IAAI,EAAE;IACpB,OAAO/E,GAAG,CAACzE,OAAO,CAAC,CAAC,GACdyE,GAAG,CAACnE,EAAE,CAAC,KAAK,IAAImE,GAAG,CAAC9C,MAAM,GAAG,KAAK,GAAG,EAAE,CAAC,GAAG6H,IAAI,CAAC,CAAC,CAAC,GAClD1I,GAAG;EACb;EAEA,SAASuK,KAAKA,CAAC5G,GAAG,EAAE+E,IAAI,EAAEyB,KAAK,EAAE;IAC7B,IAAIxG,GAAG,CAACzE,OAAO,CAAC,CAAC,IAAI,CAACK,KAAK,CAAC4K,KAAK,CAAC,EAAE;MAChC,IACIzB,IAAI,KAAK,UAAU,IACnBiB,UAAU,CAAChG,GAAG,CAACiG,IAAI,CAAC,CAAC,CAAC,IACtBjG,GAAG,CAAC8G,KAAK,CAAC,CAAC,KAAK,CAAC,IACjB9G,GAAG,CAAC+G,IAAI,CAAC,CAAC,KAAK,EAAE,EACnB;QACEP,KAAK,GAAGH,KAAK,CAACG,KAAK,CAAC;QACpBxG,GAAG,CAACnE,EAAE,CAAC,KAAK,IAAImE,GAAG,CAAC9C,MAAM,GAAG,KAAK,GAAG,EAAE,CAAC,GAAG6H,IAAI,CAAC,CAC5CyB,KAAK,EACLxG,GAAG,CAAC8G,KAAK,CAAC,CAAC,EACXE,WAAW,CAACR,KAAK,EAAExG,GAAG,CAAC8G,KAAK,CAAC,CAAC,CAClC,CAAC;MACL,CAAC,MAAM;QACH9G,GAAG,CAACnE,EAAE,CAAC,KAAK,IAAImE,GAAG,CAAC9C,MAAM,GAAG,KAAK,GAAG,EAAE,CAAC,GAAG6H,IAAI,CAAC,CAACyB,KAAK,CAAC;MAC3D;IACJ;EACJ;;EAEA;;EAEA,SAASS,SAASA,CAAC7B,KAAK,EAAE;IACtBA,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC;IAC7B,IAAI1G,UAAU,CAAC,IAAI,CAAC0G,KAAK,CAAC,CAAC,EAAE;MACzB,OAAO,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC;IACxB;IACA,OAAO,IAAI;EACf;EAEA,SAAS8B,SAASA,CAAC9B,KAAK,EAAEoB,KAAK,EAAE;IAC7B,IAAI,OAAOpB,KAAK,KAAK,QAAQ,EAAE;MAC3BA,KAAK,GAAGC,oBAAoB,CAACD,KAAK,CAAC;MACnC,IAAI+B,WAAW,GAAGvB,mBAAmB,CAACR,KAAK,CAAC;QACxC/L,CAAC;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8N,WAAW,CAACxO,MAAM,EAAEU,CAAC,EAAE,EAAE;QACrC,IAAI,CAAC8N,WAAW,CAAC9N,CAAC,CAAC,CAAC0L,IAAI,CAAC,CAACK,KAAK,CAAC+B,WAAW,CAAC9N,CAAC,CAAC,CAAC0L,IAAI,CAAC,CAAC;MACzD;IACJ,CAAC,MAAM;MACHK,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC;MAC7B,IAAI1G,UAAU,CAAC,IAAI,CAAC0G,KAAK,CAAC,CAAC,EAAE;QACzB,OAAO,IAAI,CAACA,KAAK,CAAC,CAACoB,KAAK,CAAC;MAC7B;IACJ;IACA,OAAO,IAAI;EACf;EAEA,IAAIY,MAAM,GAAG,IAAI;IAAE;IACfC,MAAM,GAAG,MAAM;IAAE;IACjBC,MAAM,GAAG,OAAO;IAAE;IAClBC,MAAM,GAAG,OAAO;IAAE;IAClBC,MAAM,GAAG,YAAY;IAAE;IACvBC,SAAS,GAAG,OAAO;IAAE;IACrBC,SAAS,GAAG,WAAW;IAAE;IACzBC,SAAS,GAAG,eAAe;IAAE;IAC7BC,SAAS,GAAG,SAAS;IAAE;IACvBC,SAAS,GAAG,SAAS;IAAE;IACvBC,SAAS,GAAG,cAAc;IAAE;IAC5BC,aAAa,GAAG,KAAK;IAAE;IACvBC,WAAW,GAAG,UAAU;IAAE;IAC1BC,WAAW,GAAG,oBAAoB;IAAE;IACpCC,gBAAgB,GAAG,yBAAyB;IAAE;IAC9CC,cAAc,GAAG,sBAAsB;IAAE;IACzC;IACA;IACAC,SAAS,GAAG,uJAAuJ;IACnKC,OAAO;EAEXA,OAAO,GAAG,CAAC,CAAC;EAEZ,SAASC,aAAaA,CAACjH,KAAK,EAAEkH,KAAK,EAAEC,WAAW,EAAE;IAC9CH,OAAO,CAAChH,KAAK,CAAC,GAAG3C,UAAU,CAAC6J,KAAK,CAAC,GAC5BA,KAAK,GACL,UAAUE,QAAQ,EAAEhH,UAAU,EAAE;MAC5B,OAAOgH,QAAQ,IAAID,WAAW,GAAGA,WAAW,GAAGD,KAAK;IACxD,CAAC;EACX;EAEA,SAASG,qBAAqBA,CAACrH,KAAK,EAAE/D,MAAM,EAAE;IAC1C,IAAI,CAAClF,UAAU,CAACiQ,OAAO,EAAEhH,KAAK,CAAC,EAAE;MAC7B,OAAO,IAAItC,MAAM,CAAC4J,cAAc,CAACtH,KAAK,CAAC,CAAC;IAC5C;IAEA,OAAOgH,OAAO,CAAChH,KAAK,CAAC,CAAC/D,MAAM,CAACtB,OAAO,EAAEsB,MAAM,CAACF,OAAO,CAAC;EACzD;;EAEA;EACA,SAASuL,cAAcA,CAAClF,CAAC,EAAE;IACvB,OAAOmF,WAAW,CACdnF,CAAC,CACI7B,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CACjBA,OAAO,CAAC,qCAAqC,EAAE,UAC5CiH,OAAO,EACPC,EAAE,EACFC,EAAE,EACFC,EAAE,EACFC,EAAE,EACJ;MACE,OAAOH,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE;IAC/B,CAAC,CACT,CAAC;EACL;EAEA,SAASL,WAAWA,CAACnF,CAAC,EAAE;IACpB,OAAOA,CAAC,CAAC7B,OAAO,CAAC,wBAAwB,EAAE,MAAM,CAAC;EACtD;EAEA,IAAIsH,MAAM,GAAG,CAAC,CAAC;EAEf,SAASC,aAAaA,CAAC9H,KAAK,EAAE1J,QAAQ,EAAE;IACpC,IAAI0B,CAAC;MACDmI,IAAI,GAAG7J,QAAQ;IACnB,IAAI,OAAO0J,KAAK,KAAK,QAAQ,EAAE;MAC3BA,KAAK,GAAG,CAACA,KAAK,CAAC;IACnB;IACA,IAAIvI,QAAQ,CAACnB,QAAQ,CAAC,EAAE;MACpB6J,IAAI,GAAG,SAAAA,CAAU3J,KAAK,EAAEiK,KAAK,EAAE;QAC3BA,KAAK,CAACnK,QAAQ,CAAC,GAAG0O,KAAK,CAACxO,KAAK,CAAC;MAClC,CAAC;IACL;IACA,KAAKwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgI,KAAK,CAAC1I,MAAM,EAAEU,CAAC,EAAE,EAAE;MAC/B6P,MAAM,CAAC7H,KAAK,CAAChI,CAAC,CAAC,CAAC,GAAGmI,IAAI;IAC3B;EACJ;EAEA,SAAS4H,iBAAiBA,CAAC/H,KAAK,EAAE1J,QAAQ,EAAE;IACxCwR,aAAa,CAAC9H,KAAK,EAAE,UAAUxJ,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE+D,KAAK,EAAE;MACxD/D,MAAM,CAAC+L,EAAE,GAAG/L,MAAM,CAAC+L,EAAE,IAAI,CAAC,CAAC;MAC3B1R,QAAQ,CAACE,KAAK,EAAEyF,MAAM,CAAC+L,EAAE,EAAE/L,MAAM,EAAE+D,KAAK,CAAC;IAC7C,CAAC,CAAC;EACN;EAEA,SAASiI,uBAAuBA,CAACjI,KAAK,EAAExJ,KAAK,EAAEyF,MAAM,EAAE;IACnD,IAAIzF,KAAK,IAAI,IAAI,IAAIO,UAAU,CAAC8Q,MAAM,EAAE7H,KAAK,CAAC,EAAE;MAC5C6H,MAAM,CAAC7H,KAAK,CAAC,CAACxJ,KAAK,EAAEyF,MAAM,CAACiM,EAAE,EAAEjM,MAAM,EAAE+D,KAAK,CAAC;IAClD;EACJ;EAEA,IAAImI,IAAI,GAAG,CAAC;IACRC,KAAK,GAAG,CAAC;IACTC,IAAI,GAAG,CAAC;IACRC,IAAI,GAAG,CAAC;IACRC,MAAM,GAAG,CAAC;IACVC,MAAM,GAAG,CAAC;IACVC,WAAW,GAAG,CAAC;IACfC,IAAI,GAAG,CAAC;IACRC,OAAO,GAAG,CAAC;EAEf,SAASC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACf,OAAO,CAAED,CAAC,GAAGC,CAAC,GAAIA,CAAC,IAAIA,CAAC;EAC5B;EAEA,IAAIC,OAAO;EAEX,IAAItS,KAAK,CAACE,SAAS,CAACoS,OAAO,EAAE;IACzBA,OAAO,GAAGtS,KAAK,CAACE,SAAS,CAACoS,OAAO;EACrC,CAAC,MAAM;IACHA,OAAO,GAAG,SAAAA,CAAUC,CAAC,EAAE;MACnB;MACA,IAAIhR,CAAC;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACV,MAAM,EAAE,EAAEU,CAAC,EAAE;QAC9B,IAAI,IAAI,CAACA,CAAC,CAAC,KAAKgR,CAAC,EAAE;UACf,OAAOhR,CAAC;QACZ;MACJ;MACA,OAAO,CAAC,CAAC;IACb,CAAC;EACL;EAEA,SAAS2N,WAAWA,CAACf,IAAI,EAAEa,KAAK,EAAE;IAC9B,IAAIlL,KAAK,CAACqK,IAAI,CAAC,IAAIrK,KAAK,CAACkL,KAAK,CAAC,EAAE;MAC7B,OAAOzK,GAAG;IACd;IACA,IAAIiO,QAAQ,GAAGL,GAAG,CAACnD,KAAK,EAAE,EAAE,CAAC;IAC7Bb,IAAI,IAAI,CAACa,KAAK,GAAGwD,QAAQ,IAAI,EAAE;IAC/B,OAAOA,QAAQ,KAAK,CAAC,GACftE,UAAU,CAACC,IAAI,CAAC,GACZ,EAAE,GACF,EAAE,GACN,EAAE,GAAKqE,QAAQ,GAAG,CAAC,GAAI,CAAE;EACnC;;EAEA;;EAEAlJ,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY;IAC7C,OAAO,IAAI,CAAC0F,KAAK,CAAC,CAAC,GAAG,CAAC;EAC3B,CAAC,CAAC;EAEF1F,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU1H,MAAM,EAAE;IAC1C,OAAO,IAAI,CAAC+H,UAAU,CAAC,CAAC,CAAC8I,WAAW,CAAC,IAAI,EAAE7Q,MAAM,CAAC;EACtD,CAAC,CAAC;EAEF0H,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU1H,MAAM,EAAE;IAC3C,OAAO,IAAI,CAAC+H,UAAU,CAAC,CAAC,CAAC+I,MAAM,CAAC,IAAI,EAAE9Q,MAAM,CAAC;EACjD,CAAC,CAAC;;EAEF;;EAEAoL,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC;;EAE1B;;EAEAY,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;;EAE3B;;EAEA4C,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EACtCiB,aAAa,CAAC,KAAK,EAAE,UAAUG,QAAQ,EAAE9O,MAAM,EAAE;IAC7C,OAAOA,MAAM,CAAC8Q,gBAAgB,CAAChC,QAAQ,CAAC;EAC5C,CAAC,CAAC;EACFH,aAAa,CAAC,MAAM,EAAE,UAAUG,QAAQ,EAAE9O,MAAM,EAAE;IAC9C,OAAOA,MAAM,CAAC+Q,WAAW,CAACjC,QAAQ,CAAC;EACvC,CAAC,CAAC;EAEFU,aAAa,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAE;IAC/CA,KAAK,CAAC2H,KAAK,CAAC,GAAGpD,KAAK,CAACxO,KAAK,CAAC,GAAG,CAAC;EACnC,CAAC,CAAC;EAEFsR,aAAa,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE+D,KAAK,EAAE;IAClE,IAAIyF,KAAK,GAAGxJ,MAAM,CAACF,OAAO,CAACuN,WAAW,CAAC9S,KAAK,EAAEwJ,KAAK,EAAE/D,MAAM,CAACtB,OAAO,CAAC;IACpE;IACA,IAAI8K,KAAK,IAAI,IAAI,EAAE;MACfhF,KAAK,CAAC2H,KAAK,CAAC,GAAG3C,KAAK;IACxB,CAAC,MAAM;MACH9L,eAAe,CAACsC,MAAM,CAAC,CAAC/C,YAAY,GAAG1C,KAAK;IAChD;EACJ,CAAC,CAAC;;EAEF;;EAEA,IAAI+S,mBAAmB,GAAG,uFAAuF,CAACC,KAAK,CAC/G,GACJ,CAAC;IACDC,wBAAwB,GAAG,iDAAiD,CAACD,KAAK,CAC9E,GACJ,CAAC;IACDE,gBAAgB,GAAG,+BAA+B;IAClDC,uBAAuB,GAAG5C,SAAS;IACnC6C,kBAAkB,GAAG7C,SAAS;EAElC,SAAS8C,YAAYA,CAACjQ,CAAC,EAAEvB,MAAM,EAAE;IAC7B,IAAI,CAACuB,CAAC,EAAE;MACJ,OAAOrD,OAAO,CAAC,IAAI,CAACuT,OAAO,CAAC,GACtB,IAAI,CAACA,OAAO,GACZ,IAAI,CAACA,OAAO,CAAC,YAAY,CAAC;IACpC;IACA,OAAOvT,OAAO,CAAC,IAAI,CAACuT,OAAO,CAAC,GACtB,IAAI,CAACA,OAAO,CAAClQ,CAAC,CAAC6L,KAAK,CAAC,CAAC,CAAC,GACvB,IAAI,CAACqE,OAAO,CACR,CAAC,IAAI,CAACA,OAAO,CAACC,QAAQ,IAAIL,gBAAgB,EAAE1I,IAAI,CAAC3I,MAAM,CAAC,GAClD,QAAQ,GACR,YAAY,CACrB,CAACuB,CAAC,CAAC6L,KAAK,CAAC,CAAC,CAAC;EACtB;EAEA,SAASuE,iBAAiBA,CAACpQ,CAAC,EAAEvB,MAAM,EAAE;IAClC,IAAI,CAACuB,CAAC,EAAE;MACJ,OAAOrD,OAAO,CAAC,IAAI,CAAC0T,YAAY,CAAC,GAC3B,IAAI,CAACA,YAAY,GACjB,IAAI,CAACA,YAAY,CAAC,YAAY,CAAC;IACzC;IACA,OAAO1T,OAAO,CAAC,IAAI,CAAC0T,YAAY,CAAC,GAC3B,IAAI,CAACA,YAAY,CAACrQ,CAAC,CAAC6L,KAAK,CAAC,CAAC,CAAC,GAC5B,IAAI,CAACwE,YAAY,CACbP,gBAAgB,CAAC1I,IAAI,CAAC3I,MAAM,CAAC,GAAG,QAAQ,GAAG,YAAY,CAC1D,CAACuB,CAAC,CAAC6L,KAAK,CAAC,CAAC,CAAC;EACtB;EAEA,SAASyE,iBAAiBA,CAACC,SAAS,EAAE9R,MAAM,EAAEE,MAAM,EAAE;IAClD,IAAIP,CAAC;MACDoS,EAAE;MACFzL,GAAG;MACH0L,GAAG,GAAGF,SAAS,CAACG,iBAAiB,CAAC,CAAC;IACvC,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;MACpB;MACA,IAAI,CAACA,YAAY,GAAG,EAAE;MACtB,IAAI,CAACC,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACC,iBAAiB,GAAG,EAAE;MAC3B,KAAKzS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;QACrB2G,GAAG,GAAGvG,SAAS,CAAC,CAAC,IAAI,EAAEJ,CAAC,CAAC,CAAC;QAC1B,IAAI,CAACyS,iBAAiB,CAACzS,CAAC,CAAC,GAAG,IAAI,CAACkR,WAAW,CACxCvK,GAAG,EACH,EACJ,CAAC,CAAC2L,iBAAiB,CAAC,CAAC;QACrB,IAAI,CAACE,gBAAgB,CAACxS,CAAC,CAAC,GAAG,IAAI,CAACmR,MAAM,CAACxK,GAAG,EAAE,EAAE,CAAC,CAAC2L,iBAAiB,CAAC,CAAC;MACvE;IACJ;IAEA,IAAI/R,MAAM,EAAE;MACR,IAAIF,MAAM,KAAK,KAAK,EAAE;QAClB+R,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAAC4T,iBAAiB,EAAEJ,GAAG,CAAC;QAC9C,OAAOD,EAAE,KAAK,CAAC,CAAC,GAAGA,EAAE,GAAG,IAAI;MAChC,CAAC,MAAM;QACHA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAAC2T,gBAAgB,EAAEH,GAAG,CAAC;QAC7C,OAAOD,EAAE,KAAK,CAAC,CAAC,GAAGA,EAAE,GAAG,IAAI;MAChC;IACJ,CAAC,MAAM;MACH,IAAI/R,MAAM,KAAK,KAAK,EAAE;QAClB+R,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAAC4T,iBAAiB,EAAEJ,GAAG,CAAC;QAC9C,IAAID,EAAE,KAAK,CAAC,CAAC,EAAE;UACX,OAAOA,EAAE;QACb;QACAA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAAC2T,gBAAgB,EAAEH,GAAG,CAAC;QAC7C,OAAOD,EAAE,KAAK,CAAC,CAAC,GAAGA,EAAE,GAAG,IAAI;MAChC,CAAC,MAAM;QACHA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAAC2T,gBAAgB,EAAEH,GAAG,CAAC;QAC7C,IAAID,EAAE,KAAK,CAAC,CAAC,EAAE;UACX,OAAOA,EAAE;QACb;QACAA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAAC4T,iBAAiB,EAAEJ,GAAG,CAAC;QAC9C,OAAOD,EAAE,KAAK,CAAC,CAAC,GAAGA,EAAE,GAAG,IAAI;MAChC;IACJ;EACJ;EAEA,SAASM,iBAAiBA,CAACP,SAAS,EAAE9R,MAAM,EAAEE,MAAM,EAAE;IAClD,IAAIP,CAAC,EAAE2G,GAAG,EAAEuI,KAAK;IAEjB,IAAI,IAAI,CAACyD,iBAAiB,EAAE;MACxB,OAAOT,iBAAiB,CAACrT,IAAI,CAAC,IAAI,EAAEsT,SAAS,EAAE9R,MAAM,EAAEE,MAAM,CAAC;IAClE;IAEA,IAAI,CAAC,IAAI,CAACgS,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GAAG,EAAE;MACtB,IAAI,CAACC,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC/B;;IAEA;IACA;IACA;IACA,KAAKzS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACrB;MACA2G,GAAG,GAAGvG,SAAS,CAAC,CAAC,IAAI,EAAEJ,CAAC,CAAC,CAAC;MAC1B,IAAIO,MAAM,IAAI,CAAC,IAAI,CAACiS,gBAAgB,CAACxS,CAAC,CAAC,EAAE;QACrC,IAAI,CAACwS,gBAAgB,CAACxS,CAAC,CAAC,GAAG,IAAI0F,MAAM,CACjC,GAAG,GAAG,IAAI,CAACyL,MAAM,CAACxK,GAAG,EAAE,EAAE,CAAC,CAAC4B,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,EACjD,GACJ,CAAC;QACD,IAAI,CAACkK,iBAAiB,CAACzS,CAAC,CAAC,GAAG,IAAI0F,MAAM,CAClC,GAAG,GAAG,IAAI,CAACwL,WAAW,CAACvK,GAAG,EAAE,EAAE,CAAC,CAAC4B,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,EACtD,GACJ,CAAC;MACL;MACA,IAAI,CAAChI,MAAM,IAAI,CAAC,IAAI,CAACgS,YAAY,CAACvS,CAAC,CAAC,EAAE;QAClCkP,KAAK,GACD,GAAG,GAAG,IAAI,CAACiC,MAAM,CAACxK,GAAG,EAAE,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAACuK,WAAW,CAACvK,GAAG,EAAE,EAAE,CAAC;QACjE,IAAI,CAAC4L,YAAY,CAACvS,CAAC,CAAC,GAAG,IAAI0F,MAAM,CAACwJ,KAAK,CAAC3G,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC;MAClE;MACA;MACA,IACIhI,MAAM,IACNF,MAAM,KAAK,MAAM,IACjB,IAAI,CAACmS,gBAAgB,CAACxS,CAAC,CAAC,CAACgJ,IAAI,CAACmJ,SAAS,CAAC,EAC1C;QACE,OAAOnS,CAAC;MACZ,CAAC,MAAM,IACHO,MAAM,IACNF,MAAM,KAAK,KAAK,IAChB,IAAI,CAACoS,iBAAiB,CAACzS,CAAC,CAAC,CAACgJ,IAAI,CAACmJ,SAAS,CAAC,EAC3C;QACE,OAAOnS,CAAC;MACZ,CAAC,MAAM,IAAI,CAACO,MAAM,IAAI,IAAI,CAACgS,YAAY,CAACvS,CAAC,CAAC,CAACgJ,IAAI,CAACmJ,SAAS,CAAC,EAAE;QACxD,OAAOnS,CAAC;MACZ;IACJ;EACJ;;EAEA;;EAEA,SAAS4S,QAAQA,CAACjM,GAAG,EAAEwG,KAAK,EAAE;IAC1B,IAAI0F,UAAU;IAEd,IAAI,CAAClM,GAAG,CAACzE,OAAO,CAAC,CAAC,EAAE;MAChB;MACA,OAAOyE,GAAG;IACd;IAEA,IAAI,OAAOwG,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,OAAO,CAACnE,IAAI,CAACmE,KAAK,CAAC,EAAE;QACrBA,KAAK,GAAGH,KAAK,CAACG,KAAK,CAAC;MACxB,CAAC,MAAM;QACHA,KAAK,GAAGxG,GAAG,CAACyB,UAAU,CAAC,CAAC,CAACkJ,WAAW,CAACnE,KAAK,CAAC;QAC3C;QACA,IAAI,CAAC1N,QAAQ,CAAC0N,KAAK,CAAC,EAAE;UAClB,OAAOxG,GAAG;QACd;MACJ;IACJ;IAEAkM,UAAU,GAAGzL,IAAI,CAAC0L,GAAG,CAACnM,GAAG,CAAC+G,IAAI,CAAC,CAAC,EAAEC,WAAW,CAAChH,GAAG,CAACiG,IAAI,CAAC,CAAC,EAAEO,KAAK,CAAC,CAAC;IACjExG,GAAG,CAACnE,EAAE,CAAC,KAAK,IAAImE,GAAG,CAAC9C,MAAM,GAAG,KAAK,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,CAACsJ,KAAK,EAAE0F,UAAU,CAAC;IACtE,OAAOlM,GAAG;EACd;EAEA,SAASoM,WAAWA,CAAC5F,KAAK,EAAE;IACxB,IAAIA,KAAK,IAAI,IAAI,EAAE;MACfyF,QAAQ,CAAC,IAAI,EAAEzF,KAAK,CAAC;MACrBjP,KAAK,CAACgG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;MAC9B,OAAO,IAAI;IACf,CAAC,MAAM;MACH,OAAOsJ,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC;IAC7B;EACJ;EAEA,SAASwF,cAAcA,CAAA,EAAG;IACtB,OAAOrF,WAAW,CAAC,IAAI,CAACf,IAAI,CAAC,CAAC,EAAE,IAAI,CAACa,KAAK,CAAC,CAAC,CAAC;EACjD;EAEA,SAAS2D,gBAAgBA,CAAChC,QAAQ,EAAE;IAChC,IAAI,IAAI,CAACuD,iBAAiB,EAAE;MACxB,IAAI,CAAC5T,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC,EAAE;QACnCkU,kBAAkB,CAACpU,IAAI,CAAC,IAAI,CAAC;MACjC;MACA,IAAIuQ,QAAQ,EAAE;QACV,OAAO,IAAI,CAAC8D,uBAAuB;MACvC,CAAC,MAAM;QACH,OAAO,IAAI,CAACC,iBAAiB;MACjC;IACJ,CAAC,MAAM;MACH,IAAI,CAACpU,UAAU,CAAC,IAAI,EAAE,mBAAmB,CAAC,EAAE;QACxC,IAAI,CAACoU,iBAAiB,GAAGxB,uBAAuB;MACpD;MACA,OAAO,IAAI,CAACuB,uBAAuB,IAAI9D,QAAQ,GACzC,IAAI,CAAC8D,uBAAuB,GAC5B,IAAI,CAACC,iBAAiB;IAChC;EACJ;EAEA,SAAS9B,WAAWA,CAACjC,QAAQ,EAAE;IAC3B,IAAI,IAAI,CAACuD,iBAAiB,EAAE;MACxB,IAAI,CAAC5T,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC,EAAE;QACnCkU,kBAAkB,CAACpU,IAAI,CAAC,IAAI,CAAC;MACjC;MACA,IAAIuQ,QAAQ,EAAE;QACV,OAAO,IAAI,CAACgE,kBAAkB;MAClC,CAAC,MAAM;QACH,OAAO,IAAI,CAACC,YAAY;MAC5B;IACJ,CAAC,MAAM;MACH,IAAI,CAACtU,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC,EAAE;QACnC,IAAI,CAACsU,YAAY,GAAGzB,kBAAkB;MAC1C;MACA,OAAO,IAAI,CAACwB,kBAAkB,IAAIhE,QAAQ,GACpC,IAAI,CAACgE,kBAAkB,GACvB,IAAI,CAACC,YAAY;IAC3B;EACJ;EAEA,SAASJ,kBAAkBA,CAAA,EAAG;IAC1B,SAASK,SAASA,CAACtU,CAAC,EAAEC,CAAC,EAAE;MACrB,OAAOA,CAAC,CAACK,MAAM,GAAGN,CAAC,CAACM,MAAM;IAC9B;IAEA,IAAIiU,WAAW,GAAG,EAAE;MAChBC,UAAU,GAAG,EAAE;MACfC,WAAW,GAAG,EAAE;MAChBzT,CAAC;MACD2G,GAAG;IACP,KAAK3G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACrB;MACA2G,GAAG,GAAGvG,SAAS,CAAC,CAAC,IAAI,EAAEJ,CAAC,CAAC,CAAC;MAC1BuT,WAAW,CAACtT,IAAI,CAAC,IAAI,CAACiR,WAAW,CAACvK,GAAG,EAAE,EAAE,CAAC,CAAC;MAC3C6M,UAAU,CAACvT,IAAI,CAAC,IAAI,CAACkR,MAAM,CAACxK,GAAG,EAAE,EAAE,CAAC,CAAC;MACrC8M,WAAW,CAACxT,IAAI,CAAC,IAAI,CAACkR,MAAM,CAACxK,GAAG,EAAE,EAAE,CAAC,CAAC;MACtC8M,WAAW,CAACxT,IAAI,CAAC,IAAI,CAACiR,WAAW,CAACvK,GAAG,EAAE,EAAE,CAAC,CAAC;IAC/C;IACA;IACA;IACA4M,WAAW,CAAC7G,IAAI,CAAC4G,SAAS,CAAC;IAC3BE,UAAU,CAAC9G,IAAI,CAAC4G,SAAS,CAAC;IAC1BG,WAAW,CAAC/G,IAAI,CAAC4G,SAAS,CAAC;IAC3B,KAAKtT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACrBuT,WAAW,CAACvT,CAAC,CAAC,GAAGuP,WAAW,CAACgE,WAAW,CAACvT,CAAC,CAAC,CAAC;MAC5CwT,UAAU,CAACxT,CAAC,CAAC,GAAGuP,WAAW,CAACiE,UAAU,CAACxT,CAAC,CAAC,CAAC;IAC9C;IACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACrByT,WAAW,CAACzT,CAAC,CAAC,GAAGuP,WAAW,CAACkE,WAAW,CAACzT,CAAC,CAAC,CAAC;IAChD;IAEA,IAAI,CAACqT,YAAY,GAAG,IAAI3N,MAAM,CAAC,IAAI,GAAG+N,WAAW,CAAC1O,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC;IACvE,IAAI,CAACoO,iBAAiB,GAAG,IAAI,CAACE,YAAY;IAC1C,IAAI,CAACD,kBAAkB,GAAG,IAAI1N,MAAM,CAChC,IAAI,GAAG8N,UAAU,CAACzO,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EACjC,GACJ,CAAC;IACD,IAAI,CAACmO,uBAAuB,GAAG,IAAIxN,MAAM,CACrC,IAAI,GAAG6N,WAAW,CAACxO,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAClC,GACJ,CAAC;EACL;;EAEA;;EAEAgD,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY;IAClC,IAAIgD,CAAC,GAAG,IAAI,CAAC6B,IAAI,CAAC,CAAC;IACnB,OAAO7B,CAAC,IAAI,IAAI,GAAGhE,QAAQ,CAACgE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGA,CAAC;EAC/C,CAAC,CAAC;EAEFhD,cAAc,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY;IACxC,OAAO,IAAI,CAAC6E,IAAI,CAAC,CAAC,GAAG,GAAG;EAC5B,CAAC,CAAC;EAEF7E,cAAc,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;EACzCA,cAAc,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;EAC1CA,cAAc,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;;EAEjD;;EAEA0D,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;;EAEzB;;EAEAY,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;;EAE1B;;EAEA4C,aAAa,CAAC,GAAG,EAAEN,WAAW,CAAC;EAC/BM,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EACtCiB,aAAa,CAAC,MAAM,EAAET,SAAS,EAAEN,MAAM,CAAC;EACxCe,aAAa,CAAC,OAAO,EAAER,SAAS,EAAEN,MAAM,CAAC;EACzCc,aAAa,CAAC,QAAQ,EAAER,SAAS,EAAEN,MAAM,CAAC;EAE1C2B,aAAa,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAEK,IAAI,CAAC;EACxCL,aAAa,CAAC,MAAM,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAE;IAC1CA,KAAK,CAAC0H,IAAI,CAAC,GACP3R,KAAK,CAACc,MAAM,KAAK,CAAC,GAAGpB,KAAK,CAACwV,iBAAiB,CAAClV,KAAK,CAAC,GAAGwO,KAAK,CAACxO,KAAK,CAAC;EAC1E,CAAC,CAAC;EACFsR,aAAa,CAAC,IAAI,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAE;IACxCA,KAAK,CAAC0H,IAAI,CAAC,GAAGjS,KAAK,CAACwV,iBAAiB,CAAClV,KAAK,CAAC;EAChD,CAAC,CAAC;EACFsR,aAAa,CAAC,GAAG,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAE;IACvCA,KAAK,CAAC0H,IAAI,CAAC,GAAGwD,QAAQ,CAACnV,KAAK,EAAE,EAAE,CAAC;EACrC,CAAC,CAAC;;EAEF;;EAEA,SAASoV,UAAUA,CAAChH,IAAI,EAAE;IACtB,OAAOD,UAAU,CAACC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;EACvC;;EAEA;;EAEA1O,KAAK,CAACwV,iBAAiB,GAAG,UAAUlV,KAAK,EAAE;IACvC,OAAOwO,KAAK,CAACxO,KAAK,CAAC,IAAIwO,KAAK,CAACxO,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;EAC3D,CAAC;;EAED;;EAEA,IAAIqV,UAAU,GAAGxG,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;EAE7C,SAASyG,aAAaA,CAAA,EAAG;IACrB,OAAOnH,UAAU,CAAC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;EAClC;EAEA,SAASmH,UAAUA,CAAChJ,CAAC,EAAEnJ,CAAC,EAAE6I,CAAC,EAAEF,CAAC,EAAEM,CAAC,EAAET,CAAC,EAAE4J,EAAE,EAAE;IACtC;IACA;IACA,IAAItG,IAAI;IACR;IACA,IAAI3C,CAAC,GAAG,GAAG,IAAIA,CAAC,IAAI,CAAC,EAAE;MACnB;MACA2C,IAAI,GAAG,IAAI/N,IAAI,CAACoL,CAAC,GAAG,GAAG,EAAEnJ,CAAC,EAAE6I,CAAC,EAAEF,CAAC,EAAEM,CAAC,EAAET,CAAC,EAAE4J,EAAE,CAAC;MAC3C,IAAI5G,QAAQ,CAACM,IAAI,CAACuG,WAAW,CAAC,CAAC,CAAC,EAAE;QAC9BvG,IAAI,CAACwG,WAAW,CAACnJ,CAAC,CAAC;MACvB;IACJ,CAAC,MAAM;MACH2C,IAAI,GAAG,IAAI/N,IAAI,CAACoL,CAAC,EAAEnJ,CAAC,EAAE6I,CAAC,EAAEF,CAAC,EAAEM,CAAC,EAAET,CAAC,EAAE4J,EAAE,CAAC;IACzC;IAEA,OAAOtG,IAAI;EACf;EAEA,SAASyG,aAAaA,CAACpJ,CAAC,EAAE;IACtB,IAAI2C,IAAI,EAAE/I,IAAI;IACd;IACA,IAAIoG,CAAC,GAAG,GAAG,IAAIA,CAAC,IAAI,CAAC,EAAE;MACnBpG,IAAI,GAAGlG,KAAK,CAACE,SAAS,CAACmG,KAAK,CAACjG,IAAI,CAACT,SAAS,CAAC;MAC5C;MACAuG,IAAI,CAAC,CAAC,CAAC,GAAGoG,CAAC,GAAG,GAAG;MACjB2C,IAAI,GAAG,IAAI/N,IAAI,CAACA,IAAI,CAACyU,GAAG,CAACjW,KAAK,CAAC,IAAI,EAAEwG,IAAI,CAAC,CAAC;MAC3C,IAAIyI,QAAQ,CAACM,IAAI,CAAC2G,cAAc,CAAC,CAAC,CAAC,EAAE;QACjC3G,IAAI,CAAC4G,cAAc,CAACvJ,CAAC,CAAC;MAC1B;IACJ,CAAC,MAAM;MACH2C,IAAI,GAAG,IAAI/N,IAAI,CAACA,IAAI,CAACyU,GAAG,CAACjW,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;IACpD;IAEA,OAAOsP,IAAI;EACf;;EAEA;EACA,SAAS6G,eAAeA,CAAC3H,IAAI,EAAE4H,GAAG,EAAEC,GAAG,EAAE;IACrC;MAAI;MACAC,GAAG,GAAG,CAAC,GAAGF,GAAG,GAAGC,GAAG;MACnB;MACAE,KAAK,GAAG,CAAC,CAAC,GAAGR,aAAa,CAACvH,IAAI,EAAE,CAAC,EAAE8H,GAAG,CAAC,CAACE,SAAS,CAAC,CAAC,GAAGJ,GAAG,IAAI,CAAC;IAEnE,OAAO,CAACG,KAAK,GAAGD,GAAG,GAAG,CAAC;EAC3B;;EAEA;EACA,SAASG,kBAAkBA,CAACjI,IAAI,EAAEkI,IAAI,EAAEC,OAAO,EAAEP,GAAG,EAAEC,GAAG,EAAE;IACvD,IAAIO,YAAY,GAAG,CAAC,CAAC,GAAGD,OAAO,GAAGP,GAAG,IAAI,CAAC;MACtCS,UAAU,GAAGV,eAAe,CAAC3H,IAAI,EAAE4H,GAAG,EAAEC,GAAG,CAAC;MAC5CS,SAAS,GAAG,CAAC,GAAG,CAAC,IAAIJ,IAAI,GAAG,CAAC,CAAC,GAAGE,YAAY,GAAGC,UAAU;MAC1DE,OAAO;MACPC,YAAY;IAEhB,IAAIF,SAAS,IAAI,CAAC,EAAE;MAChBC,OAAO,GAAGvI,IAAI,GAAG,CAAC;MAClBwI,YAAY,GAAGxB,UAAU,CAACuB,OAAO,CAAC,GAAGD,SAAS;IAClD,CAAC,MAAM,IAAIA,SAAS,GAAGtB,UAAU,CAAChH,IAAI,CAAC,EAAE;MACrCuI,OAAO,GAAGvI,IAAI,GAAG,CAAC;MAClBwI,YAAY,GAAGF,SAAS,GAAGtB,UAAU,CAAChH,IAAI,CAAC;IAC/C,CAAC,MAAM;MACHuI,OAAO,GAAGvI,IAAI;MACdwI,YAAY,GAAGF,SAAS;IAC5B;IAEA,OAAO;MACHtI,IAAI,EAAEuI,OAAO;MACbD,SAAS,EAAEE;IACf,CAAC;EACL;EAEA,SAASC,UAAUA,CAAC1O,GAAG,EAAE6N,GAAG,EAAEC,GAAG,EAAE;IAC/B,IAAIQ,UAAU,GAAGV,eAAe,CAAC5N,GAAG,CAACiG,IAAI,CAAC,CAAC,EAAE4H,GAAG,EAAEC,GAAG,CAAC;MAClDK,IAAI,GAAG1N,IAAI,CAAC2F,KAAK,CAAC,CAACpG,GAAG,CAACuO,SAAS,CAAC,CAAC,GAAGD,UAAU,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;MAC7DK,OAAO;MACPH,OAAO;IAEX,IAAIL,IAAI,GAAG,CAAC,EAAE;MACVK,OAAO,GAAGxO,GAAG,CAACiG,IAAI,CAAC,CAAC,GAAG,CAAC;MACxB0I,OAAO,GAAGR,IAAI,GAAGS,WAAW,CAACJ,OAAO,EAAEX,GAAG,EAAEC,GAAG,CAAC;IACnD,CAAC,MAAM,IAAIK,IAAI,GAAGS,WAAW,CAAC5O,GAAG,CAACiG,IAAI,CAAC,CAAC,EAAE4H,GAAG,EAAEC,GAAG,CAAC,EAAE;MACjDa,OAAO,GAAGR,IAAI,GAAGS,WAAW,CAAC5O,GAAG,CAACiG,IAAI,CAAC,CAAC,EAAE4H,GAAG,EAAEC,GAAG,CAAC;MAClDU,OAAO,GAAGxO,GAAG,CAACiG,IAAI,CAAC,CAAC,GAAG,CAAC;IAC5B,CAAC,MAAM;MACHuI,OAAO,GAAGxO,GAAG,CAACiG,IAAI,CAAC,CAAC;MACpB0I,OAAO,GAAGR,IAAI;IAClB;IAEA,OAAO;MACHA,IAAI,EAAEQ,OAAO;MACb1I,IAAI,EAAEuI;IACV,CAAC;EACL;EAEA,SAASI,WAAWA,CAAC3I,IAAI,EAAE4H,GAAG,EAAEC,GAAG,EAAE;IACjC,IAAIQ,UAAU,GAAGV,eAAe,CAAC3H,IAAI,EAAE4H,GAAG,EAAEC,GAAG,CAAC;MAC5Ce,cAAc,GAAGjB,eAAe,CAAC3H,IAAI,GAAG,CAAC,EAAE4H,GAAG,EAAEC,GAAG,CAAC;IACxD,OAAO,CAACb,UAAU,CAAChH,IAAI,CAAC,GAAGqI,UAAU,GAAGO,cAAc,IAAI,CAAC;EAC/D;;EAEA;;EAEAzN,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC;EAC5CA,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC;;EAE/C;;EAEA0D,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;EACzBA,YAAY,CAAC,SAAS,EAAE,GAAG,CAAC;;EAE5B;;EAEAY,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;EAC1BA,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC;;EAE7B;;EAEA4C,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EACtCiB,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EAEtC+B,iBAAiB,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,UACtCvR,KAAK,EACLsW,IAAI,EACJ7Q,MAAM,EACN+D,KAAK,EACP;IACE8M,IAAI,CAAC9M,KAAK,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGsF,KAAK,CAACxO,KAAK,CAAC;EAC3C,CAAC,CAAC;;EAEF;;EAEA;;EAEA,SAASiX,UAAUA,CAAC9O,GAAG,EAAE;IACrB,OAAO0O,UAAU,CAAC1O,GAAG,EAAE,IAAI,CAAC+O,KAAK,CAAClB,GAAG,EAAE,IAAI,CAACkB,KAAK,CAACjB,GAAG,CAAC,CAACK,IAAI;EAC/D;EAEA,IAAIa,iBAAiB,GAAG;IACpBnB,GAAG,EAAE,CAAC;IAAE;IACRC,GAAG,EAAE,CAAC,CAAE;EACZ,CAAC;EAED,SAASmB,oBAAoBA,CAAA,EAAG;IAC5B,OAAO,IAAI,CAACF,KAAK,CAAClB,GAAG;EACzB;EAEA,SAASqB,oBAAoBA,CAAA,EAAG;IAC5B,OAAO,IAAI,CAACH,KAAK,CAACjB,GAAG;EACzB;;EAEA;;EAEA,SAASqB,UAAUA,CAACtX,KAAK,EAAE;IACvB,IAAIsW,IAAI,GAAG,IAAI,CAAC1M,UAAU,CAAC,CAAC,CAAC0M,IAAI,CAAC,IAAI,CAAC;IACvC,OAAOtW,KAAK,IAAI,IAAI,GAAGsW,IAAI,GAAG,IAAI,CAACiB,GAAG,CAAC,CAACvX,KAAK,GAAGsW,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC;EACnE;EAEA,SAASkB,aAAaA,CAACxX,KAAK,EAAE;IAC1B,IAAIsW,IAAI,GAAGO,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAACP,IAAI;IACtC,OAAOtW,KAAK,IAAI,IAAI,GAAGsW,IAAI,GAAG,IAAI,CAACiB,GAAG,CAAC,CAACvX,KAAK,GAAGsW,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC;EACnE;;EAEA;;EAEA/M,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;EAEnCA,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU1H,MAAM,EAAE;IACzC,OAAO,IAAI,CAAC+H,UAAU,CAAC,CAAC,CAAC6N,WAAW,CAAC,IAAI,EAAE5V,MAAM,CAAC;EACtD,CAAC,CAAC;EAEF0H,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU1H,MAAM,EAAE;IAC1C,OAAO,IAAI,CAAC+H,UAAU,CAAC,CAAC,CAAC8N,aAAa,CAAC,IAAI,EAAE7V,MAAM,CAAC;EACxD,CAAC,CAAC;EAEF0H,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU1H,MAAM,EAAE;IAC3C,OAAO,IAAI,CAAC+H,UAAU,CAAC,CAAC,CAAC+N,QAAQ,CAAC,IAAI,EAAE9V,MAAM,CAAC;EACnD,CAAC,CAAC;EAEF0H,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;EACpCA,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC;;EAEvC;;EAEA0D,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC;EACxBA,YAAY,CAAC,SAAS,EAAE,GAAG,CAAC;EAC5BA,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;;EAE/B;EACAY,eAAe,CAAC,KAAK,EAAE,EAAE,CAAC;EAC1BA,eAAe,CAAC,SAAS,EAAE,EAAE,CAAC;EAC9BA,eAAe,CAAC,YAAY,EAAE,EAAE,CAAC;;EAEjC;;EAEA4C,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,IAAI,EAAE,UAAUG,QAAQ,EAAE9O,MAAM,EAAE;IAC5C,OAAOA,MAAM,CAAC8V,gBAAgB,CAAChH,QAAQ,CAAC;EAC5C,CAAC,CAAC;EACFH,aAAa,CAAC,KAAK,EAAE,UAAUG,QAAQ,EAAE9O,MAAM,EAAE;IAC7C,OAAOA,MAAM,CAAC+V,kBAAkB,CAACjH,QAAQ,CAAC;EAC9C,CAAC,CAAC;EACFH,aAAa,CAAC,MAAM,EAAE,UAAUG,QAAQ,EAAE9O,MAAM,EAAE;IAC9C,OAAOA,MAAM,CAACgW,aAAa,CAAClH,QAAQ,CAAC;EACzC,CAAC,CAAC;EAEFW,iBAAiB,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,UAAUvR,KAAK,EAAEsW,IAAI,EAAE7Q,MAAM,EAAE+D,KAAK,EAAE;IAC3E,IAAI+M,OAAO,GAAG9Q,MAAM,CAACF,OAAO,CAACwS,aAAa,CAAC/X,KAAK,EAAEwJ,KAAK,EAAE/D,MAAM,CAACtB,OAAO,CAAC;IACxE;IACA,IAAIoS,OAAO,IAAI,IAAI,EAAE;MACjBD,IAAI,CAACrK,CAAC,GAAGsK,OAAO;IACpB,CAAC,MAAM;MACHpT,eAAe,CAACsC,MAAM,CAAC,CAACvB,cAAc,GAAGlE,KAAK;IAClD;EACJ,CAAC,CAAC;EAEFuR,iBAAiB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,UAAUvR,KAAK,EAAEsW,IAAI,EAAE7Q,MAAM,EAAE+D,KAAK,EAAE;IACrE8M,IAAI,CAAC9M,KAAK,CAAC,GAAGgF,KAAK,CAACxO,KAAK,CAAC;EAC9B,CAAC,CAAC;;EAEF;;EAEA,SAASgY,YAAYA,CAAChY,KAAK,EAAE8B,MAAM,EAAE;IACjC,IAAI,OAAO9B,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAOA,KAAK;IAChB;IAEA,IAAI,CAAC+D,KAAK,CAAC/D,KAAK,CAAC,EAAE;MACf,OAAOmV,QAAQ,CAACnV,KAAK,EAAE,EAAE,CAAC;IAC9B;IAEAA,KAAK,GAAG8B,MAAM,CAACiW,aAAa,CAAC/X,KAAK,CAAC;IACnC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAOA,KAAK;IAChB;IAEA,OAAO,IAAI;EACf;EAEA,SAASiY,eAAeA,CAACjY,KAAK,EAAE8B,MAAM,EAAE;IACpC,IAAI,OAAO9B,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAO8B,MAAM,CAACiW,aAAa,CAAC/X,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;IAC/C;IACA,OAAO+D,KAAK,CAAC/D,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;EACtC;;EAEA;EACA,SAASkY,aAAaA,CAACC,EAAE,EAAE9F,CAAC,EAAE;IAC1B,OAAO8F,EAAE,CAAC7R,KAAK,CAAC+L,CAAC,EAAE,CAAC,CAAC,CAAC+F,MAAM,CAACD,EAAE,CAAC7R,KAAK,CAAC,CAAC,EAAE+L,CAAC,CAAC,CAAC;EAChD;EAEA,IAAIgG,qBAAqB,GAAG,0DAA0D,CAACrF,KAAK,CACpF,GACJ,CAAC;IACDsF,0BAA0B,GAAG,6BAA6B,CAACtF,KAAK,CAAC,GAAG,CAAC;IACrEuF,wBAAwB,GAAG,sBAAsB,CAACvF,KAAK,CAAC,GAAG,CAAC;IAC5DwF,oBAAoB,GAAGjI,SAAS;IAChCkI,yBAAyB,GAAGlI,SAAS;IACrCmI,uBAAuB,GAAGnI,SAAS;EAEvC,SAASoI,cAAcA,CAACvV,CAAC,EAAEvB,MAAM,EAAE;IAC/B,IAAI8V,QAAQ,GAAG5X,OAAO,CAAC,IAAI,CAAC6Y,SAAS,CAAC,GAChC,IAAI,CAACA,SAAS,GACd,IAAI,CAACA,SAAS,CACVxV,CAAC,IAAIA,CAAC,KAAK,IAAI,IAAI,IAAI,CAACwV,SAAS,CAACrF,QAAQ,CAAC/I,IAAI,CAAC3I,MAAM,CAAC,GACjD,QAAQ,GACR,YAAY,CACrB;IACP,OAAOuB,CAAC,KAAK,IAAI,GACX8U,aAAa,CAACP,QAAQ,EAAE,IAAI,CAACT,KAAK,CAAClB,GAAG,CAAC,GACvC5S,CAAC,GACDuU,QAAQ,CAACvU,CAAC,CAACyV,GAAG,CAAC,CAAC,CAAC,GACjBlB,QAAQ;EAClB;EAEA,SAASmB,mBAAmBA,CAAC1V,CAAC,EAAE;IAC5B,OAAOA,CAAC,KAAK,IAAI,GACX8U,aAAa,CAAC,IAAI,CAACa,cAAc,EAAE,IAAI,CAAC7B,KAAK,CAAClB,GAAG,CAAC,GAClD5S,CAAC,GACD,IAAI,CAAC2V,cAAc,CAAC3V,CAAC,CAACyV,GAAG,CAAC,CAAC,CAAC,GAC5B,IAAI,CAACE,cAAc;EAC7B;EAEA,SAASC,iBAAiBA,CAAC5V,CAAC,EAAE;IAC1B,OAAOA,CAAC,KAAK,IAAI,GACX8U,aAAa,CAAC,IAAI,CAACe,YAAY,EAAE,IAAI,CAAC/B,KAAK,CAAClB,GAAG,CAAC,GAChD5S,CAAC,GACD,IAAI,CAAC6V,YAAY,CAAC7V,CAAC,CAACyV,GAAG,CAAC,CAAC,CAAC,GAC1B,IAAI,CAACI,YAAY;EAC3B;EAEA,SAASC,mBAAmBA,CAACC,WAAW,EAAEtX,MAAM,EAAEE,MAAM,EAAE;IACtD,IAAIP,CAAC;MACDoS,EAAE;MACFzL,GAAG;MACH0L,GAAG,GAAGsF,WAAW,CAACrF,iBAAiB,CAAC,CAAC;IACzC,IAAI,CAAC,IAAI,CAACsF,cAAc,EAAE;MACtB,IAAI,CAACA,cAAc,GAAG,EAAE;MACxB,IAAI,CAACC,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACC,iBAAiB,GAAG,EAAE;MAE3B,KAAK9X,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;QACpB2G,GAAG,GAAGvG,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAACiX,GAAG,CAACrX,CAAC,CAAC;QACjC,IAAI,CAAC8X,iBAAiB,CAAC9X,CAAC,CAAC,GAAG,IAAI,CAACiW,WAAW,CACxCtP,GAAG,EACH,EACJ,CAAC,CAAC2L,iBAAiB,CAAC,CAAC;QACrB,IAAI,CAACuF,mBAAmB,CAAC7X,CAAC,CAAC,GAAG,IAAI,CAACkW,aAAa,CAC5CvP,GAAG,EACH,EACJ,CAAC,CAAC2L,iBAAiB,CAAC,CAAC;QACrB,IAAI,CAACsF,cAAc,CAAC5X,CAAC,CAAC,GAAG,IAAI,CAACmW,QAAQ,CAACxP,GAAG,EAAE,EAAE,CAAC,CAAC2L,iBAAiB,CAAC,CAAC;MACvE;IACJ;IAEA,IAAI/R,MAAM,EAAE;MACR,IAAIF,MAAM,KAAK,MAAM,EAAE;QACnB+R,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAAC+Y,cAAc,EAAEvF,GAAG,CAAC;QAC3C,OAAOD,EAAE,KAAK,CAAC,CAAC,GAAGA,EAAE,GAAG,IAAI;MAChC,CAAC,MAAM,IAAI/R,MAAM,KAAK,KAAK,EAAE;QACzB+R,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAACgZ,mBAAmB,EAAExF,GAAG,CAAC;QAChD,OAAOD,EAAE,KAAK,CAAC,CAAC,GAAGA,EAAE,GAAG,IAAI;MAChC,CAAC,MAAM;QACHA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAACiZ,iBAAiB,EAAEzF,GAAG,CAAC;QAC9C,OAAOD,EAAE,KAAK,CAAC,CAAC,GAAGA,EAAE,GAAG,IAAI;MAChC;IACJ,CAAC,MAAM;MACH,IAAI/R,MAAM,KAAK,MAAM,EAAE;QACnB+R,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAAC+Y,cAAc,EAAEvF,GAAG,CAAC;QAC3C,IAAID,EAAE,KAAK,CAAC,CAAC,EAAE;UACX,OAAOA,EAAE;QACb;QACAA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAACgZ,mBAAmB,EAAExF,GAAG,CAAC;QAChD,IAAID,EAAE,KAAK,CAAC,CAAC,EAAE;UACX,OAAOA,EAAE;QACb;QACAA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAACiZ,iBAAiB,EAAEzF,GAAG,CAAC;QAC9C,OAAOD,EAAE,KAAK,CAAC,CAAC,GAAGA,EAAE,GAAG,IAAI;MAChC,CAAC,MAAM,IAAI/R,MAAM,KAAK,KAAK,EAAE;QACzB+R,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAACgZ,mBAAmB,EAAExF,GAAG,CAAC;QAChD,IAAID,EAAE,KAAK,CAAC,CAAC,EAAE;UACX,OAAOA,EAAE;QACb;QACAA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAAC+Y,cAAc,EAAEvF,GAAG,CAAC;QAC3C,IAAID,EAAE,KAAK,CAAC,CAAC,EAAE;UACX,OAAOA,EAAE;QACb;QACAA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAACiZ,iBAAiB,EAAEzF,GAAG,CAAC;QAC9C,OAAOD,EAAE,KAAK,CAAC,CAAC,GAAGA,EAAE,GAAG,IAAI;MAChC,CAAC,MAAM;QACHA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAACiZ,iBAAiB,EAAEzF,GAAG,CAAC;QAC9C,IAAID,EAAE,KAAK,CAAC,CAAC,EAAE;UACX,OAAOA,EAAE;QACb;QACAA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAAC+Y,cAAc,EAAEvF,GAAG,CAAC;QAC3C,IAAID,EAAE,KAAK,CAAC,CAAC,EAAE;UACX,OAAOA,EAAE;QACb;QACAA,EAAE,GAAGrB,OAAO,CAAClS,IAAI,CAAC,IAAI,CAACgZ,mBAAmB,EAAExF,GAAG,CAAC;QAChD,OAAOD,EAAE,KAAK,CAAC,CAAC,GAAGA,EAAE,GAAG,IAAI;MAChC;IACJ;EACJ;EAEA,SAAS2F,mBAAmBA,CAACJ,WAAW,EAAEtX,MAAM,EAAEE,MAAM,EAAE;IACtD,IAAIP,CAAC,EAAE2G,GAAG,EAAEuI,KAAK;IAEjB,IAAI,IAAI,CAAC8I,mBAAmB,EAAE;MAC1B,OAAON,mBAAmB,CAAC7Y,IAAI,CAAC,IAAI,EAAE8Y,WAAW,EAAEtX,MAAM,EAAEE,MAAM,CAAC;IACtE;IAEA,IAAI,CAAC,IAAI,CAACqX,cAAc,EAAE;MACtB,IAAI,CAACA,cAAc,GAAG,EAAE;MACxB,IAAI,CAACE,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACD,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACI,kBAAkB,GAAG,EAAE;IAChC;IAEA,KAAKjY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpB;;MAEA2G,GAAG,GAAGvG,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAACiX,GAAG,CAACrX,CAAC,CAAC;MACjC,IAAIO,MAAM,IAAI,CAAC,IAAI,CAAC0X,kBAAkB,CAACjY,CAAC,CAAC,EAAE;QACvC,IAAI,CAACiY,kBAAkB,CAACjY,CAAC,CAAC,GAAG,IAAI0F,MAAM,CACnC,GAAG,GAAG,IAAI,CAACyQ,QAAQ,CAACxP,GAAG,EAAE,EAAE,CAAC,CAAC4B,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,GAAG,EACvD,GACJ,CAAC;QACD,IAAI,CAACsP,mBAAmB,CAAC7X,CAAC,CAAC,GAAG,IAAI0F,MAAM,CACpC,GAAG,GAAG,IAAI,CAACwQ,aAAa,CAACvP,GAAG,EAAE,EAAE,CAAC,CAAC4B,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,GAAG,EAC5D,GACJ,CAAC;QACD,IAAI,CAACuP,iBAAiB,CAAC9X,CAAC,CAAC,GAAG,IAAI0F,MAAM,CAClC,GAAG,GAAG,IAAI,CAACuQ,WAAW,CAACtP,GAAG,EAAE,EAAE,CAAC,CAAC4B,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,GAAG,EAC1D,GACJ,CAAC;MACL;MACA,IAAI,CAAC,IAAI,CAACqP,cAAc,CAAC5X,CAAC,CAAC,EAAE;QACzBkP,KAAK,GACD,GAAG,GACH,IAAI,CAACiH,QAAQ,CAACxP,GAAG,EAAE,EAAE,CAAC,GACtB,IAAI,GACJ,IAAI,CAACuP,aAAa,CAACvP,GAAG,EAAE,EAAE,CAAC,GAC3B,IAAI,GACJ,IAAI,CAACsP,WAAW,CAACtP,GAAG,EAAE,EAAE,CAAC;QAC7B,IAAI,CAACiR,cAAc,CAAC5X,CAAC,CAAC,GAAG,IAAI0F,MAAM,CAACwJ,KAAK,CAAC3G,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC;MACpE;MACA;MACA,IACIhI,MAAM,IACNF,MAAM,KAAK,MAAM,IACjB,IAAI,CAAC4X,kBAAkB,CAACjY,CAAC,CAAC,CAACgJ,IAAI,CAAC2O,WAAW,CAAC,EAC9C;QACE,OAAO3X,CAAC;MACZ,CAAC,MAAM,IACHO,MAAM,IACNF,MAAM,KAAK,KAAK,IAChB,IAAI,CAACwX,mBAAmB,CAAC7X,CAAC,CAAC,CAACgJ,IAAI,CAAC2O,WAAW,CAAC,EAC/C;QACE,OAAO3X,CAAC;MACZ,CAAC,MAAM,IACHO,MAAM,IACNF,MAAM,KAAK,IAAI,IACf,IAAI,CAACyX,iBAAiB,CAAC9X,CAAC,CAAC,CAACgJ,IAAI,CAAC2O,WAAW,CAAC,EAC7C;QACE,OAAO3X,CAAC;MACZ,CAAC,MAAM,IAAI,CAACO,MAAM,IAAI,IAAI,CAACqX,cAAc,CAAC5X,CAAC,CAAC,CAACgJ,IAAI,CAAC2O,WAAW,CAAC,EAAE;QAC5D,OAAO3X,CAAC;MACZ;IACJ;EACJ;;EAEA;;EAEA,SAASkY,eAAeA,CAAC1Z,KAAK,EAAE;IAC5B,IAAI,CAAC,IAAI,CAAC0D,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO1D,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGwE,GAAG;IACrC;IACA,IAAIqU,GAAG,GAAG,IAAI,CAACxT,MAAM,GAAG,IAAI,CAACrB,EAAE,CAACoS,SAAS,CAAC,CAAC,GAAG,IAAI,CAACpS,EAAE,CAAC2V,MAAM,CAAC,CAAC;IAC9D,IAAI3Z,KAAK,IAAI,IAAI,EAAE;MACfA,KAAK,GAAGgY,YAAY,CAAChY,KAAK,EAAE,IAAI,CAAC4J,UAAU,CAAC,CAAC,CAAC;MAC9C,OAAO,IAAI,CAAC2N,GAAG,CAACvX,KAAK,GAAG6Y,GAAG,EAAE,GAAG,CAAC;IACrC,CAAC,MAAM;MACH,OAAOA,GAAG;IACd;EACJ;EAEA,SAASe,qBAAqBA,CAAC5Z,KAAK,EAAE;IAClC,IAAI,CAAC,IAAI,CAAC0D,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO1D,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGwE,GAAG;IACrC;IACA,IAAI+R,OAAO,GAAG,CAAC,IAAI,CAACsC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACjP,UAAU,CAAC,CAAC,CAACsN,KAAK,CAAClB,GAAG,IAAI,CAAC;IAChE,OAAOhW,KAAK,IAAI,IAAI,GAAGuW,OAAO,GAAG,IAAI,CAACgB,GAAG,CAACvX,KAAK,GAAGuW,OAAO,EAAE,GAAG,CAAC;EACnE;EAEA,SAASsD,kBAAkBA,CAAC7Z,KAAK,EAAE;IAC/B,IAAI,CAAC,IAAI,CAAC0D,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO1D,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGwE,GAAG;IACrC;;IAEA;IACA;IACA;;IAEA,IAAIxE,KAAK,IAAI,IAAI,EAAE;MACf,IAAIuW,OAAO,GAAG0B,eAAe,CAACjY,KAAK,EAAE,IAAI,CAAC4J,UAAU,CAAC,CAAC,CAAC;MACvD,OAAO,IAAI,CAACiP,GAAG,CAAC,IAAI,CAACA,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGtC,OAAO,GAAGA,OAAO,GAAG,CAAC,CAAC;IAC3D,CAAC,MAAM;MACH,OAAO,IAAI,CAACsC,GAAG,CAAC,CAAC,IAAI,CAAC;IAC1B;EACJ;EAEA,SAASf,aAAaA,CAAClH,QAAQ,EAAE;IAC7B,IAAI,IAAI,CAAC4I,mBAAmB,EAAE;MAC1B,IAAI,CAACjZ,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC,EAAE;QACrCuZ,oBAAoB,CAACzZ,IAAI,CAAC,IAAI,CAAC;MACnC;MACA,IAAIuQ,QAAQ,EAAE;QACV,OAAO,IAAI,CAACmJ,oBAAoB;MACpC,CAAC,MAAM;QACH,OAAO,IAAI,CAACC,cAAc;MAC9B;IACJ,CAAC,MAAM;MACH,IAAI,CAACzZ,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC,EAAE;QACrC,IAAI,CAACyZ,cAAc,GAAGxB,oBAAoB;MAC9C;MACA,OAAO,IAAI,CAACuB,oBAAoB,IAAInJ,QAAQ,GACtC,IAAI,CAACmJ,oBAAoB,GACzB,IAAI,CAACC,cAAc;IAC7B;EACJ;EAEA,SAASnC,kBAAkBA,CAACjH,QAAQ,EAAE;IAClC,IAAI,IAAI,CAAC4I,mBAAmB,EAAE;MAC1B,IAAI,CAACjZ,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC,EAAE;QACrCuZ,oBAAoB,CAACzZ,IAAI,CAAC,IAAI,CAAC;MACnC;MACA,IAAIuQ,QAAQ,EAAE;QACV,OAAO,IAAI,CAACqJ,yBAAyB;MACzC,CAAC,MAAM;QACH,OAAO,IAAI,CAACC,mBAAmB;MACnC;IACJ,CAAC,MAAM;MACH,IAAI,CAAC3Z,UAAU,CAAC,IAAI,EAAE,qBAAqB,CAAC,EAAE;QAC1C,IAAI,CAAC2Z,mBAAmB,GAAGzB,yBAAyB;MACxD;MACA,OAAO,IAAI,CAACwB,yBAAyB,IAAIrJ,QAAQ,GAC3C,IAAI,CAACqJ,yBAAyB,GAC9B,IAAI,CAACC,mBAAmB;IAClC;EACJ;EAEA,SAAStC,gBAAgBA,CAAChH,QAAQ,EAAE;IAChC,IAAI,IAAI,CAAC4I,mBAAmB,EAAE;MAC1B,IAAI,CAACjZ,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC,EAAE;QACrCuZ,oBAAoB,CAACzZ,IAAI,CAAC,IAAI,CAAC;MACnC;MACA,IAAIuQ,QAAQ,EAAE;QACV,OAAO,IAAI,CAACuJ,uBAAuB;MACvC,CAAC,MAAM;QACH,OAAO,IAAI,CAACC,iBAAiB;MACjC;IACJ,CAAC,MAAM;MACH,IAAI,CAAC7Z,UAAU,CAAC,IAAI,EAAE,mBAAmB,CAAC,EAAE;QACxC,IAAI,CAAC6Z,iBAAiB,GAAG1B,uBAAuB;MACpD;MACA,OAAO,IAAI,CAACyB,uBAAuB,IAAIvJ,QAAQ,GACzC,IAAI,CAACuJ,uBAAuB,GAC5B,IAAI,CAACC,iBAAiB;IAChC;EACJ;EAEA,SAASN,oBAAoBA,CAAA,EAAG;IAC5B,SAAShF,SAASA,CAACtU,CAAC,EAAEC,CAAC,EAAE;MACrB,OAAOA,CAAC,CAACK,MAAM,GAAGN,CAAC,CAACM,MAAM;IAC9B;IAEA,IAAIuZ,SAAS,GAAG,EAAE;MACdtF,WAAW,GAAG,EAAE;MAChBC,UAAU,GAAG,EAAE;MACfC,WAAW,GAAG,EAAE;MAChBzT,CAAC;MACD2G,GAAG;MACHmS,IAAI;MACJC,MAAM;MACNC,KAAK;IACT,KAAKhZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpB;MACA2G,GAAG,GAAGvG,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAACiX,GAAG,CAACrX,CAAC,CAAC;MACjC8Y,IAAI,GAAGvJ,WAAW,CAAC,IAAI,CAAC0G,WAAW,CAACtP,GAAG,EAAE,EAAE,CAAC,CAAC;MAC7CoS,MAAM,GAAGxJ,WAAW,CAAC,IAAI,CAAC2G,aAAa,CAACvP,GAAG,EAAE,EAAE,CAAC,CAAC;MACjDqS,KAAK,GAAGzJ,WAAW,CAAC,IAAI,CAAC4G,QAAQ,CAACxP,GAAG,EAAE,EAAE,CAAC,CAAC;MAC3CkS,SAAS,CAAC5Y,IAAI,CAAC6Y,IAAI,CAAC;MACpBvF,WAAW,CAACtT,IAAI,CAAC8Y,MAAM,CAAC;MACxBvF,UAAU,CAACvT,IAAI,CAAC+Y,KAAK,CAAC;MACtBvF,WAAW,CAACxT,IAAI,CAAC6Y,IAAI,CAAC;MACtBrF,WAAW,CAACxT,IAAI,CAAC8Y,MAAM,CAAC;MACxBtF,WAAW,CAACxT,IAAI,CAAC+Y,KAAK,CAAC;IAC3B;IACA;IACA;IACAH,SAAS,CAACnM,IAAI,CAAC4G,SAAS,CAAC;IACzBC,WAAW,CAAC7G,IAAI,CAAC4G,SAAS,CAAC;IAC3BE,UAAU,CAAC9G,IAAI,CAAC4G,SAAS,CAAC;IAC1BG,WAAW,CAAC/G,IAAI,CAAC4G,SAAS,CAAC;IAE3B,IAAI,CAACkF,cAAc,GAAG,IAAI9S,MAAM,CAAC,IAAI,GAAG+N,WAAW,CAAC1O,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC;IACzE,IAAI,CAAC2T,mBAAmB,GAAG,IAAI,CAACF,cAAc;IAC9C,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACJ,cAAc;IAE5C,IAAI,CAACD,oBAAoB,GAAG,IAAI7S,MAAM,CAClC,IAAI,GAAG8N,UAAU,CAACzO,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EACjC,GACJ,CAAC;IACD,IAAI,CAAC0T,yBAAyB,GAAG,IAAI/S,MAAM,CACvC,IAAI,GAAG6N,WAAW,CAACxO,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAClC,GACJ,CAAC;IACD,IAAI,CAAC4T,uBAAuB,GAAG,IAAIjT,MAAM,CACrC,IAAI,GAAGmT,SAAS,CAAC9T,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAChC,GACJ,CAAC;EACL;;EAEA;;EAEA,SAASkU,OAAOA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE;EAClC;EAEA,SAASC,OAAOA,CAAA,EAAG;IACf,OAAO,IAAI,CAACD,KAAK,CAAC,CAAC,IAAI,EAAE;EAC7B;EAEAnR,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;EACzCA,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAEkR,OAAO,CAAC;EAC1ClR,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAEoR,OAAO,CAAC;EAE1CpR,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY;IACpC,OAAO,EAAE,GAAGkR,OAAO,CAAC9a,KAAK,CAAC,IAAI,CAAC,GAAG4I,QAAQ,CAAC,IAAI,CAACqS,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACjE,CAAC,CAAC;EAEFrR,cAAc,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY;IACtC,OACI,EAAE,GACFkR,OAAO,CAAC9a,KAAK,CAAC,IAAI,CAAC,GACnB4I,QAAQ,CAAC,IAAI,CAACqS,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAC3BrS,QAAQ,CAAC,IAAI,CAACsS,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAEnC,CAAC,CAAC;EAEFtR,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY;IACpC,OAAO,EAAE,GAAG,IAAI,CAACmR,KAAK,CAAC,CAAC,GAAGnS,QAAQ,CAAC,IAAI,CAACqS,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1D,CAAC,CAAC;EAEFrR,cAAc,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY;IACtC,OACI,EAAE,GACF,IAAI,CAACmR,KAAK,CAAC,CAAC,GACZnS,QAAQ,CAAC,IAAI,CAACqS,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAC3BrS,QAAQ,CAAC,IAAI,CAACsS,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAEnC,CAAC,CAAC;EAEF,SAAS7X,QAAQA,CAACwG,KAAK,EAAEsR,SAAS,EAAE;IAChCvR,cAAc,CAACC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY;MACpC,OAAO,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC5G,QAAQ,CAC7B,IAAI,CAAC0X,KAAK,CAAC,CAAC,EACZ,IAAI,CAACE,OAAO,CAAC,CAAC,EACdE,SACJ,CAAC;IACL,CAAC,CAAC;EACN;EAEA9X,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC;EACnBA,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC;;EAEpB;;EAEAiK,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;;EAEzB;EACAY,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC;;EAE3B;;EAEA,SAASkN,aAAaA,CAACnK,QAAQ,EAAE9O,MAAM,EAAE;IACrC,OAAOA,MAAM,CAACkZ,cAAc;EAChC;EAEAvK,aAAa,CAAC,GAAG,EAAEsK,aAAa,CAAC;EACjCtK,aAAa,CAAC,GAAG,EAAEsK,aAAa,CAAC;EACjCtK,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EACtCiB,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EACtCiB,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EAEtCiB,aAAa,CAAC,KAAK,EAAEZ,SAAS,CAAC;EAC/BY,aAAa,CAAC,OAAO,EAAEX,SAAS,CAAC;EACjCW,aAAa,CAAC,KAAK,EAAEZ,SAAS,CAAC;EAC/BY,aAAa,CAAC,OAAO,EAAEX,SAAS,CAAC;EAEjCwB,aAAa,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAEQ,IAAI,CAAC;EAChCR,aAAa,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE;IACvD,IAAIwV,MAAM,GAAGzM,KAAK,CAACxO,KAAK,CAAC;IACzBiK,KAAK,CAAC6H,IAAI,CAAC,GAAGmJ,MAAM,KAAK,EAAE,GAAG,CAAC,GAAGA,MAAM;EAC5C,CAAC,CAAC;EACF3J,aAAa,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE;IACtDA,MAAM,CAACyV,KAAK,GAAGzV,MAAM,CAACF,OAAO,CAAC4V,IAAI,CAACnb,KAAK,CAAC;IACzCyF,MAAM,CAAC2V,SAAS,GAAGpb,KAAK;EAC5B,CAAC,CAAC;EACFsR,aAAa,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE;IACvDwE,KAAK,CAAC6H,IAAI,CAAC,GAAGtD,KAAK,CAACxO,KAAK,CAAC;IAC1BmD,eAAe,CAACsC,MAAM,CAAC,CAACrB,OAAO,GAAG,IAAI;EAC1C,CAAC,CAAC;EACFkN,aAAa,CAAC,KAAK,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE;IACjD,IAAI4V,GAAG,GAAGrb,KAAK,CAACc,MAAM,GAAG,CAAC;IAC1BmJ,KAAK,CAAC6H,IAAI,CAAC,GAAGtD,KAAK,CAACxO,KAAK,CAACkJ,MAAM,CAAC,CAAC,EAAEmS,GAAG,CAAC,CAAC;IACzCpR,KAAK,CAAC8H,MAAM,CAAC,GAAGvD,KAAK,CAACxO,KAAK,CAACkJ,MAAM,CAACmS,GAAG,CAAC,CAAC;IACxClY,eAAe,CAACsC,MAAM,CAAC,CAACrB,OAAO,GAAG,IAAI;EAC1C,CAAC,CAAC;EACFkN,aAAa,CAAC,OAAO,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE;IACnD,IAAI6V,IAAI,GAAGtb,KAAK,CAACc,MAAM,GAAG,CAAC;MACvBya,IAAI,GAAGvb,KAAK,CAACc,MAAM,GAAG,CAAC;IAC3BmJ,KAAK,CAAC6H,IAAI,CAAC,GAAGtD,KAAK,CAACxO,KAAK,CAACkJ,MAAM,CAAC,CAAC,EAAEoS,IAAI,CAAC,CAAC;IAC1CrR,KAAK,CAAC8H,MAAM,CAAC,GAAGvD,KAAK,CAACxO,KAAK,CAACkJ,MAAM,CAACoS,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5CrR,KAAK,CAAC+H,MAAM,CAAC,GAAGxD,KAAK,CAACxO,KAAK,CAACkJ,MAAM,CAACqS,IAAI,CAAC,CAAC;IACzCpY,eAAe,CAACsC,MAAM,CAAC,CAACrB,OAAO,GAAG,IAAI;EAC1C,CAAC,CAAC;EACFkN,aAAa,CAAC,KAAK,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE;IACjD,IAAI4V,GAAG,GAAGrb,KAAK,CAACc,MAAM,GAAG,CAAC;IAC1BmJ,KAAK,CAAC6H,IAAI,CAAC,GAAGtD,KAAK,CAACxO,KAAK,CAACkJ,MAAM,CAAC,CAAC,EAAEmS,GAAG,CAAC,CAAC;IACzCpR,KAAK,CAAC8H,MAAM,CAAC,GAAGvD,KAAK,CAACxO,KAAK,CAACkJ,MAAM,CAACmS,GAAG,CAAC,CAAC;EAC5C,CAAC,CAAC;EACF/J,aAAa,CAAC,OAAO,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE;IACnD,IAAI6V,IAAI,GAAGtb,KAAK,CAACc,MAAM,GAAG,CAAC;MACvBya,IAAI,GAAGvb,KAAK,CAACc,MAAM,GAAG,CAAC;IAC3BmJ,KAAK,CAAC6H,IAAI,CAAC,GAAGtD,KAAK,CAACxO,KAAK,CAACkJ,MAAM,CAAC,CAAC,EAAEoS,IAAI,CAAC,CAAC;IAC1CrR,KAAK,CAAC8H,MAAM,CAAC,GAAGvD,KAAK,CAACxO,KAAK,CAACkJ,MAAM,CAACoS,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5CrR,KAAK,CAAC+H,MAAM,CAAC,GAAGxD,KAAK,CAACxO,KAAK,CAACkJ,MAAM,CAACqS,IAAI,CAAC,CAAC;EAC7C,CAAC,CAAC;;EAEF;;EAEA,SAASC,UAAUA,CAACxb,KAAK,EAAE;IACvB;IACA;IACA,OAAO,CAACA,KAAK,GAAG,EAAE,EAAEqN,WAAW,CAAC,CAAC,CAACoO,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;EACvD;EAEA,IAAIC,0BAA0B,GAAG,eAAe;IAC5C;IACA;IACA;IACA;IACAC,UAAU,GAAG9M,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC;EAE1C,SAAS+M,cAAcA,CAAClB,KAAK,EAAEE,OAAO,EAAEiB,OAAO,EAAE;IAC7C,IAAInB,KAAK,GAAG,EAAE,EAAE;MACZ,OAAOmB,OAAO,GAAG,IAAI,GAAG,IAAI;IAChC,CAAC,MAAM;MACH,OAAOA,OAAO,GAAG,IAAI,GAAG,IAAI;IAChC;EACJ;EAEA,IAAIC,UAAU,GAAG;IACb5T,QAAQ,EAAEP,eAAe;IACzB2C,cAAc,EAAEG,qBAAqB;IACrCN,WAAW,EAAEiB,kBAAkB;IAC/B1B,OAAO,EAAE4B,cAAc;IACvByQ,sBAAsB,EAAExQ,6BAA6B;IACrDkB,YAAY,EAAEhB,mBAAmB;IAEjCkH,MAAM,EAAEI,mBAAmB;IAC3BL,WAAW,EAAEO,wBAAwB;IAErCqD,IAAI,EAAEa,iBAAiB;IAEvBQ,QAAQ,EAAEU,qBAAqB;IAC/BZ,WAAW,EAAEc,wBAAwB;IACrCb,aAAa,EAAEY,0BAA0B;IAEzC0D,aAAa,EAAEN;EACnB,CAAC;;EAED;EACA,IAAIO,OAAO,GAAG,CAAC,CAAC;IACZC,cAAc,GAAG,CAAC,CAAC;IACnBC,YAAY;EAEhB,SAASC,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;IAC9B,IAAI9a,CAAC;MACD+a,IAAI,GAAG3T,IAAI,CAAC0L,GAAG,CAAC+H,IAAI,CAACvb,MAAM,EAAEwb,IAAI,CAACxb,MAAM,CAAC;IAC7C,KAAKU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+a,IAAI,EAAE/a,CAAC,IAAI,CAAC,EAAE;MAC1B,IAAI6a,IAAI,CAAC7a,CAAC,CAAC,KAAK8a,IAAI,CAAC9a,CAAC,CAAC,EAAE;QACrB,OAAOA,CAAC;MACZ;IACJ;IACA,OAAO+a,IAAI;EACf;EAEA,SAASC,eAAeA,CAACnW,GAAG,EAAE;IAC1B,OAAOA,GAAG,GAAGA,GAAG,CAACgH,WAAW,CAAC,CAAC,CAACtD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG1D,GAAG;EAC1D;;EAEA;EACA;EACA;EACA,SAASoW,YAAYA,CAACC,KAAK,EAAE;IACzB,IAAIlb,CAAC,GAAG,CAAC;MACLmb,CAAC;MACDC,IAAI;MACJ9a,MAAM;MACNkR,KAAK;IAET,OAAOxR,CAAC,GAAGkb,KAAK,CAAC5b,MAAM,EAAE;MACrBkS,KAAK,GAAGwJ,eAAe,CAACE,KAAK,CAAClb,CAAC,CAAC,CAAC,CAACwR,KAAK,CAAC,GAAG,CAAC;MAC5C2J,CAAC,GAAG3J,KAAK,CAAClS,MAAM;MAChB8b,IAAI,GAAGJ,eAAe,CAACE,KAAK,CAAClb,CAAC,GAAG,CAAC,CAAC,CAAC;MACpCob,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC5J,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI;MACpC,OAAO2J,CAAC,GAAG,CAAC,EAAE;QACV7a,MAAM,GAAG+a,UAAU,CAAC7J,KAAK,CAAC1M,KAAK,CAAC,CAAC,EAAEqW,CAAC,CAAC,CAACpW,IAAI,CAAC,GAAG,CAAC,CAAC;QAChD,IAAIzE,MAAM,EAAE;UACR,OAAOA,MAAM;QACjB;QACA,IACI8a,IAAI,IACJA,IAAI,CAAC9b,MAAM,IAAI6b,CAAC,IAChBP,YAAY,CAACpJ,KAAK,EAAE4J,IAAI,CAAC,IAAID,CAAC,GAAG,CAAC,EACpC;UACE;UACA;QACJ;QACAA,CAAC,EAAE;MACP;MACAnb,CAAC,EAAE;IACP;IACA,OAAO2a,YAAY;EACvB;EAEA,SAASU,UAAUA,CAACjW,IAAI,EAAE;IACtB,IAAIkW,SAAS,GAAG,IAAI;MAChBC,cAAc;IAClB;IACA,IACId,OAAO,CAACrV,IAAI,CAAC,KAAKvC,SAAS,IAC3B,OAAOhF,MAAM,KAAK,WAAW,IAC7BA,MAAM,IACNA,MAAM,CAACD,OAAO,EAChB;MACE,IAAI;QACA0d,SAAS,GAAGX,YAAY,CAACa,KAAK;QAC9BD,cAAc,GAAGE,OAAO;QACxBF,cAAc,CAAC,WAAW,GAAGnW,IAAI,CAAC;QAClCsW,kBAAkB,CAACJ,SAAS,CAAC;MACjC,CAAC,CAAC,OAAOK,CAAC,EAAE;QACR;QACA;QACAlB,OAAO,CAACrV,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;MAC1B;IACJ;IACA,OAAOqV,OAAO,CAACrV,IAAI,CAAC;EACxB;;EAEA;EACA;EACA;EACA,SAASsW,kBAAkBA,CAAC7W,GAAG,EAAE+W,MAAM,EAAE;IACrC,IAAIC,IAAI;IACR,IAAIhX,GAAG,EAAE;MACL,IAAIrF,WAAW,CAACoc,MAAM,CAAC,EAAE;QACrBC,IAAI,GAAGC,SAAS,CAACjX,GAAG,CAAC;MACzB,CAAC,MAAM;QACHgX,IAAI,GAAGE,YAAY,CAAClX,GAAG,EAAE+W,MAAM,CAAC;MACpC;MAEA,IAAIC,IAAI,EAAE;QACN;QACAlB,YAAY,GAAGkB,IAAI;MACvB,CAAC,MAAM;QACH,IAAI,OAAOtX,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACH,IAAI,EAAE;UAChD;UACAG,OAAO,CAACH,IAAI,CACR,SAAS,GAAGS,GAAG,GAAG,wCACtB,CAAC;QACL;MACJ;IACJ;IAEA,OAAO8V,YAAY,CAACa,KAAK;EAC7B;EAEA,SAASO,YAAYA,CAAC3W,IAAI,EAAEnB,MAAM,EAAE;IAChC,IAAIA,MAAM,KAAK,IAAI,EAAE;MACjB,IAAI3D,MAAM;QACNyF,YAAY,GAAGuU,UAAU;MAC7BrW,MAAM,CAAC+X,IAAI,GAAG5W,IAAI;MAClB,IAAIqV,OAAO,CAACrV,IAAI,CAAC,IAAI,IAAI,EAAE;QACvBD,eAAe,CACX,sBAAsB,EACtB,wDAAwD,GACpD,sDAAsD,GACtD,wDAAwD,GACxD,yEACR,CAAC;QACDY,YAAY,GAAG0U,OAAO,CAACrV,IAAI,CAAC,CAACI,OAAO;MACxC,CAAC,MAAM,IAAIvB,MAAM,CAACgY,YAAY,IAAI,IAAI,EAAE;QACpC,IAAIxB,OAAO,CAACxW,MAAM,CAACgY,YAAY,CAAC,IAAI,IAAI,EAAE;UACtClW,YAAY,GAAG0U,OAAO,CAACxW,MAAM,CAACgY,YAAY,CAAC,CAACzW,OAAO;QACvD,CAAC,MAAM;UACHlF,MAAM,GAAG+a,UAAU,CAACpX,MAAM,CAACgY,YAAY,CAAC;UACxC,IAAI3b,MAAM,IAAI,IAAI,EAAE;YAChByF,YAAY,GAAGzF,MAAM,CAACkF,OAAO;UACjC,CAAC,MAAM;YACH,IAAI,CAACkV,cAAc,CAACzW,MAAM,CAACgY,YAAY,CAAC,EAAE;cACtCvB,cAAc,CAACzW,MAAM,CAACgY,YAAY,CAAC,GAAG,EAAE;YAC5C;YACAvB,cAAc,CAACzW,MAAM,CAACgY,YAAY,CAAC,CAAChc,IAAI,CAAC;cACrCmF,IAAI,EAAEA,IAAI;cACVnB,MAAM,EAAEA;YACZ,CAAC,CAAC;YACF,OAAO,IAAI;UACf;QACJ;MACJ;MACAwW,OAAO,CAACrV,IAAI,CAAC,GAAG,IAAIa,MAAM,CAACH,YAAY,CAACC,YAAY,EAAE9B,MAAM,CAAC,CAAC;MAE9D,IAAIyW,cAAc,CAACtV,IAAI,CAAC,EAAE;QACtBsV,cAAc,CAACtV,IAAI,CAAC,CAAC8W,OAAO,CAAC,UAAUpL,CAAC,EAAE;UACtCiL,YAAY,CAACjL,CAAC,CAAC1L,IAAI,EAAE0L,CAAC,CAAC7M,MAAM,CAAC;QAClC,CAAC,CAAC;MACN;;MAEA;MACA;MACA;MACAyX,kBAAkB,CAACtW,IAAI,CAAC;MAExB,OAAOqV,OAAO,CAACrV,IAAI,CAAC;IACxB,CAAC,MAAM;MACH;MACA,OAAOqV,OAAO,CAACrV,IAAI,CAAC;MACpB,OAAO,IAAI;IACf;EACJ;EAEA,SAAS+W,YAAYA,CAAC/W,IAAI,EAAEnB,MAAM,EAAE;IAChC,IAAIA,MAAM,IAAI,IAAI,EAAE;MAChB,IAAI3D,MAAM;QACN8b,SAAS;QACTrW,YAAY,GAAGuU,UAAU;MAE7B,IAAIG,OAAO,CAACrV,IAAI,CAAC,IAAI,IAAI,IAAIqV,OAAO,CAACrV,IAAI,CAAC,CAAC6W,YAAY,IAAI,IAAI,EAAE;QAC7D;QACAxB,OAAO,CAACrV,IAAI,CAAC,CAACG,GAAG,CAACO,YAAY,CAAC2U,OAAO,CAACrV,IAAI,CAAC,CAACI,OAAO,EAAEvB,MAAM,CAAC,CAAC;MAClE,CAAC,MAAM;QACH;QACAmY,SAAS,GAAGf,UAAU,CAACjW,IAAI,CAAC;QAC5B,IAAIgX,SAAS,IAAI,IAAI,EAAE;UACnBrW,YAAY,GAAGqW,SAAS,CAAC5W,OAAO;QACpC;QACAvB,MAAM,GAAG6B,YAAY,CAACC,YAAY,EAAE9B,MAAM,CAAC;QAC3C,IAAImY,SAAS,IAAI,IAAI,EAAE;UACnB;UACA;UACA;UACAnY,MAAM,CAAC+X,IAAI,GAAG5W,IAAI;QACtB;QACA9E,MAAM,GAAG,IAAI2F,MAAM,CAAChC,MAAM,CAAC;QAC3B3D,MAAM,CAAC2b,YAAY,GAAGxB,OAAO,CAACrV,IAAI,CAAC;QACnCqV,OAAO,CAACrV,IAAI,CAAC,GAAG9E,MAAM;MAC1B;;MAEA;MACAob,kBAAkB,CAACtW,IAAI,CAAC;IAC5B,CAAC,MAAM;MACH;MACA,IAAIqV,OAAO,CAACrV,IAAI,CAAC,IAAI,IAAI,EAAE;QACvB,IAAIqV,OAAO,CAACrV,IAAI,CAAC,CAAC6W,YAAY,IAAI,IAAI,EAAE;UACpCxB,OAAO,CAACrV,IAAI,CAAC,GAAGqV,OAAO,CAACrV,IAAI,CAAC,CAAC6W,YAAY;UAC1C,IAAI7W,IAAI,KAAKsW,kBAAkB,CAAC,CAAC,EAAE;YAC/BA,kBAAkB,CAACtW,IAAI,CAAC;UAC5B;QACJ,CAAC,MAAM,IAAIqV,OAAO,CAACrV,IAAI,CAAC,IAAI,IAAI,EAAE;UAC9B,OAAOqV,OAAO,CAACrV,IAAI,CAAC;QACxB;MACJ;IACJ;IACA,OAAOqV,OAAO,CAACrV,IAAI,CAAC;EACxB;;EAEA;EACA,SAAS0W,SAASA,CAACjX,GAAG,EAAE;IACpB,IAAIvE,MAAM;IAEV,IAAIuE,GAAG,IAAIA,GAAG,CAACd,OAAO,IAAIc,GAAG,CAACd,OAAO,CAACyX,KAAK,EAAE;MACzC3W,GAAG,GAAGA,GAAG,CAACd,OAAO,CAACyX,KAAK;IAC3B;IAEA,IAAI,CAAC3W,GAAG,EAAE;MACN,OAAO8V,YAAY;IACvB;IAEA,IAAI,CAACpc,OAAO,CAACsG,GAAG,CAAC,EAAE;MACf;MACAvE,MAAM,GAAG+a,UAAU,CAACxW,GAAG,CAAC;MACxB,IAAIvE,MAAM,EAAE;QACR,OAAOA,MAAM;MACjB;MACAuE,GAAG,GAAG,CAACA,GAAG,CAAC;IACf;IAEA,OAAOoW,YAAY,CAACpW,GAAG,CAAC;EAC5B;EAEA,SAASwX,WAAWA,CAAA,EAAG;IACnB,OAAOnW,IAAI,CAACuU,OAAO,CAAC;EACxB;EAEA,SAAS6B,aAAaA,CAAC1a,CAAC,EAAE;IACtB,IAAId,QAAQ;MACR9B,CAAC,GAAG4C,CAAC,CAACsO,EAAE;IAEZ,IAAIlR,CAAC,IAAI2C,eAAe,CAACC,CAAC,CAAC,CAACd,QAAQ,KAAK,CAAC,CAAC,EAAE;MACzCA,QAAQ,GACJ9B,CAAC,CAACoR,KAAK,CAAC,GAAG,CAAC,IAAIpR,CAAC,CAACoR,KAAK,CAAC,GAAG,EAAE,GACvBA,KAAK,GACLpR,CAAC,CAACqR,IAAI,CAAC,GAAG,CAAC,IAAIrR,CAAC,CAACqR,IAAI,CAAC,GAAG1C,WAAW,CAAC3O,CAAC,CAACmR,IAAI,CAAC,EAAEnR,CAAC,CAACoR,KAAK,CAAC,CAAC,GACvDC,IAAI,GACJrR,CAAC,CAACsR,IAAI,CAAC,GAAG,CAAC,IACXtR,CAAC,CAACsR,IAAI,CAAC,GAAG,EAAE,IACXtR,CAAC,CAACsR,IAAI,CAAC,KAAK,EAAE,KACVtR,CAAC,CAACuR,MAAM,CAAC,KAAK,CAAC,IACZvR,CAAC,CAACwR,MAAM,CAAC,KAAK,CAAC,IACfxR,CAAC,CAACyR,WAAW,CAAC,KAAK,CAAC,CAAE,GAC9BH,IAAI,GACJtR,CAAC,CAACuR,MAAM,CAAC,GAAG,CAAC,IAAIvR,CAAC,CAACuR,MAAM,CAAC,GAAG,EAAE,GAC/BA,MAAM,GACNvR,CAAC,CAACwR,MAAM,CAAC,GAAG,CAAC,IAAIxR,CAAC,CAACwR,MAAM,CAAC,GAAG,EAAE,GAC/BA,MAAM,GACNxR,CAAC,CAACyR,WAAW,CAAC,GAAG,CAAC,IAAIzR,CAAC,CAACyR,WAAW,CAAC,GAAG,GAAG,GAC1CA,WAAW,GACX,CAAC,CAAC;MAEZ,IACI9O,eAAe,CAACC,CAAC,CAAC,CAAC2a,kBAAkB,KACpCzb,QAAQ,GAAGqP,IAAI,IAAIrP,QAAQ,GAAGuP,IAAI,CAAC,EACtC;QACEvP,QAAQ,GAAGuP,IAAI;MACnB;MACA,IAAI1O,eAAe,CAACC,CAAC,CAAC,CAAC4a,cAAc,IAAI1b,QAAQ,KAAK,CAAC,CAAC,EAAE;QACtDA,QAAQ,GAAG4P,IAAI;MACnB;MACA,IAAI/O,eAAe,CAACC,CAAC,CAAC,CAAC6a,gBAAgB,IAAI3b,QAAQ,KAAK,CAAC,CAAC,EAAE;QACxDA,QAAQ,GAAG6P,OAAO;MACtB;MAEAhP,eAAe,CAACC,CAAC,CAAC,CAACd,QAAQ,GAAGA,QAAQ;IAC1C;IAEA,OAAOc,CAAC;EACZ;;EAEA;EACA;EACA,IAAI8a,gBAAgB,GAAG,gJAAgJ;IACnKC,aAAa,GAAG,4IAA4I;IAC5JC,OAAO,GAAG,uBAAuB;IACjCC,QAAQ,GAAG,CACP,CAAC,cAAc,EAAE,qBAAqB,CAAC,EACvC,CAAC,YAAY,EAAE,iBAAiB,CAAC,EACjC,CAAC,cAAc,EAAE,gBAAgB,CAAC,EAClC,CAAC,YAAY,EAAE,aAAa,EAAE,KAAK,CAAC,EACpC,CAAC,UAAU,EAAE,aAAa,CAAC,EAC3B,CAAC,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,EAChC,CAAC,YAAY,EAAE,YAAY,CAAC,EAC5B,CAAC,UAAU,EAAE,OAAO,CAAC,EACrB,CAAC,YAAY,EAAE,aAAa,CAAC,EAC7B,CAAC,WAAW,EAAE,aAAa,EAAE,KAAK,CAAC,EACnC,CAAC,SAAS,EAAE,OAAO,CAAC,EACpB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,EAC1B,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAC3B;IACD;IACAC,QAAQ,GAAG,CACP,CAAC,eAAe,EAAE,qBAAqB,CAAC,EACxC,CAAC,eAAe,EAAE,oBAAoB,CAAC,EACvC,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAC9B,CAAC,OAAO,EAAE,WAAW,CAAC,EACtB,CAAC,aAAa,EAAE,mBAAmB,CAAC,EACpC,CAAC,aAAa,EAAE,kBAAkB,CAAC,EACnC,CAAC,QAAQ,EAAE,cAAc,CAAC,EAC1B,CAAC,MAAM,EAAE,UAAU,CAAC,EACpB,CAAC,IAAI,EAAE,MAAM,CAAC,CACjB;IACDC,eAAe,GAAG,oBAAoB;IACtC;IACAtb,OAAO,GAAG,yLAAyL;IACnMub,UAAU,GAAG;MACTC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;MACZC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;MACZC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;MACZC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;MACZC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;MACZC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;MACZC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;MACZC,GAAG,EAAE,CAAC,CAAC,GAAG;IACd,CAAC;;EAEL;EACA,SAASC,aAAaA,CAAC1Z,MAAM,EAAE;IAC3B,IAAIjE,CAAC;MACD4d,CAAC;MACDzS,MAAM,GAAGlH,MAAM,CAACR,EAAE;MAClB6E,KAAK,GAAGoU,gBAAgB,CAACmB,IAAI,CAAC1S,MAAM,CAAC,IAAIwR,aAAa,CAACkB,IAAI,CAAC1S,MAAM,CAAC;MACnE2S,SAAS;MACTC,UAAU;MACVC,UAAU;MACVC,QAAQ;IAEZ,IAAI3V,KAAK,EAAE;MACP3G,eAAe,CAACsC,MAAM,CAAC,CAAC5C,GAAG,GAAG,IAAI;MAElC,KAAKrB,CAAC,GAAG,CAAC,EAAE4d,CAAC,GAAGf,QAAQ,CAACvd,MAAM,EAAEU,CAAC,GAAG4d,CAAC,EAAE5d,CAAC,EAAE,EAAE;QACzC,IAAI6c,QAAQ,CAAC7c,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC6d,IAAI,CAACvV,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;UAC/ByV,UAAU,GAAGlB,QAAQ,CAAC7c,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B8d,SAAS,GAAGjB,QAAQ,CAAC7c,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK;UACpC;QACJ;MACJ;MACA,IAAI+d,UAAU,IAAI,IAAI,EAAE;QACpB9Z,MAAM,CAAC9B,QAAQ,GAAG,KAAK;QACvB;MACJ;MACA,IAAImG,KAAK,CAAC,CAAC,CAAC,EAAE;QACV,KAAKtI,CAAC,GAAG,CAAC,EAAE4d,CAAC,GAAGd,QAAQ,CAACxd,MAAM,EAAEU,CAAC,GAAG4d,CAAC,EAAE5d,CAAC,EAAE,EAAE;UACzC,IAAI8c,QAAQ,CAAC9c,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC6d,IAAI,CAACvV,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YAC/B;YACA0V,UAAU,GAAG,CAAC1V,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,IAAIwU,QAAQ,CAAC9c,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C;UACJ;QACJ;QACA,IAAIge,UAAU,IAAI,IAAI,EAAE;UACpB/Z,MAAM,CAAC9B,QAAQ,GAAG,KAAK;UACvB;QACJ;MACJ;MACA,IAAI,CAAC2b,SAAS,IAAIE,UAAU,IAAI,IAAI,EAAE;QAClC/Z,MAAM,CAAC9B,QAAQ,GAAG,KAAK;QACvB;MACJ;MACA,IAAImG,KAAK,CAAC,CAAC,CAAC,EAAE;QACV,IAAIsU,OAAO,CAACiB,IAAI,CAACvV,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB2V,QAAQ,GAAG,GAAG;QAClB,CAAC,MAAM;UACHha,MAAM,CAAC9B,QAAQ,GAAG,KAAK;UACvB;QACJ;MACJ;MACA8B,MAAM,CAACP,EAAE,GAAGqa,UAAU,IAAIC,UAAU,IAAI,EAAE,CAAC,IAAIC,QAAQ,IAAI,EAAE,CAAC;MAC9DC,yBAAyB,CAACja,MAAM,CAAC;IACrC,CAAC,MAAM;MACHA,MAAM,CAAC9B,QAAQ,GAAG,KAAK;IAC3B;EACJ;EAEA,SAASgc,yBAAyBA,CAC9BC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,SAAS,EACX;IACE,IAAIC,MAAM,GAAG,CACTC,cAAc,CAACP,OAAO,CAAC,EACvB3M,wBAAwB,CAACV,OAAO,CAACsN,QAAQ,CAAC,EAC1C1K,QAAQ,CAAC2K,MAAM,EAAE,EAAE,CAAC,EACpB3K,QAAQ,CAAC4K,OAAO,EAAE,EAAE,CAAC,EACrB5K,QAAQ,CAAC6K,SAAS,EAAE,EAAE,CAAC,CAC1B;IAED,IAAIC,SAAS,EAAE;MACXC,MAAM,CAACze,IAAI,CAAC0T,QAAQ,CAAC8K,SAAS,EAAE,EAAE,CAAC,CAAC;IACxC;IAEA,OAAOC,MAAM;EACjB;EAEA,SAASC,cAAcA,CAACP,OAAO,EAAE;IAC7B,IAAIxR,IAAI,GAAG+G,QAAQ,CAACyK,OAAO,EAAE,EAAE,CAAC;IAChC,IAAIxR,IAAI,IAAI,EAAE,EAAE;MACZ,OAAO,IAAI,GAAGA,IAAI;IACtB,CAAC,MAAM,IAAIA,IAAI,IAAI,GAAG,EAAE;MACpB,OAAO,IAAI,GAAGA,IAAI;IACtB;IACA,OAAOA,IAAI;EACf;EAEA,SAASgS,iBAAiBA,CAACxU,CAAC,EAAE;IAC1B;IACA,OAAOA,CAAC,CACH7B,OAAO,CAAC,mBAAmB,EAAE,GAAG,CAAC,CACjCA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CACxBA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CACrBA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;EAC9B;EAEA,SAASsW,YAAYA,CAACC,UAAU,EAAEC,WAAW,EAAE9a,MAAM,EAAE;IACnD,IAAI6a,UAAU,EAAE;MACZ;MACA,IAAIE,eAAe,GAAGlI,0BAA0B,CAAC/F,OAAO,CAAC+N,UAAU,CAAC;QAChEG,aAAa,GAAG,IAAItf,IAAI,CACpBof,WAAW,CAAC,CAAC,CAAC,EACdA,WAAW,CAAC,CAAC,CAAC,EACdA,WAAW,CAAC,CAAC,CACjB,CAAC,CAAC5G,MAAM,CAAC,CAAC;MACd,IAAI6G,eAAe,KAAKC,aAAa,EAAE;QACnCtd,eAAe,CAACsC,MAAM,CAAC,CAACvC,eAAe,GAAG,IAAI;QAC9CuC,MAAM,CAAC9B,QAAQ,GAAG,KAAK;QACvB,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EAEA,SAAS+c,eAAeA,CAACC,SAAS,EAAEC,cAAc,EAAEC,SAAS,EAAE;IAC3D,IAAIF,SAAS,EAAE;MACX,OAAOnC,UAAU,CAACmC,SAAS,CAAC;IAChC,CAAC,MAAM,IAAIC,cAAc,EAAE;MACvB;MACA,OAAO,CAAC;IACZ,CAAC,MAAM;MACH,IAAIE,EAAE,GAAG3L,QAAQ,CAAC0L,SAAS,EAAE,EAAE,CAAC;QAC5Bzd,CAAC,GAAG0d,EAAE,GAAG,GAAG;QACZ/U,CAAC,GAAG,CAAC+U,EAAE,GAAG1d,CAAC,IAAI,GAAG;MACtB,OAAO2I,CAAC,GAAG,EAAE,GAAG3I,CAAC;IACrB;EACJ;;EAEA;EACA,SAAS2d,iBAAiBA,CAACtb,MAAM,EAAE;IAC/B,IAAIqE,KAAK,GAAG7G,OAAO,CAACoc,IAAI,CAACe,iBAAiB,CAAC3a,MAAM,CAACR,EAAE,CAAC,CAAC;MAClD+b,WAAW;IACf,IAAIlX,KAAK,EAAE;MACPkX,WAAW,GAAGrB,yBAAyB,CACnC7V,KAAK,CAAC,CAAC,CAAC,EACRA,KAAK,CAAC,CAAC,CAAC,EACRA,KAAK,CAAC,CAAC,CAAC,EACRA,KAAK,CAAC,CAAC,CAAC,EACRA,KAAK,CAAC,CAAC,CAAC,EACRA,KAAK,CAAC,CAAC,CACX,CAAC;MACD,IAAI,CAACuW,YAAY,CAACvW,KAAK,CAAC,CAAC,CAAC,EAAEkX,WAAW,EAAEvb,MAAM,CAAC,EAAE;QAC9C;MACJ;MAEAA,MAAM,CAACiM,EAAE,GAAGsP,WAAW;MACvBvb,MAAM,CAACL,IAAI,GAAGsb,eAAe,CAAC5W,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,EAAE,CAAC,CAAC;MAE5DrE,MAAM,CAACzB,EAAE,GAAG2R,aAAa,CAAChW,KAAK,CAAC,IAAI,EAAE8F,MAAM,CAACiM,EAAE,CAAC;MAChDjM,MAAM,CAACzB,EAAE,CAACid,aAAa,CAACxb,MAAM,CAACzB,EAAE,CAACkd,aAAa,CAAC,CAAC,GAAGzb,MAAM,CAACL,IAAI,CAAC;MAEhEjC,eAAe,CAACsC,MAAM,CAAC,CAACxC,OAAO,GAAG,IAAI;IAC1C,CAAC,MAAM;MACHwC,MAAM,CAAC9B,QAAQ,GAAG,KAAK;IAC3B;EACJ;;EAEA;EACA,SAASwd,gBAAgBA,CAAC1b,MAAM,EAAE;IAC9B,IAAIuL,OAAO,GAAGuN,eAAe,CAACc,IAAI,CAAC5Z,MAAM,CAACR,EAAE,CAAC;IAC7C,IAAI+L,OAAO,KAAK,IAAI,EAAE;MAClBvL,MAAM,CAACzB,EAAE,GAAG,IAAI7C,IAAI,CAAC,CAAC6P,OAAO,CAAC,CAAC,CAAC,CAAC;MACjC;IACJ;IAEAmO,aAAa,CAAC1Z,MAAM,CAAC;IACrB,IAAIA,MAAM,CAAC9B,QAAQ,KAAK,KAAK,EAAE;MAC3B,OAAO8B,MAAM,CAAC9B,QAAQ;IAC1B,CAAC,MAAM;MACH;IACJ;IAEAod,iBAAiB,CAACtb,MAAM,CAAC;IACzB,IAAIA,MAAM,CAAC9B,QAAQ,KAAK,KAAK,EAAE;MAC3B,OAAO8B,MAAM,CAAC9B,QAAQ;IAC1B,CAAC,MAAM;MACH;IACJ;IAEA,IAAI8B,MAAM,CAACtB,OAAO,EAAE;MAChBsB,MAAM,CAAC9B,QAAQ,GAAG,KAAK;IAC3B,CAAC,MAAM;MACH;MACAjE,KAAK,CAAC0hB,uBAAuB,CAAC3b,MAAM,CAAC;IACzC;EACJ;EAEA/F,KAAK,CAAC0hB,uBAAuB,GAAGpb,SAAS,CACrC,4GAA4G,GACxG,2FAA2F,GAC3F,4FAA4F,EAChG,UAAUP,MAAM,EAAE;IACdA,MAAM,CAACzB,EAAE,GAAG,IAAI7C,IAAI,CAACsE,MAAM,CAACR,EAAE,IAAIQ,MAAM,CAAC4b,OAAO,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC;EACpE,CACJ,CAAC;;EAED;EACA,SAASC,QAAQA,CAAC9gB,CAAC,EAAEC,CAAC,EAAE8gB,CAAC,EAAE;IACvB,IAAI/gB,CAAC,IAAI,IAAI,EAAE;MACX,OAAOA,CAAC;IACZ;IACA,IAAIC,CAAC,IAAI,IAAI,EAAE;MACX,OAAOA,CAAC;IACZ;IACA,OAAO8gB,CAAC;EACZ;EAEA,SAASC,gBAAgBA,CAAC/b,MAAM,EAAE;IAC9B;IACA,IAAIgc,QAAQ,GAAG,IAAItgB,IAAI,CAACzB,KAAK,CAAC0I,GAAG,CAAC,CAAC,CAAC;IACpC,IAAI3C,MAAM,CAAC4b,OAAO,EAAE;MAChB,OAAO,CACHI,QAAQ,CAAC5L,cAAc,CAAC,CAAC,EACzB4L,QAAQ,CAACC,WAAW,CAAC,CAAC,EACtBD,QAAQ,CAACE,UAAU,CAAC,CAAC,CACxB;IACL;IACA,OAAO,CAACF,QAAQ,CAAChM,WAAW,CAAC,CAAC,EAAEgM,QAAQ,CAACG,QAAQ,CAAC,CAAC,EAAEH,QAAQ,CAACI,OAAO,CAAC,CAAC,CAAC;EAC5E;;EAEA;EACA;EACA;EACA;EACA,SAASC,eAAeA,CAACrc,MAAM,EAAE;IAC7B,IAAIjE,CAAC;MACD0N,IAAI;MACJlP,KAAK,GAAG,EAAE;MACV+hB,WAAW;MACXC,eAAe;MACfC,SAAS;IAEb,IAAIxc,MAAM,CAACzB,EAAE,EAAE;MACX;IACJ;IAEA+d,WAAW,GAAGP,gBAAgB,CAAC/b,MAAM,CAAC;;IAEtC;IACA,IAAIA,MAAM,CAAC+L,EAAE,IAAI/L,MAAM,CAACiM,EAAE,CAACG,IAAI,CAAC,IAAI,IAAI,IAAIpM,MAAM,CAACiM,EAAE,CAACE,KAAK,CAAC,IAAI,IAAI,EAAE;MAClEsQ,qBAAqB,CAACzc,MAAM,CAAC;IACjC;;IAEA;IACA,IAAIA,MAAM,CAAC0c,UAAU,IAAI,IAAI,EAAE;MAC3BF,SAAS,GAAGX,QAAQ,CAAC7b,MAAM,CAACiM,EAAE,CAACC,IAAI,CAAC,EAAEoQ,WAAW,CAACpQ,IAAI,CAAC,CAAC;MAExD,IACIlM,MAAM,CAAC0c,UAAU,GAAG/M,UAAU,CAAC6M,SAAS,CAAC,IACzCxc,MAAM,CAAC0c,UAAU,KAAK,CAAC,EACzB;QACEhf,eAAe,CAACsC,MAAM,CAAC,CAACsY,kBAAkB,GAAG,IAAI;MACrD;MAEA7O,IAAI,GAAGyG,aAAa,CAACsM,SAAS,EAAE,CAAC,EAAExc,MAAM,CAAC0c,UAAU,CAAC;MACrD1c,MAAM,CAACiM,EAAE,CAACE,KAAK,CAAC,GAAG1C,IAAI,CAACwS,WAAW,CAAC,CAAC;MACrCjc,MAAM,CAACiM,EAAE,CAACG,IAAI,CAAC,GAAG3C,IAAI,CAACyS,UAAU,CAAC,CAAC;IACvC;;IAEA;IACA;IACA;IACA;IACA;IACA,KAAKngB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAIiE,MAAM,CAACiM,EAAE,CAAClQ,CAAC,CAAC,IAAI,IAAI,EAAE,EAAEA,CAAC,EAAE;MAC5CiE,MAAM,CAACiM,EAAE,CAAClQ,CAAC,CAAC,GAAGxB,KAAK,CAACwB,CAAC,CAAC,GAAGugB,WAAW,CAACvgB,CAAC,CAAC;IAC5C;;IAEA;IACA,OAAOA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACfiE,MAAM,CAACiM,EAAE,CAAClQ,CAAC,CAAC,GAAGxB,KAAK,CAACwB,CAAC,CAAC,GACnBiE,MAAM,CAACiM,EAAE,CAAClQ,CAAC,CAAC,IAAI,IAAI,GAAIA,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAIiE,MAAM,CAACiM,EAAE,CAAClQ,CAAC,CAAC;IAC/D;;IAEA;IACA,IACIiE,MAAM,CAACiM,EAAE,CAACI,IAAI,CAAC,KAAK,EAAE,IACtBrM,MAAM,CAACiM,EAAE,CAACK,MAAM,CAAC,KAAK,CAAC,IACvBtM,MAAM,CAACiM,EAAE,CAACM,MAAM,CAAC,KAAK,CAAC,IACvBvM,MAAM,CAACiM,EAAE,CAACO,WAAW,CAAC,KAAK,CAAC,EAC9B;MACExM,MAAM,CAAC2c,QAAQ,GAAG,IAAI;MACtB3c,MAAM,CAACiM,EAAE,CAACI,IAAI,CAAC,GAAG,CAAC;IACvB;IAEArM,MAAM,CAACzB,EAAE,GAAG,CAACyB,MAAM,CAAC4b,OAAO,GAAG1L,aAAa,GAAGJ,UAAU,EAAE5V,KAAK,CAC3D,IAAI,EACJK,KACJ,CAAC;IACDgiB,eAAe,GAAGvc,MAAM,CAAC4b,OAAO,GAC1B5b,MAAM,CAACzB,EAAE,CAACoS,SAAS,CAAC,CAAC,GACrB3Q,MAAM,CAACzB,EAAE,CAAC2V,MAAM,CAAC,CAAC;;IAExB;IACA;IACA,IAAIlU,MAAM,CAACL,IAAI,IAAI,IAAI,EAAE;MACrBK,MAAM,CAACzB,EAAE,CAACid,aAAa,CAACxb,MAAM,CAACzB,EAAE,CAACkd,aAAa,CAAC,CAAC,GAAGzb,MAAM,CAACL,IAAI,CAAC;IACpE;IAEA,IAAIK,MAAM,CAAC2c,QAAQ,EAAE;MACjB3c,MAAM,CAACiM,EAAE,CAACI,IAAI,CAAC,GAAG,EAAE;IACxB;;IAEA;IACA,IACIrM,MAAM,CAAC+L,EAAE,IACT,OAAO/L,MAAM,CAAC+L,EAAE,CAACvF,CAAC,KAAK,WAAW,IAClCxG,MAAM,CAAC+L,EAAE,CAACvF,CAAC,KAAK+V,eAAe,EACjC;MACE7e,eAAe,CAACsC,MAAM,CAAC,CAACvC,eAAe,GAAG,IAAI;IAClD;EACJ;EAEA,SAASgf,qBAAqBA,CAACzc,MAAM,EAAE;IACnC,IAAI0G,CAAC,EAAEkW,QAAQ,EAAE/L,IAAI,EAAEC,OAAO,EAAEP,GAAG,EAAEC,GAAG,EAAEqM,IAAI,EAAEC,eAAe,EAAEC,OAAO;IAExErW,CAAC,GAAG1G,MAAM,CAAC+L,EAAE;IACb,IAAIrF,CAAC,CAACsW,EAAE,IAAI,IAAI,IAAItW,CAAC,CAACuW,CAAC,IAAI,IAAI,IAAIvW,CAAC,CAACwW,CAAC,IAAI,IAAI,EAAE;MAC5C3M,GAAG,GAAG,CAAC;MACPC,GAAG,GAAG,CAAC;;MAEP;MACA;MACA;MACA;MACAoM,QAAQ,GAAGf,QAAQ,CACfnV,CAAC,CAACsW,EAAE,EACJhd,MAAM,CAACiM,EAAE,CAACC,IAAI,CAAC,EACfkF,UAAU,CAAC+L,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACxU,IACpC,CAAC;MACDkI,IAAI,GAAGgL,QAAQ,CAACnV,CAAC,CAACuW,CAAC,EAAE,CAAC,CAAC;MACvBnM,OAAO,GAAG+K,QAAQ,CAACnV,CAAC,CAACwW,CAAC,EAAE,CAAC,CAAC;MAC1B,IAAIpM,OAAO,GAAG,CAAC,IAAIA,OAAO,GAAG,CAAC,EAAE;QAC5BgM,eAAe,GAAG,IAAI;MAC1B;IACJ,CAAC,MAAM;MACHvM,GAAG,GAAGvQ,MAAM,CAACF,OAAO,CAAC2R,KAAK,CAAClB,GAAG;MAC9BC,GAAG,GAAGxQ,MAAM,CAACF,OAAO,CAAC2R,KAAK,CAACjB,GAAG;MAE9BuM,OAAO,GAAG3L,UAAU,CAAC+L,WAAW,CAAC,CAAC,EAAE5M,GAAG,EAAEC,GAAG,CAAC;MAE7CoM,QAAQ,GAAGf,QAAQ,CAACnV,CAAC,CAAC0W,EAAE,EAAEpd,MAAM,CAACiM,EAAE,CAACC,IAAI,CAAC,EAAE6Q,OAAO,CAACpU,IAAI,CAAC;;MAExD;MACAkI,IAAI,GAAGgL,QAAQ,CAACnV,CAAC,CAACA,CAAC,EAAEqW,OAAO,CAAClM,IAAI,CAAC;MAElC,IAAInK,CAAC,CAACF,CAAC,IAAI,IAAI,EAAE;QACb;QACAsK,OAAO,GAAGpK,CAAC,CAACF,CAAC;QACb,IAAIsK,OAAO,GAAG,CAAC,IAAIA,OAAO,GAAG,CAAC,EAAE;UAC5BgM,eAAe,GAAG,IAAI;QAC1B;MACJ,CAAC,MAAM,IAAIpW,CAAC,CAACgR,CAAC,IAAI,IAAI,EAAE;QACpB;QACA5G,OAAO,GAAGpK,CAAC,CAACgR,CAAC,GAAGnH,GAAG;QACnB,IAAI7J,CAAC,CAACgR,CAAC,GAAG,CAAC,IAAIhR,CAAC,CAACgR,CAAC,GAAG,CAAC,EAAE;UACpBoF,eAAe,GAAG,IAAI;QAC1B;MACJ,CAAC,MAAM;QACH;QACAhM,OAAO,GAAGP,GAAG;MACjB;IACJ;IACA,IAAIM,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAGS,WAAW,CAACsL,QAAQ,EAAErM,GAAG,EAAEC,GAAG,CAAC,EAAE;MACpD9S,eAAe,CAACsC,MAAM,CAAC,CAACuY,cAAc,GAAG,IAAI;IACjD,CAAC,MAAM,IAAIuE,eAAe,IAAI,IAAI,EAAE;MAChCpf,eAAe,CAACsC,MAAM,CAAC,CAACwY,gBAAgB,GAAG,IAAI;IACnD,CAAC,MAAM;MACHqE,IAAI,GAAGjM,kBAAkB,CAACgM,QAAQ,EAAE/L,IAAI,EAAEC,OAAO,EAAEP,GAAG,EAAEC,GAAG,CAAC;MAC5DxQ,MAAM,CAACiM,EAAE,CAACC,IAAI,CAAC,GAAG2Q,IAAI,CAAClU,IAAI;MAC3B3I,MAAM,CAAC0c,UAAU,GAAGG,IAAI,CAAC5L,SAAS;IACtC;EACJ;;EAEA;EACAhX,KAAK,CAACojB,QAAQ,GAAG,YAAY,CAAC,CAAC;;EAE/B;EACApjB,KAAK,CAACqjB,QAAQ,GAAG,YAAY,CAAC,CAAC;;EAE/B;EACA,SAASrD,yBAAyBA,CAACja,MAAM,EAAE;IACvC;IACA,IAAIA,MAAM,CAACP,EAAE,KAAKxF,KAAK,CAACojB,QAAQ,EAAE;MAC9B3D,aAAa,CAAC1Z,MAAM,CAAC;MACrB;IACJ;IACA,IAAIA,MAAM,CAACP,EAAE,KAAKxF,KAAK,CAACqjB,QAAQ,EAAE;MAC9BhC,iBAAiB,CAACtb,MAAM,CAAC;MACzB;IACJ;IACAA,MAAM,CAACiM,EAAE,GAAG,EAAE;IACdvO,eAAe,CAACsC,MAAM,CAAC,CAACtD,KAAK,GAAG,IAAI;;IAEpC;IACA,IAAIwK,MAAM,GAAG,EAAE,GAAGlH,MAAM,CAACR,EAAE;MACvBzD,CAAC;MACD+e,WAAW;MACXlP,MAAM;MACN7H,KAAK;MACLwZ,OAAO;MACPC,YAAY,GAAGtW,MAAM,CAAC7L,MAAM;MAC5BoiB,sBAAsB,GAAG,CAAC;MAC1BngB,GAAG;IAEPsO,MAAM,GACFjH,YAAY,CAAC3E,MAAM,CAACP,EAAE,EAAEO,MAAM,CAACF,OAAO,CAAC,CAACuE,KAAK,CAACX,gBAAgB,CAAC,IAAI,EAAE;IAEzE,KAAK3H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6P,MAAM,CAACvQ,MAAM,EAAEU,CAAC,EAAE,EAAE;MAChCgI,KAAK,GAAG6H,MAAM,CAAC7P,CAAC,CAAC;MACjB+e,WAAW,GAAG,CAAC5T,MAAM,CAAC7C,KAAK,CAAC+G,qBAAqB,CAACrH,KAAK,EAAE/D,MAAM,CAAC,CAAC,IAC7D,EAAE,EAAE,CAAC,CAAC;MACV,IAAI8a,WAAW,EAAE;QACbyC,OAAO,GAAGrW,MAAM,CAACzD,MAAM,CAAC,CAAC,EAAEyD,MAAM,CAAC4F,OAAO,CAACgO,WAAW,CAAC,CAAC;QACvD,IAAIyC,OAAO,CAACliB,MAAM,GAAG,CAAC,EAAE;UACpBqC,eAAe,CAACsC,MAAM,CAAC,CAACpD,WAAW,CAACZ,IAAI,CAACuhB,OAAO,CAAC;QACrD;QACArW,MAAM,GAAGA,MAAM,CAACrG,KAAK,CACjBqG,MAAM,CAAC4F,OAAO,CAACgO,WAAW,CAAC,GAAGA,WAAW,CAACzf,MAC9C,CAAC;QACDoiB,sBAAsB,IAAI3C,WAAW,CAACzf,MAAM;MAChD;MACA;MACA,IAAIwI,oBAAoB,CAACE,KAAK,CAAC,EAAE;QAC7B,IAAI+W,WAAW,EAAE;UACbpd,eAAe,CAACsC,MAAM,CAAC,CAACtD,KAAK,GAAG,KAAK;QACzC,CAAC,MAAM;UACHgB,eAAe,CAACsC,MAAM,CAAC,CAACrD,YAAY,CAACX,IAAI,CAAC+H,KAAK,CAAC;QACpD;QACAiI,uBAAuB,CAACjI,KAAK,EAAE+W,WAAW,EAAE9a,MAAM,CAAC;MACvD,CAAC,MAAM,IAAIA,MAAM,CAACtB,OAAO,IAAI,CAACoc,WAAW,EAAE;QACvCpd,eAAe,CAACsC,MAAM,CAAC,CAACrD,YAAY,CAACX,IAAI,CAAC+H,KAAK,CAAC;MACpD;IACJ;;IAEA;IACArG,eAAe,CAACsC,MAAM,CAAC,CAAClD,aAAa,GACjC0gB,YAAY,GAAGC,sBAAsB;IACzC,IAAIvW,MAAM,CAAC7L,MAAM,GAAG,CAAC,EAAE;MACnBqC,eAAe,CAACsC,MAAM,CAAC,CAACpD,WAAW,CAACZ,IAAI,CAACkL,MAAM,CAAC;IACpD;;IAEA;IACA,IACIlH,MAAM,CAACiM,EAAE,CAACI,IAAI,CAAC,IAAI,EAAE,IACrB3O,eAAe,CAACsC,MAAM,CAAC,CAACrB,OAAO,KAAK,IAAI,IACxCqB,MAAM,CAACiM,EAAE,CAACI,IAAI,CAAC,GAAG,CAAC,EACrB;MACE3O,eAAe,CAACsC,MAAM,CAAC,CAACrB,OAAO,GAAGC,SAAS;IAC/C;IAEAlB,eAAe,CAACsC,MAAM,CAAC,CAAC3C,eAAe,GAAG2C,MAAM,CAACiM,EAAE,CAACpL,KAAK,CAAC,CAAC,CAAC;IAC5DnD,eAAe,CAACsC,MAAM,CAAC,CAACzC,QAAQ,GAAGyC,MAAM,CAAC2V,SAAS;IACnD;IACA3V,MAAM,CAACiM,EAAE,CAACI,IAAI,CAAC,GAAGqR,eAAe,CAC7B1d,MAAM,CAACF,OAAO,EACdE,MAAM,CAACiM,EAAE,CAACI,IAAI,CAAC,EACfrM,MAAM,CAAC2V,SACX,CAAC;;IAED;IACArY,GAAG,GAAGI,eAAe,CAACsC,MAAM,CAAC,CAAC1C,GAAG;IACjC,IAAIA,GAAG,KAAK,IAAI,EAAE;MACd0C,MAAM,CAACiM,EAAE,CAACC,IAAI,CAAC,GAAGlM,MAAM,CAACF,OAAO,CAAC6d,eAAe,CAACrgB,GAAG,EAAE0C,MAAM,CAACiM,EAAE,CAACC,IAAI,CAAC,CAAC;IAC1E;IAEAmQ,eAAe,CAACrc,MAAM,CAAC;IACvBqY,aAAa,CAACrY,MAAM,CAAC;EACzB;EAEA,SAAS0d,eAAeA,CAACrhB,MAAM,EAAEuhB,IAAI,EAAErgB,QAAQ,EAAE;IAC7C,IAAIsgB,IAAI;IAER,IAAItgB,QAAQ,IAAI,IAAI,EAAE;MAClB;MACA,OAAOqgB,IAAI;IACf;IACA,IAAIvhB,MAAM,CAACyhB,YAAY,IAAI,IAAI,EAAE;MAC7B,OAAOzhB,MAAM,CAACyhB,YAAY,CAACF,IAAI,EAAErgB,QAAQ,CAAC;IAC9C,CAAC,MAAM,IAAIlB,MAAM,CAACqZ,IAAI,IAAI,IAAI,EAAE;MAC5B;MACAmI,IAAI,GAAGxhB,MAAM,CAACqZ,IAAI,CAACnY,QAAQ,CAAC;MAC5B,IAAIsgB,IAAI,IAAID,IAAI,GAAG,EAAE,EAAE;QACnBA,IAAI,IAAI,EAAE;MACd;MACA,IAAI,CAACC,IAAI,IAAID,IAAI,KAAK,EAAE,EAAE;QACtBA,IAAI,GAAG,CAAC;MACZ;MACA,OAAOA,IAAI;IACf,CAAC,MAAM;MACH;MACA,OAAOA,IAAI;IACf;EACJ;;EAEA;EACA,SAASG,wBAAwBA,CAAC/d,MAAM,EAAE;IACtC,IAAIge,UAAU;MACVC,UAAU;MACVC,WAAW;MACXniB,CAAC;MACDoiB,YAAY;MACZC,gBAAgB;MAChBC,iBAAiB,GAAG,KAAK;IAE7B,IAAIre,MAAM,CAACP,EAAE,CAACpE,MAAM,KAAK,CAAC,EAAE;MACxBqC,eAAe,CAACsC,MAAM,CAAC,CAAC9C,aAAa,GAAG,IAAI;MAC5C8C,MAAM,CAACzB,EAAE,GAAG,IAAI7C,IAAI,CAACqD,GAAG,CAAC;MACzB;IACJ;IAEA,KAAKhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,MAAM,CAACP,EAAE,CAACpE,MAAM,EAAEU,CAAC,EAAE,EAAE;MACnCoiB,YAAY,GAAG,CAAC;MAChBC,gBAAgB,GAAG,KAAK;MACxBJ,UAAU,GAAG9e,UAAU,CAAC,CAAC,CAAC,EAAEc,MAAM,CAAC;MACnC,IAAIA,MAAM,CAAC4b,OAAO,IAAI,IAAI,EAAE;QACxBoC,UAAU,CAACpC,OAAO,GAAG5b,MAAM,CAAC4b,OAAO;MACvC;MACAoC,UAAU,CAACve,EAAE,GAAGO,MAAM,CAACP,EAAE,CAAC1D,CAAC,CAAC;MAC5Bke,yBAAyB,CAAC+D,UAAU,CAAC;MAErC,IAAI/f,OAAO,CAAC+f,UAAU,CAAC,EAAE;QACrBI,gBAAgB,GAAG,IAAI;MAC3B;;MAEA;MACAD,YAAY,IAAIzgB,eAAe,CAACsgB,UAAU,CAAC,CAAClhB,aAAa;;MAEzD;MACAqhB,YAAY,IAAIzgB,eAAe,CAACsgB,UAAU,CAAC,CAACrhB,YAAY,CAACtB,MAAM,GAAG,EAAE;MAEpEqC,eAAe,CAACsgB,UAAU,CAAC,CAACM,KAAK,GAAGH,YAAY;MAEhD,IAAI,CAACE,iBAAiB,EAAE;QACpB,IACIH,WAAW,IAAI,IAAI,IACnBC,YAAY,GAAGD,WAAW,IAC1BE,gBAAgB,EAClB;UACEF,WAAW,GAAGC,YAAY;UAC1BF,UAAU,GAAGD,UAAU;UACvB,IAAII,gBAAgB,EAAE;YAClBC,iBAAiB,GAAG,IAAI;UAC5B;QACJ;MACJ,CAAC,MAAM;QACH,IAAIF,YAAY,GAAGD,WAAW,EAAE;UAC5BA,WAAW,GAAGC,YAAY;UAC1BF,UAAU,GAAGD,UAAU;QAC3B;MACJ;IACJ;IAEA/hB,MAAM,CAAC+D,MAAM,EAAEie,UAAU,IAAID,UAAU,CAAC;EAC5C;EAEA,SAASO,gBAAgBA,CAACve,MAAM,EAAE;IAC9B,IAAIA,MAAM,CAACzB,EAAE,EAAE;MACX;IACJ;IAEA,IAAIxC,CAAC,GAAGgM,oBAAoB,CAAC/H,MAAM,CAACR,EAAE,CAAC;MACnCgf,SAAS,GAAGziB,CAAC,CAACqX,GAAG,KAAKxU,SAAS,GAAG7C,CAAC,CAAC0N,IAAI,GAAG1N,CAAC,CAACqX,GAAG;IACpDpT,MAAM,CAACiM,EAAE,GAAGtQ,GAAG,CACX,CAACI,CAAC,CAAC4M,IAAI,EAAE5M,CAAC,CAACyN,KAAK,EAAEgV,SAAS,EAAEziB,CAAC,CAAC6hB,IAAI,EAAE7hB,CAAC,CAAC0iB,MAAM,EAAE1iB,CAAC,CAAC2iB,MAAM,EAAE3iB,CAAC,CAAC4iB,WAAW,CAAC,EACvE,UAAUxjB,GAAG,EAAE;MACX,OAAOA,GAAG,IAAIuU,QAAQ,CAACvU,GAAG,EAAE,EAAE,CAAC;IACnC,CACJ,CAAC;IAEDkhB,eAAe,CAACrc,MAAM,CAAC;EAC3B;EAEA,SAAS4e,gBAAgBA,CAAC5e,MAAM,EAAE;IAC9B,IAAIlE,GAAG,GAAG,IAAIiE,MAAM,CAACsY,aAAa,CAACwG,aAAa,CAAC7e,MAAM,CAAC,CAAC,CAAC;IAC1D,IAAIlE,GAAG,CAAC6gB,QAAQ,EAAE;MACd;MACA7gB,GAAG,CAACgW,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;MACfhW,GAAG,CAAC6gB,QAAQ,GAAG/d,SAAS;IAC5B;IAEA,OAAO9C,GAAG;EACd;EAEA,SAAS+iB,aAAaA,CAAC7e,MAAM,EAAE;IAC3B,IAAIzF,KAAK,GAAGyF,MAAM,CAACR,EAAE;MACjBpD,MAAM,GAAG4D,MAAM,CAACP,EAAE;IAEtBO,MAAM,CAACF,OAAO,GAAGE,MAAM,CAACF,OAAO,IAAI+X,SAAS,CAAC7X,MAAM,CAACN,EAAE,CAAC;IAEvD,IAAInF,KAAK,KAAK,IAAI,IAAK6B,MAAM,KAAKwC,SAAS,IAAIrE,KAAK,KAAK,EAAG,EAAE;MAC1D,OAAOuE,aAAa,CAAC;QAAE/B,SAAS,EAAE;MAAK,CAAC,CAAC;IAC7C;IAEA,IAAI,OAAOxC,KAAK,KAAK,QAAQ,EAAE;MAC3ByF,MAAM,CAACR,EAAE,GAAGjF,KAAK,GAAGyF,MAAM,CAACF,OAAO,CAACgf,QAAQ,CAACvkB,KAAK,CAAC;IACtD;IAEA,IAAI2F,QAAQ,CAAC3F,KAAK,CAAC,EAAE;MACjB,OAAO,IAAIwF,MAAM,CAACsY,aAAa,CAAC9d,KAAK,CAAC,CAAC;IAC3C,CAAC,MAAM,IAAIkB,MAAM,CAAClB,KAAK,CAAC,EAAE;MACtByF,MAAM,CAACzB,EAAE,GAAGhE,KAAK;IACrB,CAAC,MAAM,IAAID,OAAO,CAAC8B,MAAM,CAAC,EAAE;MACxB2hB,wBAAwB,CAAC/d,MAAM,CAAC;IACpC,CAAC,MAAM,IAAI5D,MAAM,EAAE;MACf6d,yBAAyB,CAACja,MAAM,CAAC;IACrC,CAAC,MAAM;MACH+e,eAAe,CAAC/e,MAAM,CAAC;IAC3B;IAEA,IAAI,CAAC/B,OAAO,CAAC+B,MAAM,CAAC,EAAE;MAClBA,MAAM,CAACzB,EAAE,GAAG,IAAI;IACpB;IAEA,OAAOyB,MAAM;EACjB;EAEA,SAAS+e,eAAeA,CAAC/e,MAAM,EAAE;IAC7B,IAAIzF,KAAK,GAAGyF,MAAM,CAACR,EAAE;IACrB,IAAIjE,WAAW,CAAChB,KAAK,CAAC,EAAE;MACpByF,MAAM,CAACzB,EAAE,GAAG,IAAI7C,IAAI,CAACzB,KAAK,CAAC0I,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC,MAAM,IAAIlH,MAAM,CAAClB,KAAK,CAAC,EAAE;MACtByF,MAAM,CAACzB,EAAE,GAAG,IAAI7C,IAAI,CAACnB,KAAK,CAAC2B,OAAO,CAAC,CAAC,CAAC;IACzC,CAAC,MAAM,IAAI,OAAO3B,KAAK,KAAK,QAAQ,EAAE;MAClCmhB,gBAAgB,CAAC1b,MAAM,CAAC;IAC5B,CAAC,MAAM,IAAI1F,OAAO,CAACC,KAAK,CAAC,EAAE;MACvByF,MAAM,CAACiM,EAAE,GAAGtQ,GAAG,CAACpB,KAAK,CAACsG,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU1F,GAAG,EAAE;QAC3C,OAAOuU,QAAQ,CAACvU,GAAG,EAAE,EAAE,CAAC;MAC5B,CAAC,CAAC;MACFkhB,eAAe,CAACrc,MAAM,CAAC;IAC3B,CAAC,MAAM,IAAInF,QAAQ,CAACN,KAAK,CAAC,EAAE;MACxBgkB,gBAAgB,CAACve,MAAM,CAAC;IAC5B,CAAC,MAAM,IAAIxE,QAAQ,CAACjB,KAAK,CAAC,EAAE;MACxB;MACAyF,MAAM,CAACzB,EAAE,GAAG,IAAI7C,IAAI,CAACnB,KAAK,CAAC;IAC/B,CAAC,MAAM;MACHN,KAAK,CAAC0hB,uBAAuB,CAAC3b,MAAM,CAAC;IACzC;EACJ;EAEA,SAASzD,gBAAgBA,CAAChC,KAAK,EAAE6B,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE0iB,KAAK,EAAE;IAC5D,IAAIlD,CAAC,GAAG,CAAC,CAAC;IAEV,IAAI1f,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,EAAE;MACrCE,MAAM,GAAGF,MAAM;MACfA,MAAM,GAAGwC,SAAS;IACtB;IAEA,IAAIvC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,EAAE;MACrCC,MAAM,GAAGD,MAAM;MACfA,MAAM,GAAGuC,SAAS;IACtB;IAEA,IACK/D,QAAQ,CAACN,KAAK,CAAC,IAAIW,aAAa,CAACX,KAAK,CAAC,IACvCD,OAAO,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACc,MAAM,KAAK,CAAE,EACxC;MACEd,KAAK,GAAGqE,SAAS;IACrB;IACA;IACA;IACAkd,CAAC,CAACvc,gBAAgB,GAAG,IAAI;IACzBuc,CAAC,CAACF,OAAO,GAAGE,CAAC,CAAClc,MAAM,GAAGof,KAAK;IAC5BlD,CAAC,CAACpc,EAAE,GAAGrD,MAAM;IACbyf,CAAC,CAACtc,EAAE,GAAGjF,KAAK;IACZuhB,CAAC,CAACrc,EAAE,GAAGrD,MAAM;IACb0f,CAAC,CAACpd,OAAO,GAAGpC,MAAM;IAElB,OAAOsiB,gBAAgB,CAAC9C,CAAC,CAAC;EAC9B;EAEA,SAASqB,WAAWA,CAAC5iB,KAAK,EAAE6B,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;IAChD,OAAOC,gBAAgB,CAAChC,KAAK,EAAE6B,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE,KAAK,CAAC;EACjE;EAEA,IAAI2iB,YAAY,GAAG1e,SAAS,CACpB,oGAAoG,EACpG,YAAY;MACR,IAAI2e,KAAK,GAAG/B,WAAW,CAACjjB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC9C,IAAI,IAAI,CAAC8D,OAAO,CAAC,CAAC,IAAIihB,KAAK,CAACjhB,OAAO,CAAC,CAAC,EAAE;QACnC,OAAOihB,KAAK,GAAG,IAAI,GAAG,IAAI,GAAGA,KAAK;MACtC,CAAC,MAAM;QACH,OAAOpgB,aAAa,CAAC,CAAC;MAC1B;IACJ,CACJ,CAAC;IACDqgB,YAAY,GAAG5e,SAAS,CACpB,oGAAoG,EACpG,YAAY;MACR,IAAI2e,KAAK,GAAG/B,WAAW,CAACjjB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC9C,IAAI,IAAI,CAAC8D,OAAO,CAAC,CAAC,IAAIihB,KAAK,CAACjhB,OAAO,CAAC,CAAC,EAAE;QACnC,OAAOihB,KAAK,GAAG,IAAI,GAAG,IAAI,GAAGA,KAAK;MACtC,CAAC,MAAM;QACH,OAAOpgB,aAAa,CAAC,CAAC;MAC1B;IACJ,CACJ,CAAC;;EAEL;EACA;EACA;EACA;EACA;EACA,SAASsgB,MAAMA,CAACvjB,EAAE,EAAEwjB,OAAO,EAAE;IACzB,IAAIvjB,GAAG,EAAEC,CAAC;IACV,IAAIsjB,OAAO,CAAChkB,MAAM,KAAK,CAAC,IAAIf,OAAO,CAAC+kB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7CA,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;IACxB;IACA,IAAI,CAACA,OAAO,CAAChkB,MAAM,EAAE;MACjB,OAAO8hB,WAAW,CAAC,CAAC;IACxB;IACArhB,GAAG,GAAGujB,OAAO,CAAC,CAAC,CAAC;IAChB,KAAKtjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsjB,OAAO,CAAChkB,MAAM,EAAE,EAAEU,CAAC,EAAE;MACjC,IAAI,CAACsjB,OAAO,CAACtjB,CAAC,CAAC,CAACkC,OAAO,CAAC,CAAC,IAAIohB,OAAO,CAACtjB,CAAC,CAAC,CAACF,EAAE,CAAC,CAACC,GAAG,CAAC,EAAE;QAC9CA,GAAG,GAAGujB,OAAO,CAACtjB,CAAC,CAAC;MACpB;IACJ;IACA,OAAOD,GAAG;EACd;;EAEA;EACA,SAAS+S,GAAGA,CAAA,EAAG;IACX,IAAInO,IAAI,GAAG,EAAE,CAACG,KAAK,CAACjG,IAAI,CAACT,SAAS,EAAE,CAAC,CAAC;IAEtC,OAAOilB,MAAM,CAAC,UAAU,EAAE1e,IAAI,CAAC;EACnC;EAEA,SAAS8C,GAAGA,CAAA,EAAG;IACX,IAAI9C,IAAI,GAAG,EAAE,CAACG,KAAK,CAACjG,IAAI,CAACT,SAAS,EAAE,CAAC,CAAC;IAEtC,OAAOilB,MAAM,CAAC,SAAS,EAAE1e,IAAI,CAAC;EAClC;EAEA,IAAIiC,GAAG,GAAG,SAAAA,CAAA,EAAY;IAClB,OAAOjH,IAAI,CAACiH,GAAG,GAAGjH,IAAI,CAACiH,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIjH,IAAI,CAAC,CAAC;EAC9C,CAAC;EAED,IAAI4jB,QAAQ,GAAG,CACX,MAAM,EACN,SAAS,EACT,OAAO,EACP,MAAM,EACN,KAAK,EACL,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,aAAa,CAChB;EAED,SAASC,eAAeA,CAAC5hB,CAAC,EAAE;IACxB,IAAIiD,GAAG;MACH4e,cAAc,GAAG,KAAK;MACtBzjB,CAAC;IACL,KAAK6E,GAAG,IAAIjD,CAAC,EAAE;MACX,IACI7C,UAAU,CAAC6C,CAAC,EAAEiD,GAAG,CAAC,IAClB,EACIkM,OAAO,CAAClS,IAAI,CAAC0kB,QAAQ,EAAE1e,GAAG,CAAC,KAAK,CAAC,CAAC,KACjCjD,CAAC,CAACiD,GAAG,CAAC,IAAI,IAAI,IAAI,CAACtC,KAAK,CAACX,CAAC,CAACiD,GAAG,CAAC,CAAC,CAAC,CACrC,EACH;QACE,OAAO,KAAK;MAChB;IACJ;IAEA,KAAK7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGujB,QAAQ,CAACjkB,MAAM,EAAE,EAAEU,CAAC,EAAE;MAClC,IAAI4B,CAAC,CAAC2hB,QAAQ,CAACvjB,CAAC,CAAC,CAAC,EAAE;QAChB,IAAIyjB,cAAc,EAAE;UAChB,OAAO,KAAK,CAAC,CAAC;QAClB;QACA,IAAIC,UAAU,CAAC9hB,CAAC,CAAC2hB,QAAQ,CAACvjB,CAAC,CAAC,CAAC,CAAC,KAAKgN,KAAK,CAACpL,CAAC,CAAC2hB,QAAQ,CAACvjB,CAAC,CAAC,CAAC,CAAC,EAAE;UACtDyjB,cAAc,GAAG,IAAI;QACzB;MACJ;IACJ;IAEA,OAAO,IAAI;EACf;EAEA,SAASE,SAASA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACxhB,QAAQ;EACxB;EAEA,SAASyhB,eAAeA,CAAA,EAAG;IACvB,OAAOC,cAAc,CAAC7gB,GAAG,CAAC;EAC9B;EAEA,SAAS8gB,QAAQA,CAACC,QAAQ,EAAE;IACxB,IAAI7X,eAAe,GAAGF,oBAAoB,CAAC+X,QAAQ,CAAC;MAChDC,KAAK,GAAG9X,eAAe,CAACU,IAAI,IAAI,CAAC;MACjCqX,QAAQ,GAAG/X,eAAe,CAACgY,OAAO,IAAI,CAAC;MACvC/S,MAAM,GAAGjF,eAAe,CAACuB,KAAK,IAAI,CAAC;MACnC0W,KAAK,GAAGjY,eAAe,CAAC4I,IAAI,IAAI5I,eAAe,CAACkY,OAAO,IAAI,CAAC;MAC5DC,IAAI,GAAGnY,eAAe,CAACmL,GAAG,IAAI,CAAC;MAC/B6B,KAAK,GAAGhN,eAAe,CAAC2V,IAAI,IAAI,CAAC;MACjCzI,OAAO,GAAGlN,eAAe,CAACwW,MAAM,IAAI,CAAC;MACrCrJ,OAAO,GAAGnN,eAAe,CAACyW,MAAM,IAAI,CAAC;MACrC2B,YAAY,GAAGpY,eAAe,CAAC0W,WAAW,IAAI,CAAC;IAEnD,IAAI,CAACzgB,QAAQ,GAAGqhB,eAAe,CAACtX,eAAe,CAAC;;IAEhD;IACA,IAAI,CAACqY,aAAa,GACd,CAACD,YAAY,GACbjL,OAAO,GAAG,GAAG;IAAG;IAChBD,OAAO,GAAG,GAAG;IAAG;IAChBF,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5B;IACA;IACA,IAAI,CAACsL,KAAK,GAAG,CAACH,IAAI,GAAGF,KAAK,GAAG,CAAC;IAC9B;IACA;IACA;IACA,IAAI,CAACrS,OAAO,GAAG,CAACX,MAAM,GAAG8S,QAAQ,GAAG,CAAC,GAAGD,KAAK,GAAG,EAAE;IAElD,IAAI,CAACS,KAAK,GAAG,CAAC,CAAC;IAEf,IAAI,CAAC1gB,OAAO,GAAG+X,SAAS,CAAC,CAAC;IAE1B,IAAI,CAAC4I,OAAO,CAAC,CAAC;EAClB;EAEA,SAASC,UAAUA,CAACvlB,GAAG,EAAE;IACrB,OAAOA,GAAG,YAAY0kB,QAAQ;EAClC;EAEA,SAASc,QAAQA,CAAC5d,MAAM,EAAE;IACtB,IAAIA,MAAM,GAAG,CAAC,EAAE;MACZ,OAAOI,IAAI,CAACyd,KAAK,CAAC,CAAC,CAAC,GAAG7d,MAAM,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC,MAAM;MACH,OAAOI,IAAI,CAACyd,KAAK,CAAC7d,MAAM,CAAC;IAC7B;EACJ;;EAEA;EACA,SAAS8d,aAAaA,CAACC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAE;IAChD,IAAIhjB,GAAG,GAAGmF,IAAI,CAAC0L,GAAG,CAACiS,MAAM,CAACzlB,MAAM,EAAE0lB,MAAM,CAAC1lB,MAAM,CAAC;MAC5C4lB,UAAU,GAAG9d,IAAI,CAACC,GAAG,CAAC0d,MAAM,CAACzlB,MAAM,GAAG0lB,MAAM,CAAC1lB,MAAM,CAAC;MACpD6lB,KAAK,GAAG,CAAC;MACTnlB,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,GAAG,EAAEjC,CAAC,EAAE,EAAE;MACtB,IACKilB,WAAW,IAAIF,MAAM,CAAC/kB,CAAC,CAAC,KAAKglB,MAAM,CAAChlB,CAAC,CAAC,IACtC,CAACilB,WAAW,IAAIjY,KAAK,CAAC+X,MAAM,CAAC/kB,CAAC,CAAC,CAAC,KAAKgN,KAAK,CAACgY,MAAM,CAAChlB,CAAC,CAAC,CAAE,EACzD;QACEmlB,KAAK,EAAE;MACX;IACJ;IACA,OAAOA,KAAK,GAAGD,UAAU;EAC7B;;EAEA;;EAEA,SAASE,MAAMA,CAACpd,KAAK,EAAEqd,SAAS,EAAE;IAC9Btd,cAAc,CAACC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY;MACpC,IAAIod,MAAM,GAAG,IAAI,CAACE,SAAS,CAAC,CAAC;QACzB/d,IAAI,GAAG,GAAG;MACd,IAAI6d,MAAM,GAAG,CAAC,EAAE;QACZA,MAAM,GAAG,CAACA,MAAM;QAChB7d,IAAI,GAAG,GAAG;MACd;MACA,OACIA,IAAI,GACJR,QAAQ,CAAC,CAAC,EAAEqe,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAC5BC,SAAS,GACTte,QAAQ,CAAC,CAAC,CAACqe,MAAM,GAAG,EAAE,EAAE,CAAC,CAAC;IAElC,CAAC,CAAC;EACN;EAEAA,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;EAChBA,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;;EAEhB;;EAEAnW,aAAa,CAAC,GAAG,EAAEJ,gBAAgB,CAAC;EACpCI,aAAa,CAAC,IAAI,EAAEJ,gBAAgB,CAAC;EACrCiB,aAAa,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE;IACvDA,MAAM,CAAC4b,OAAO,GAAG,IAAI;IACrB5b,MAAM,CAACL,IAAI,GAAG2hB,gBAAgB,CAAC1W,gBAAgB,EAAErQ,KAAK,CAAC;EAC3D,CAAC,CAAC;;EAEF;;EAEA;EACA;EACA;EACA,IAAIgnB,WAAW,GAAG,iBAAiB;EAEnC,SAASD,gBAAgBA,CAACE,OAAO,EAAEta,MAAM,EAAE;IACvC,IAAIua,OAAO,GAAG,CAACva,MAAM,IAAI,EAAE,EAAE7C,KAAK,CAACmd,OAAO,CAAC;MACvCE,KAAK;MACLC,KAAK;MACLxM,OAAO;IAEX,IAAIsM,OAAO,KAAK,IAAI,EAAE;MAClB,OAAO,IAAI;IACf;IAEAC,KAAK,GAAGD,OAAO,CAACA,OAAO,CAACpmB,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE;IACzCsmB,KAAK,GAAG,CAACD,KAAK,GAAG,EAAE,EAAErd,KAAK,CAACkd,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IACtDpM,OAAO,GAAG,EAAEwM,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG5Y,KAAK,CAAC4Y,KAAK,CAAC,CAAC,CAAC,CAAC;IAE5C,OAAOxM,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGwM,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGxM,OAAO,GAAG,CAACA,OAAO;EACpE;;EAEA;EACA,SAASyM,eAAeA,CAACrnB,KAAK,EAAEsnB,KAAK,EAAE;IACnC,IAAI/lB,GAAG,EAAEwL,IAAI;IACb,IAAIua,KAAK,CAACjiB,MAAM,EAAE;MACd9D,GAAG,GAAG+lB,KAAK,CAACC,KAAK,CAAC,CAAC;MACnBxa,IAAI,GACA,CAACpH,QAAQ,CAAC3F,KAAK,CAAC,IAAIkB,MAAM,CAAClB,KAAK,CAAC,GAC3BA,KAAK,CAAC2B,OAAO,CAAC,CAAC,GACfihB,WAAW,CAAC5iB,KAAK,CAAC,CAAC2B,OAAO,CAAC,CAAC,IAAIJ,GAAG,CAACI,OAAO,CAAC,CAAC;MACvD;MACAJ,GAAG,CAACyC,EAAE,CAACwjB,OAAO,CAACjmB,GAAG,CAACyC,EAAE,CAACrC,OAAO,CAAC,CAAC,GAAGoL,IAAI,CAAC;MACvCrN,KAAK,CAACgG,YAAY,CAACnE,GAAG,EAAE,KAAK,CAAC;MAC9B,OAAOA,GAAG;IACd,CAAC,MAAM;MACH,OAAOqhB,WAAW,CAAC5iB,KAAK,CAAC,CAACynB,KAAK,CAAC,CAAC;IACrC;EACJ;EAEA,SAASC,aAAaA,CAACtkB,CAAC,EAAE;IACtB;IACA;IACA,OAAO,CAACwF,IAAI,CAACyd,KAAK,CAACjjB,CAAC,CAACY,EAAE,CAAC2jB,iBAAiB,CAAC,CAAC,CAAC;EAChD;;EAEA;;EAEA;EACA;EACAjoB,KAAK,CAACgG,YAAY,GAAG,YAAY,CAAC,CAAC;;EAEnC;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASkiB,YAAYA,CAAC5nB,KAAK,EAAE6nB,aAAa,EAAEC,WAAW,EAAE;IACrD,IAAIlB,MAAM,GAAG,IAAI,CAACthB,OAAO,IAAI,CAAC;MAC1ByiB,WAAW;IACf,IAAI,CAAC,IAAI,CAACrkB,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO1D,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGwE,GAAG;IACrC;IACA,IAAIxE,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3BA,KAAK,GAAG+mB,gBAAgB,CAAC1W,gBAAgB,EAAErQ,KAAK,CAAC;QACjD,IAAIA,KAAK,KAAK,IAAI,EAAE;UAChB,OAAO,IAAI;QACf;MACJ,CAAC,MAAM,IAAI4I,IAAI,CAACC,GAAG,CAAC7I,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC8nB,WAAW,EAAE;QAC7C9nB,KAAK,GAAGA,KAAK,GAAG,EAAE;MACtB;MACA,IAAI,CAAC,IAAI,CAACqF,MAAM,IAAIwiB,aAAa,EAAE;QAC/BE,WAAW,GAAGL,aAAa,CAAC,IAAI,CAAC;MACrC;MACA,IAAI,CAACpiB,OAAO,GAAGtF,KAAK;MACpB,IAAI,CAACqF,MAAM,GAAG,IAAI;MAClB,IAAI0iB,WAAW,IAAI,IAAI,EAAE;QACrB,IAAI,CAACxQ,GAAG,CAACwQ,WAAW,EAAE,GAAG,CAAC;MAC9B;MACA,IAAInB,MAAM,KAAK5mB,KAAK,EAAE;QAClB,IAAI,CAAC6nB,aAAa,IAAI,IAAI,CAACG,iBAAiB,EAAE;UAC1CC,WAAW,CACP,IAAI,EACJ5C,cAAc,CAACrlB,KAAK,GAAG4mB,MAAM,EAAE,GAAG,CAAC,EACnC,CAAC,EACD,KACJ,CAAC;QACL,CAAC,MAAM,IAAI,CAAC,IAAI,CAACoB,iBAAiB,EAAE;UAChC,IAAI,CAACA,iBAAiB,GAAG,IAAI;UAC7BtoB,KAAK,CAACgG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;UAC9B,IAAI,CAACsiB,iBAAiB,GAAG,IAAI;QACjC;MACJ;MACA,OAAO,IAAI;IACf,CAAC,MAAM;MACH,OAAO,IAAI,CAAC3iB,MAAM,GAAGuhB,MAAM,GAAGc,aAAa,CAAC,IAAI,CAAC;IACrD;EACJ;EAEA,SAASQ,UAAUA,CAACloB,KAAK,EAAE6nB,aAAa,EAAE;IACtC,IAAI7nB,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3BA,KAAK,GAAG,CAACA,KAAK;MAClB;MAEA,IAAI,CAAC8mB,SAAS,CAAC9mB,KAAK,EAAE6nB,aAAa,CAAC;MAEpC,OAAO,IAAI;IACf,CAAC,MAAM;MACH,OAAO,CAAC,IAAI,CAACf,SAAS,CAAC,CAAC;IAC5B;EACJ;EAEA,SAASqB,cAAcA,CAACN,aAAa,EAAE;IACnC,OAAO,IAAI,CAACf,SAAS,CAAC,CAAC,EAAEe,aAAa,CAAC;EAC3C;EAEA,SAASO,gBAAgBA,CAACP,aAAa,EAAE;IACrC,IAAI,IAAI,CAACxiB,MAAM,EAAE;MACb,IAAI,CAACyhB,SAAS,CAAC,CAAC,EAAEe,aAAa,CAAC;MAChC,IAAI,CAACxiB,MAAM,GAAG,KAAK;MAEnB,IAAIwiB,aAAa,EAAE;QACf,IAAI,CAACQ,QAAQ,CAACX,aAAa,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;MAC3C;IACJ;IACA,OAAO,IAAI;EACf;EAEA,SAASY,uBAAuBA,CAAA,EAAG;IAC/B,IAAI,IAAI,CAACljB,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,CAAC0hB,SAAS,CAAC,IAAI,CAAC1hB,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC1C,CAAC,MAAM,IAAI,OAAO,IAAI,CAACH,EAAE,KAAK,QAAQ,EAAE;MACpC,IAAIsjB,KAAK,GAAGxB,gBAAgB,CAAC3W,WAAW,EAAE,IAAI,CAACnL,EAAE,CAAC;MAClD,IAAIsjB,KAAK,IAAI,IAAI,EAAE;QACf,IAAI,CAACzB,SAAS,CAACyB,KAAK,CAAC;MACzB,CAAC,MAAM;QACH,IAAI,CAACzB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC;MAC3B;IACJ;IACA,OAAO,IAAI;EACf;EAEA,SAAS0B,oBAAoBA,CAACxoB,KAAK,EAAE;IACjC,IAAI,CAAC,IAAI,CAAC0D,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO,KAAK;IAChB;IACA1D,KAAK,GAAGA,KAAK,GAAG4iB,WAAW,CAAC5iB,KAAK,CAAC,CAAC8mB,SAAS,CAAC,CAAC,GAAG,CAAC;IAElD,OAAO,CAAC,IAAI,CAACA,SAAS,CAAC,CAAC,GAAG9mB,KAAK,IAAI,EAAE,KAAK,CAAC;EAChD;EAEA,SAASyoB,oBAAoBA,CAAA,EAAG;IAC5B,OACI,IAAI,CAAC3B,SAAS,CAAC,CAAC,GAAG,IAAI,CAACS,KAAK,CAAC,CAAC,CAACtY,KAAK,CAAC,CAAC,CAAC,CAAC6X,SAAS,CAAC,CAAC,IACpD,IAAI,CAACA,SAAS,CAAC,CAAC,GAAG,IAAI,CAACS,KAAK,CAAC,CAAC,CAACtY,KAAK,CAAC,CAAC,CAAC,CAAC6X,SAAS,CAAC,CAAC;EAE5D;EAEA,SAAS4B,2BAA2BA,CAAA,EAAG;IACnC,IAAI,CAAC1nB,WAAW,CAAC,IAAI,CAAC2nB,aAAa,CAAC,EAAE;MAClC,OAAO,IAAI,CAACA,aAAa;IAC7B;IAEA,IAAIpH,CAAC,GAAG,CAAC,CAAC;MACNoD,KAAK;IAEThgB,UAAU,CAAC4c,CAAC,EAAE,IAAI,CAAC;IACnBA,CAAC,GAAG+C,aAAa,CAAC/C,CAAC,CAAC;IAEpB,IAAIA,CAAC,CAAC7P,EAAE,EAAE;MACNiT,KAAK,GAAGpD,CAAC,CAAClc,MAAM,GAAGzD,SAAS,CAAC2f,CAAC,CAAC7P,EAAE,CAAC,GAAGkR,WAAW,CAACrB,CAAC,CAAC7P,EAAE,CAAC;MACtD,IAAI,CAACiX,aAAa,GACd,IAAI,CAACjlB,OAAO,CAAC,CAAC,IAAI4iB,aAAa,CAAC/E,CAAC,CAAC7P,EAAE,EAAEiT,KAAK,CAACiE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAClE,CAAC,MAAM;MACH,IAAI,CAACD,aAAa,GAAG,KAAK;IAC9B;IAEA,OAAO,IAAI,CAACA,aAAa;EAC7B;EAEA,SAASE,OAAOA,CAAA,EAAG;IACf,OAAO,IAAI,CAACnlB,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC2B,MAAM,GAAG,KAAK;EAChD;EAEA,SAASyjB,WAAWA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACplB,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC2B,MAAM,GAAG,KAAK;EAC/C;EAEA,SAAS0jB,KAAKA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrlB,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC2B,MAAM,IAAI,IAAI,CAACC,OAAO,KAAK,CAAC,GAAG,KAAK;EACrE;;EAEA;EACA,IAAI0jB,WAAW,GAAG,uDAAuD;IACrE;IACA;IACA;IACAC,QAAQ,GAAG,qKAAqK;EAEpL,SAAS5D,cAAcA,CAACrlB,KAAK,EAAEqG,GAAG,EAAE;IAChC,IAAIkf,QAAQ,GAAGvlB,KAAK;MAChB;MACA8J,KAAK,GAAG,IAAI;MACZf,IAAI;MACJmgB,GAAG;MACHC,OAAO;IAEX,IAAIhD,UAAU,CAACnmB,KAAK,CAAC,EAAE;MACnBulB,QAAQ,GAAG;QACP/P,EAAE,EAAExV,KAAK,CAAC+lB,aAAa;QACvB9Z,CAAC,EAAEjM,KAAK,CAACgmB,KAAK;QACd3Z,CAAC,EAAErM,KAAK,CAACsT;MACb,CAAC;IACL,CAAC,MAAM,IAAIrS,QAAQ,CAACjB,KAAK,CAAC,IAAI,CAAC+D,KAAK,CAAC,CAAC/D,KAAK,CAAC,EAAE;MAC1CulB,QAAQ,GAAG,CAAC,CAAC;MACb,IAAIlf,GAAG,EAAE;QACLkf,QAAQ,CAAClf,GAAG,CAAC,GAAG,CAACrG,KAAK;MAC1B,CAAC,MAAM;QACHulB,QAAQ,CAACO,YAAY,GAAG,CAAC9lB,KAAK;MAClC;IACJ,CAAC,MAAM,IAAK8J,KAAK,GAAGkf,WAAW,CAAC3J,IAAI,CAACrf,KAAK,CAAC,EAAG;MAC1C+I,IAAI,GAAGe,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;MAChCyb,QAAQ,GAAG;QACPhZ,CAAC,EAAE,CAAC;QACJN,CAAC,EAAEuC,KAAK,CAAC1E,KAAK,CAAC+H,IAAI,CAAC,CAAC,GAAG9I,IAAI;QAC5BgD,CAAC,EAAEyC,KAAK,CAAC1E,KAAK,CAACgI,IAAI,CAAC,CAAC,GAAG/I,IAAI;QAC5B3F,CAAC,EAAEoL,KAAK,CAAC1E,KAAK,CAACiI,MAAM,CAAC,CAAC,GAAGhJ,IAAI;QAC9B6C,CAAC,EAAE4C,KAAK,CAAC1E,KAAK,CAACkI,MAAM,CAAC,CAAC,GAAGjJ,IAAI;QAC9ByM,EAAE,EAAEhH,KAAK,CAAC4X,QAAQ,CAACtc,KAAK,CAACmI,WAAW,CAAC,GAAG,IAAI,CAAC,CAAC,GAAGlJ,IAAI,CAAE;MAC3D,CAAC;IACL,CAAC,MAAM,IAAKe,KAAK,GAAGmf,QAAQ,CAAC5J,IAAI,CAACrf,KAAK,CAAC,EAAG;MACvC+I,IAAI,GAAGe,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;MAChCyb,QAAQ,GAAG;QACPhZ,CAAC,EAAE6c,QAAQ,CAACtf,KAAK,CAAC,CAAC,CAAC,EAAEf,IAAI,CAAC;QAC3BsD,CAAC,EAAE+c,QAAQ,CAACtf,KAAK,CAAC,CAAC,CAAC,EAAEf,IAAI,CAAC;QAC3BoD,CAAC,EAAEid,QAAQ,CAACtf,KAAK,CAAC,CAAC,CAAC,EAAEf,IAAI,CAAC;QAC3BkD,CAAC,EAAEmd,QAAQ,CAACtf,KAAK,CAAC,CAAC,CAAC,EAAEf,IAAI,CAAC;QAC3BgD,CAAC,EAAEqd,QAAQ,CAACtf,KAAK,CAAC,CAAC,CAAC,EAAEf,IAAI,CAAC;QAC3B3F,CAAC,EAAEgmB,QAAQ,CAACtf,KAAK,CAAC,CAAC,CAAC,EAAEf,IAAI,CAAC;QAC3B6C,CAAC,EAAEwd,QAAQ,CAACtf,KAAK,CAAC,CAAC,CAAC,EAAEf,IAAI;MAC9B,CAAC;IACL,CAAC,MAAM,IAAIwc,QAAQ,IAAI,IAAI,EAAE;MACzB;MACAA,QAAQ,GAAG,CAAC,CAAC;IACjB,CAAC,MAAM,IACH,OAAOA,QAAQ,KAAK,QAAQ,KAC3B,MAAM,IAAIA,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAAC,EAC1C;MACE4D,OAAO,GAAGE,iBAAiB,CACvBzG,WAAW,CAAC2C,QAAQ,CAAC1gB,IAAI,CAAC,EAC1B+d,WAAW,CAAC2C,QAAQ,CAAC3gB,EAAE,CAC3B,CAAC;MAED2gB,QAAQ,GAAG,CAAC,CAAC;MACbA,QAAQ,CAAC/P,EAAE,GAAG2T,OAAO,CAACrD,YAAY;MAClCP,QAAQ,CAAClZ,CAAC,GAAG8c,OAAO,CAACxW,MAAM;IAC/B;IAEAuW,GAAG,GAAG,IAAI5D,QAAQ,CAACC,QAAQ,CAAC;IAE5B,IAAIY,UAAU,CAACnmB,KAAK,CAAC,IAAIO,UAAU,CAACP,KAAK,EAAE,SAAS,CAAC,EAAE;MACnDkpB,GAAG,CAAC3jB,OAAO,GAAGvF,KAAK,CAACuF,OAAO;IAC/B;IAEA,IAAI4gB,UAAU,CAACnmB,KAAK,CAAC,IAAIO,UAAU,CAACP,KAAK,EAAE,UAAU,CAAC,EAAE;MACpDkpB,GAAG,CAACvlB,QAAQ,GAAG3D,KAAK,CAAC2D,QAAQ;IACjC;IAEA,OAAOulB,GAAG;EACd;EAEA7D,cAAc,CAAC/jB,EAAE,GAAGgkB,QAAQ,CAACnlB,SAAS;EACtCklB,cAAc,CAACiE,OAAO,GAAGlE,eAAe;EAExC,SAASgE,QAAQA,CAACG,GAAG,EAAExgB,IAAI,EAAE;IACzB;IACA;IACA;IACA,IAAIxH,GAAG,GAAGgoB,GAAG,IAAIrE,UAAU,CAACqE,GAAG,CAACxf,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAClD;IACA,OAAO,CAAChG,KAAK,CAACxC,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG,IAAIwH,IAAI;EACxC;EAEA,SAASygB,yBAAyBA,CAACC,IAAI,EAAE9E,KAAK,EAAE;IAC5C,IAAIpjB,GAAG,GAAG,CAAC,CAAC;IAEZA,GAAG,CAACoR,MAAM,GACNgS,KAAK,CAAC1V,KAAK,CAAC,CAAC,GAAGwa,IAAI,CAACxa,KAAK,CAAC,CAAC,GAAG,CAAC0V,KAAK,CAACvW,IAAI,CAAC,CAAC,GAAGqb,IAAI,CAACrb,IAAI,CAAC,CAAC,IAAI,EAAE;IACpE,IAAIqb,IAAI,CAAClC,KAAK,CAAC,CAAC,CAAChQ,GAAG,CAAChW,GAAG,CAACoR,MAAM,EAAE,GAAG,CAAC,CAAC+W,OAAO,CAAC/E,KAAK,CAAC,EAAE;MAClD,EAAEpjB,GAAG,CAACoR,MAAM;IAChB;IAEApR,GAAG,CAACukB,YAAY,GAAG,CAACnB,KAAK,GAAG,CAAC8E,IAAI,CAAClC,KAAK,CAAC,CAAC,CAAChQ,GAAG,CAAChW,GAAG,CAACoR,MAAM,EAAE,GAAG,CAAC;IAE9D,OAAOpR,GAAG;EACd;EAEA,SAAS8nB,iBAAiBA,CAACI,IAAI,EAAE9E,KAAK,EAAE;IACpC,IAAIpjB,GAAG;IACP,IAAI,EAAEkoB,IAAI,CAAC/lB,OAAO,CAAC,CAAC,IAAIihB,KAAK,CAACjhB,OAAO,CAAC,CAAC,CAAC,EAAE;MACtC,OAAO;QAAEoiB,YAAY,EAAE,CAAC;QAAEnT,MAAM,EAAE;MAAE,CAAC;IACzC;IAEAgS,KAAK,GAAG0C,eAAe,CAAC1C,KAAK,EAAE8E,IAAI,CAAC;IACpC,IAAIA,IAAI,CAACE,QAAQ,CAAChF,KAAK,CAAC,EAAE;MACtBpjB,GAAG,GAAGioB,yBAAyB,CAACC,IAAI,EAAE9E,KAAK,CAAC;IAChD,CAAC,MAAM;MACHpjB,GAAG,GAAGioB,yBAAyB,CAAC7E,KAAK,EAAE8E,IAAI,CAAC;MAC5CloB,GAAG,CAACukB,YAAY,GAAG,CAACvkB,GAAG,CAACukB,YAAY;MACpCvkB,GAAG,CAACoR,MAAM,GAAG,CAACpR,GAAG,CAACoR,MAAM;IAC5B;IAEA,OAAOpR,GAAG;EACd;;EAEA;EACA,SAASqoB,WAAWA,CAACC,SAAS,EAAEjjB,IAAI,EAAE;IAClC,OAAO,UAAU7B,GAAG,EAAE+kB,MAAM,EAAE;MAC1B,IAAIC,GAAG,EAAEC,GAAG;MACZ;MACA,IAAIF,MAAM,KAAK,IAAI,IAAI,CAAC/lB,KAAK,CAAC,CAAC+lB,MAAM,CAAC,EAAE;QACpCnjB,eAAe,CACXC,IAAI,EACJ,WAAW,GACPA,IAAI,GACJ,sDAAsD,GACtDA,IAAI,GACJ,oBAAoB,GACpB,8EACR,CAAC;QACDojB,GAAG,GAAGjlB,GAAG;QACTA,GAAG,GAAG+kB,MAAM;QACZA,MAAM,GAAGE,GAAG;MAChB;MAEAD,GAAG,GAAG1E,cAAc,CAACtgB,GAAG,EAAE+kB,MAAM,CAAC;MACjC7B,WAAW,CAAC,IAAI,EAAE8B,GAAG,EAAEF,SAAS,CAAC;MACjC,OAAO,IAAI;IACf,CAAC;EACL;EAEA,SAAS5B,WAAWA,CAAC9f,GAAG,EAAEod,QAAQ,EAAE0E,QAAQ,EAAEvkB,YAAY,EAAE;IACxD,IAAIogB,YAAY,GAAGP,QAAQ,CAACQ,aAAa;MACrCF,IAAI,GAAGO,QAAQ,CAACb,QAAQ,CAACS,KAAK,CAAC;MAC/BrT,MAAM,GAAGyT,QAAQ,CAACb,QAAQ,CAACjS,OAAO,CAAC;IAEvC,IAAI,CAACnL,GAAG,CAACzE,OAAO,CAAC,CAAC,EAAE;MAChB;MACA;IACJ;IAEAgC,YAAY,GAAGA,YAAY,IAAI,IAAI,GAAG,IAAI,GAAGA,YAAY;IAEzD,IAAIiN,MAAM,EAAE;MACRyB,QAAQ,CAACjM,GAAG,EAAE6G,GAAG,CAAC7G,GAAG,EAAE,OAAO,CAAC,GAAGwK,MAAM,GAAGsX,QAAQ,CAAC;IACxD;IACA,IAAIpE,IAAI,EAAE;MACN9W,KAAK,CAAC5G,GAAG,EAAE,MAAM,EAAE6G,GAAG,CAAC7G,GAAG,EAAE,MAAM,CAAC,GAAG0d,IAAI,GAAGoE,QAAQ,CAAC;IAC1D;IACA,IAAInE,YAAY,EAAE;MACd3d,GAAG,CAACnE,EAAE,CAACwjB,OAAO,CAACrf,GAAG,CAACnE,EAAE,CAACrC,OAAO,CAAC,CAAC,GAAGmkB,YAAY,GAAGmE,QAAQ,CAAC;IAC9D;IACA,IAAIvkB,YAAY,EAAE;MACdhG,KAAK,CAACgG,YAAY,CAACyC,GAAG,EAAE0d,IAAI,IAAIlT,MAAM,CAAC;IAC3C;EACJ;EAEA,IAAI4E,GAAG,GAAGqS,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC;IAC3BvB,QAAQ,GAAGuB,WAAW,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;EAE1C,SAASM,QAAQA,CAAClqB,KAAK,EAAE;IACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYmqB,MAAM;EAC/D;;EAEA;EACA,SAASC,aAAaA,CAACpqB,KAAK,EAAE;IAC1B,OACI2F,QAAQ,CAAC3F,KAAK,CAAC,IACfkB,MAAM,CAAClB,KAAK,CAAC,IACbkqB,QAAQ,CAAClqB,KAAK,CAAC,IACfiB,QAAQ,CAACjB,KAAK,CAAC,IACfqqB,qBAAqB,CAACrqB,KAAK,CAAC,IAC5BsqB,mBAAmB,CAACtqB,KAAK,CAAC,IAC1BA,KAAK,KAAK,IAAI,IACdA,KAAK,KAAKqE,SAAS;EAE3B;EAEA,SAASimB,mBAAmBA,CAACtqB,KAAK,EAAE;IAChC,IAAIuqB,UAAU,GAAGjqB,QAAQ,CAACN,KAAK,CAAC,IAAI,CAACW,aAAa,CAACX,KAAK,CAAC;MACrDwqB,YAAY,GAAG,KAAK;MACpBC,UAAU,GAAG,CACT,OAAO,EACP,MAAM,EACN,GAAG,EACH,QAAQ,EACR,OAAO,EACP,GAAG,EACH,MAAM,EACN,KAAK,EACL,GAAG,EACH,OAAO,EACP,MAAM,EACN,GAAG,EACH,OAAO,EACP,MAAM,EACN,GAAG,EACH,SAAS,EACT,QAAQ,EACR,GAAG,EACH,SAAS,EACT,QAAQ,EACR,GAAG,EACH,cAAc,EACd,aAAa,EACb,IAAI,CACP;MACDjpB,CAAC;MACDkpB,QAAQ;IAEZ,KAAKlpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGipB,UAAU,CAAC3pB,MAAM,EAAEU,CAAC,IAAI,CAAC,EAAE;MACvCkpB,QAAQ,GAAGD,UAAU,CAACjpB,CAAC,CAAC;MACxBgpB,YAAY,GAAGA,YAAY,IAAIjqB,UAAU,CAACP,KAAK,EAAE0qB,QAAQ,CAAC;IAC9D;IAEA,OAAOH,UAAU,IAAIC,YAAY;EACrC;EAEA,SAASH,qBAAqBA,CAACrqB,KAAK,EAAE;IAClC,IAAI2qB,SAAS,GAAG5qB,OAAO,CAACC,KAAK,CAAC;MAC1B4qB,YAAY,GAAG,KAAK;IACxB,IAAID,SAAS,EAAE;MACXC,YAAY,GACR5qB,KAAK,CAAC6qB,MAAM,CAAC,UAAUC,IAAI,EAAE;QACzB,OAAO,CAAC7pB,QAAQ,CAAC6pB,IAAI,CAAC,IAAIZ,QAAQ,CAAClqB,KAAK,CAAC;MAC7C,CAAC,CAAC,CAACc,MAAM,KAAK,CAAC;IACvB;IACA,OAAO6pB,SAAS,IAAIC,YAAY;EACpC;EAEA,SAASG,cAAcA,CAAC/qB,KAAK,EAAE;IAC3B,IAAIuqB,UAAU,GAAGjqB,QAAQ,CAACN,KAAK,CAAC,IAAI,CAACW,aAAa,CAACX,KAAK,CAAC;MACrDwqB,YAAY,GAAG,KAAK;MACpBC,UAAU,GAAG,CACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU,CACb;MACDjpB,CAAC;MACDkpB,QAAQ;IAEZ,KAAKlpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGipB,UAAU,CAAC3pB,MAAM,EAAEU,CAAC,IAAI,CAAC,EAAE;MACvCkpB,QAAQ,GAAGD,UAAU,CAACjpB,CAAC,CAAC;MACxBgpB,YAAY,GAAGA,YAAY,IAAIjqB,UAAU,CAACP,KAAK,EAAE0qB,QAAQ,CAAC;IAC9D;IAEA,OAAOH,UAAU,IAAIC,YAAY;EACrC;EAEA,SAASQ,iBAAiBA,CAACC,QAAQ,EAAE7iB,GAAG,EAAE;IACtC,IAAI2E,IAAI,GAAGke,QAAQ,CAACle,IAAI,CAAC3E,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC;IAC3C,OAAO2E,IAAI,GAAG,CAAC,CAAC,GACV,UAAU,GACVA,IAAI,GAAG,CAAC,CAAC,GACT,UAAU,GACVA,IAAI,GAAG,CAAC,GACR,SAAS,GACTA,IAAI,GAAG,CAAC,GACR,SAAS,GACTA,IAAI,GAAG,CAAC,GACR,SAAS,GACTA,IAAI,GAAG,CAAC,GACR,UAAU,GACV,UAAU;EACpB;EAEA,SAASme,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;IAC/B;IACA,IAAIxrB,SAAS,CAACkB,MAAM,KAAK,CAAC,EAAE;MACxB,IAAI,CAAClB,SAAS,CAAC,CAAC,CAAC,EAAE;QACfurB,IAAI,GAAG9mB,SAAS;QAChB+mB,OAAO,GAAG/mB,SAAS;MACvB,CAAC,MAAM,IAAI+lB,aAAa,CAACxqB,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QACpCurB,IAAI,GAAGvrB,SAAS,CAAC,CAAC,CAAC;QACnBwrB,OAAO,GAAG/mB,SAAS;MACvB,CAAC,MAAM,IAAI0mB,cAAc,CAACnrB,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QACrCwrB,OAAO,GAAGxrB,SAAS,CAAC,CAAC,CAAC;QACtBurB,IAAI,GAAG9mB,SAAS;MACpB;IACJ;IACA;IACA;IACA,IAAI+D,GAAG,GAAG+iB,IAAI,IAAIvI,WAAW,CAAC,CAAC;MAC3ByI,GAAG,GAAGhE,eAAe,CAACjf,GAAG,EAAE,IAAI,CAAC,CAACkjB,OAAO,CAAC,KAAK,CAAC;MAC/CzpB,MAAM,GAAGnC,KAAK,CAAC6rB,cAAc,CAAC,IAAI,EAAEF,GAAG,CAAC,IAAI,UAAU;MACtDhjB,MAAM,GACF+iB,OAAO,KACNvkB,UAAU,CAACukB,OAAO,CAACvpB,MAAM,CAAC,CAAC,GACtBupB,OAAO,CAACvpB,MAAM,CAAC,CAACxB,IAAI,CAAC,IAAI,EAAE+H,GAAG,CAAC,GAC/BgjB,OAAO,CAACvpB,MAAM,CAAC,CAAC;IAE9B,OAAO,IAAI,CAACA,MAAM,CACdwG,MAAM,IAAI,IAAI,CAACuB,UAAU,CAAC,CAAC,CAAC1B,QAAQ,CAACrG,MAAM,EAAE,IAAI,EAAE+gB,WAAW,CAACxa,GAAG,CAAC,CACvE,CAAC;EACL;EAEA,SAASmf,KAAKA,CAAA,EAAG;IACb,OAAO,IAAI/hB,MAAM,CAAC,IAAI,CAAC;EAC3B;EAEA,SAASkkB,OAAOA,CAAC1pB,KAAK,EAAEuN,KAAK,EAAE;IAC3B,IAAIie,UAAU,GAAG7lB,QAAQ,CAAC3F,KAAK,CAAC,GAAGA,KAAK,GAAG4iB,WAAW,CAAC5iB,KAAK,CAAC;IAC7D,IAAI,EAAE,IAAI,CAAC0D,OAAO,CAAC,CAAC,IAAI8nB,UAAU,CAAC9nB,OAAO,CAAC,CAAC,CAAC,EAAE;MAC3C,OAAO,KAAK;IAChB;IACA6J,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC,IAAI,aAAa;IAC9C,IAAIA,KAAK,KAAK,aAAa,EAAE;MACzB,OAAO,IAAI,CAAC5L,OAAO,CAAC,CAAC,GAAG6pB,UAAU,CAAC7pB,OAAO,CAAC,CAAC;IAChD,CAAC,MAAM;MACH,OAAO6pB,UAAU,CAAC7pB,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC4lB,KAAK,CAAC,CAAC,CAAC+D,OAAO,CAAC/d,KAAK,CAAC,CAAC5L,OAAO,CAAC,CAAC;IACvE;EACJ;EAEA,SAASgoB,QAAQA,CAAC3pB,KAAK,EAAEuN,KAAK,EAAE;IAC5B,IAAIie,UAAU,GAAG7lB,QAAQ,CAAC3F,KAAK,CAAC,GAAGA,KAAK,GAAG4iB,WAAW,CAAC5iB,KAAK,CAAC;IAC7D,IAAI,EAAE,IAAI,CAAC0D,OAAO,CAAC,CAAC,IAAI8nB,UAAU,CAAC9nB,OAAO,CAAC,CAAC,CAAC,EAAE;MAC3C,OAAO,KAAK;IAChB;IACA6J,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC,IAAI,aAAa;IAC9C,IAAIA,KAAK,KAAK,aAAa,EAAE;MACzB,OAAO,IAAI,CAAC5L,OAAO,CAAC,CAAC,GAAG6pB,UAAU,CAAC7pB,OAAO,CAAC,CAAC;IAChD,CAAC,MAAM;MACH,OAAO,IAAI,CAAC4lB,KAAK,CAAC,CAAC,CAACkE,KAAK,CAACle,KAAK,CAAC,CAAC5L,OAAO,CAAC,CAAC,GAAG6pB,UAAU,CAAC7pB,OAAO,CAAC,CAAC;IACrE;EACJ;EAEA,SAAS+pB,SAASA,CAAC7mB,IAAI,EAAED,EAAE,EAAE2I,KAAK,EAAEoe,WAAW,EAAE;IAC7C,IAAIC,SAAS,GAAGjmB,QAAQ,CAACd,IAAI,CAAC,GAAGA,IAAI,GAAG+d,WAAW,CAAC/d,IAAI,CAAC;MACrDgnB,OAAO,GAAGlmB,QAAQ,CAACf,EAAE,CAAC,GAAGA,EAAE,GAAGge,WAAW,CAAChe,EAAE,CAAC;IACjD,IAAI,EAAE,IAAI,CAAClB,OAAO,CAAC,CAAC,IAAIkoB,SAAS,CAACloB,OAAO,CAAC,CAAC,IAAImoB,OAAO,CAACnoB,OAAO,CAAC,CAAC,CAAC,EAAE;MAC/D,OAAO,KAAK;IAChB;IACAioB,WAAW,GAAGA,WAAW,IAAI,IAAI;IACjC,OACI,CAACA,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,GACjB,IAAI,CAACjC,OAAO,CAACkC,SAAS,EAAEre,KAAK,CAAC,GAC9B,CAAC,IAAI,CAACoc,QAAQ,CAACiC,SAAS,EAAEre,KAAK,CAAC,MACrCoe,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,GACjB,IAAI,CAAChC,QAAQ,CAACkC,OAAO,EAAEte,KAAK,CAAC,GAC7B,CAAC,IAAI,CAACmc,OAAO,CAACmC,OAAO,EAAEte,KAAK,CAAC,CAAC;EAE5C;EAEA,SAASue,MAAMA,CAAC9rB,KAAK,EAAEuN,KAAK,EAAE;IAC1B,IAAIie,UAAU,GAAG7lB,QAAQ,CAAC3F,KAAK,CAAC,GAAGA,KAAK,GAAG4iB,WAAW,CAAC5iB,KAAK,CAAC;MACzD+rB,OAAO;IACX,IAAI,EAAE,IAAI,CAACroB,OAAO,CAAC,CAAC,IAAI8nB,UAAU,CAAC9nB,OAAO,CAAC,CAAC,CAAC,EAAE;MAC3C,OAAO,KAAK;IAChB;IACA6J,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC,IAAI,aAAa;IAC9C,IAAIA,KAAK,KAAK,aAAa,EAAE;MACzB,OAAO,IAAI,CAAC5L,OAAO,CAAC,CAAC,KAAK6pB,UAAU,CAAC7pB,OAAO,CAAC,CAAC;IAClD,CAAC,MAAM;MACHoqB,OAAO,GAAGP,UAAU,CAAC7pB,OAAO,CAAC,CAAC;MAC9B,OACI,IAAI,CAAC4lB,KAAK,CAAC,CAAC,CAAC+D,OAAO,CAAC/d,KAAK,CAAC,CAAC5L,OAAO,CAAC,CAAC,IAAIoqB,OAAO,IAChDA,OAAO,IAAI,IAAI,CAACxE,KAAK,CAAC,CAAC,CAACkE,KAAK,CAACle,KAAK,CAAC,CAAC5L,OAAO,CAAC,CAAC;IAEtD;EACJ;EAEA,SAASqqB,aAAaA,CAAChsB,KAAK,EAAEuN,KAAK,EAAE;IACjC,OAAO,IAAI,CAACue,MAAM,CAAC9rB,KAAK,EAAEuN,KAAK,CAAC,IAAI,IAAI,CAACmc,OAAO,CAAC1pB,KAAK,EAAEuN,KAAK,CAAC;EAClE;EAEA,SAAS0e,cAAcA,CAACjsB,KAAK,EAAEuN,KAAK,EAAE;IAClC,OAAO,IAAI,CAACue,MAAM,CAAC9rB,KAAK,EAAEuN,KAAK,CAAC,IAAI,IAAI,CAACoc,QAAQ,CAAC3pB,KAAK,EAAEuN,KAAK,CAAC;EACnE;EAEA,SAASR,IAAIA,CAAC/M,KAAK,EAAEuN,KAAK,EAAE2e,OAAO,EAAE;IACjC,IAAIC,IAAI,EAAEC,SAAS,EAAE/jB,MAAM;IAE3B,IAAI,CAAC,IAAI,CAAC3E,OAAO,CAAC,CAAC,EAAE;MACjB,OAAOc,GAAG;IACd;IAEA2nB,IAAI,GAAG9E,eAAe,CAACrnB,KAAK,EAAE,IAAI,CAAC;IAEnC,IAAI,CAACmsB,IAAI,CAACzoB,OAAO,CAAC,CAAC,EAAE;MACjB,OAAOc,GAAG;IACd;IAEA4nB,SAAS,GAAG,CAACD,IAAI,CAACrF,SAAS,CAAC,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC,IAAI,GAAG;IAEvDvZ,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC;IAE7B,QAAQA,KAAK;MACT,KAAK,MAAM;QACPlF,MAAM,GAAGgkB,SAAS,CAAC,IAAI,EAAEF,IAAI,CAAC,GAAG,EAAE;QACnC;MACJ,KAAK,OAAO;QACR9jB,MAAM,GAAGgkB,SAAS,CAAC,IAAI,EAAEF,IAAI,CAAC;QAC9B;MACJ,KAAK,SAAS;QACV9jB,MAAM,GAAGgkB,SAAS,CAAC,IAAI,EAAEF,IAAI,CAAC,GAAG,CAAC;QAClC;MACJ,KAAK,QAAQ;QACT9jB,MAAM,GAAG,CAAC,IAAI,GAAG8jB,IAAI,IAAI,GAAG;QAC5B;MAAO;MACX,KAAK,QAAQ;QACT9jB,MAAM,GAAG,CAAC,IAAI,GAAG8jB,IAAI,IAAI,GAAG;QAC5B;MAAO;MACX,KAAK,MAAM;QACP9jB,MAAM,GAAG,CAAC,IAAI,GAAG8jB,IAAI,IAAI,IAAI;QAC7B;MAAO;MACX,KAAK,KAAK;QACN9jB,MAAM,GAAG,CAAC,IAAI,GAAG8jB,IAAI,GAAGC,SAAS,IAAI,KAAK;QAC1C;MAAO;MACX,KAAK,MAAM;QACP/jB,MAAM,GAAG,CAAC,IAAI,GAAG8jB,IAAI,GAAGC,SAAS,IAAI,MAAM;QAC3C;MAAO;MACX;QACI/jB,MAAM,GAAG,IAAI,GAAG8jB,IAAI;IAC5B;IAEA,OAAOD,OAAO,GAAG7jB,MAAM,GAAGgG,QAAQ,CAAChG,MAAM,CAAC;EAC9C;EAEA,SAASgkB,SAASA,CAAC7rB,CAAC,EAAEC,CAAC,EAAE;IACrB,IAAID,CAAC,CAAC0O,IAAI,CAAC,CAAC,GAAGzO,CAAC,CAACyO,IAAI,CAAC,CAAC,EAAE;MACrB;MACA;MACA,OAAO,CAACmd,SAAS,CAAC5rB,CAAC,EAAED,CAAC,CAAC;IAC3B;IACA;IACA,IAAI8rB,cAAc,GAAG,CAAC7rB,CAAC,CAAC2N,IAAI,CAAC,CAAC,GAAG5N,CAAC,CAAC4N,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI3N,CAAC,CAACwO,KAAK,CAAC,CAAC,GAAGzO,CAAC,CAACyO,KAAK,CAAC,CAAC,CAAC;MACrE;MACAsd,MAAM,GAAG/rB,CAAC,CAAC+mB,KAAK,CAAC,CAAC,CAAChQ,GAAG,CAAC+U,cAAc,EAAE,QAAQ,CAAC;MAChDE,OAAO;MACPC,MAAM;IAEV,IAAIhsB,CAAC,GAAG8rB,MAAM,GAAG,CAAC,EAAE;MAChBC,OAAO,GAAGhsB,CAAC,CAAC+mB,KAAK,CAAC,CAAC,CAAChQ,GAAG,CAAC+U,cAAc,GAAG,CAAC,EAAE,QAAQ,CAAC;MACrD;MACAG,MAAM,GAAG,CAAChsB,CAAC,GAAG8rB,MAAM,KAAKA,MAAM,GAAGC,OAAO,CAAC;IAC9C,CAAC,MAAM;MACHA,OAAO,GAAGhsB,CAAC,CAAC+mB,KAAK,CAAC,CAAC,CAAChQ,GAAG,CAAC+U,cAAc,GAAG,CAAC,EAAE,QAAQ,CAAC;MACrD;MACAG,MAAM,GAAG,CAAChsB,CAAC,GAAG8rB,MAAM,KAAKC,OAAO,GAAGD,MAAM,CAAC;IAC9C;;IAEA;IACA,OAAO,EAAED,cAAc,GAAGG,MAAM,CAAC,IAAI,CAAC;EAC1C;EAEA/sB,KAAK,CAACgtB,aAAa,GAAG,sBAAsB;EAC5ChtB,KAAK,CAACitB,gBAAgB,GAAG,wBAAwB;EAEjD,SAASvsB,QAAQA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACmnB,KAAK,CAAC,CAAC,CAACzlB,MAAM,CAAC,IAAI,CAAC,CAACD,MAAM,CAAC,kCAAkC,CAAC;EAC/E;EAEA,SAAS+qB,WAAWA,CAACC,UAAU,EAAE;IAC7B,IAAI,CAAC,IAAI,CAACnpB,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO,IAAI;IACf;IACA,IAAIzB,GAAG,GAAG4qB,UAAU,KAAK,IAAI;MACzBzpB,CAAC,GAAGnB,GAAG,GAAG,IAAI,CAACslB,KAAK,CAAC,CAAC,CAACtlB,GAAG,CAAC,CAAC,GAAG,IAAI;IACvC,IAAImB,CAAC,CAACgL,IAAI,CAAC,CAAC,GAAG,CAAC,IAAIhL,CAAC,CAACgL,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE;MACjC,OAAOlE,YAAY,CACf9G,CAAC,EACDnB,GAAG,GACG,gCAAgC,GAChC,8BACV,CAAC;IACL;IACA,IAAI4E,UAAU,CAAC1F,IAAI,CAAChB,SAAS,CAACysB,WAAW,CAAC,EAAE;MACxC;MACA,IAAI3qB,GAAG,EAAE;QACL,OAAO,IAAI,CAAC6qB,MAAM,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC;MACtC,CAAC,MAAM;QACH,OAAO,IAAIzrB,IAAI,CAAC,IAAI,CAACQ,OAAO,CAAC,CAAC,GAAG,IAAI,CAACmlB,SAAS,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CACzD8F,WAAW,CAAC,CAAC,CACb7iB,OAAO,CAAC,GAAG,EAAEG,YAAY,CAAC9G,CAAC,EAAE,GAAG,CAAC,CAAC;MAC3C;IACJ;IACA,OAAO8G,YAAY,CACf9G,CAAC,EACDnB,GAAG,GAAG,8BAA8B,GAAG,4BAC3C,CAAC;EACL;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAAS8qB,OAAOA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACrpB,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO,oBAAoB,GAAG,IAAI,CAACuB,EAAE,GAAG,MAAM;IAClD;IACA,IAAI0E,IAAI,GAAG,QAAQ;MACfqjB,IAAI,GAAG,EAAE;MACTC,MAAM;MACN7e,IAAI;MACJ8e,QAAQ;MACRC,MAAM;IACV,IAAI,CAAC,IAAI,CAACtE,OAAO,CAAC,CAAC,EAAE;MACjBlf,IAAI,GAAG,IAAI,CAACmd,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,kBAAkB;MACjEkG,IAAI,GAAG,GAAG;IACd;IACAC,MAAM,GAAG,GAAG,GAAGtjB,IAAI,GAAG,KAAK;IAC3ByE,IAAI,GAAG,CAAC,IAAI,IAAI,CAACA,IAAI,CAAC,CAAC,IAAI,IAAI,CAACA,IAAI,CAAC,CAAC,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ;IAClE8e,QAAQ,GAAG,uBAAuB;IAClCC,MAAM,GAAGH,IAAI,GAAG,MAAM;IAEtB,OAAO,IAAI,CAACnrB,MAAM,CAACorB,MAAM,GAAG7e,IAAI,GAAG8e,QAAQ,GAAGC,MAAM,CAAC;EACzD;EAEA,SAAStrB,MAAMA,CAACurB,WAAW,EAAE;IACzB,IAAI,CAACA,WAAW,EAAE;MACdA,WAAW,GAAG,IAAI,CAACrE,KAAK,CAAC,CAAC,GACpBrpB,KAAK,CAACitB,gBAAgB,GACtBjtB,KAAK,CAACgtB,aAAa;IAC7B;IACA,IAAIrkB,MAAM,GAAG6B,YAAY,CAAC,IAAI,EAAEkjB,WAAW,CAAC;IAC5C,OAAO,IAAI,CAACxjB,UAAU,CAAC,CAAC,CAACyjB,UAAU,CAAChlB,MAAM,CAAC;EAC/C;EAEA,SAASxD,IAAIA,CAACsmB,IAAI,EAAEze,aAAa,EAAE;IAC/B,IACI,IAAI,CAAChJ,OAAO,CAAC,CAAC,KACZiC,QAAQ,CAACwlB,IAAI,CAAC,IAAIA,IAAI,CAACznB,OAAO,CAAC,CAAC,IAAKkf,WAAW,CAACuI,IAAI,CAAC,CAACznB,OAAO,CAAC,CAAC,CAAC,EACrE;MACE,OAAO2hB,cAAc,CAAC;QAAEzgB,EAAE,EAAE,IAAI;QAAEC,IAAI,EAAEsmB;MAAK,CAAC,CAAC,CAC1CrpB,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,CACrBwrB,QAAQ,CAAC,CAAC5gB,aAAa,CAAC;IACjC,CAAC,MAAM;MACH,OAAO,IAAI,CAAC9C,UAAU,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;IAC1C;EACJ;EAEA,SAASojB,OAAOA,CAAC7gB,aAAa,EAAE;IAC5B,OAAO,IAAI,CAAC7H,IAAI,CAAC+d,WAAW,CAAC,CAAC,EAAElW,aAAa,CAAC;EAClD;EAEA,SAAS9H,EAAEA,CAACumB,IAAI,EAAEze,aAAa,EAAE;IAC7B,IACI,IAAI,CAAChJ,OAAO,CAAC,CAAC,KACZiC,QAAQ,CAACwlB,IAAI,CAAC,IAAIA,IAAI,CAACznB,OAAO,CAAC,CAAC,IAAKkf,WAAW,CAACuI,IAAI,CAAC,CAACznB,OAAO,CAAC,CAAC,CAAC,EACrE;MACE,OAAO2hB,cAAc,CAAC;QAAExgB,IAAI,EAAE,IAAI;QAAED,EAAE,EAAEumB;MAAK,CAAC,CAAC,CAC1CrpB,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,CACrBwrB,QAAQ,CAAC,CAAC5gB,aAAa,CAAC;IACjC,CAAC,MAAM;MACH,OAAO,IAAI,CAAC9C,UAAU,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;IAC1C;EACJ;EAEA,SAASqjB,KAAKA,CAAC9gB,aAAa,EAAE;IAC1B,OAAO,IAAI,CAAC9H,EAAE,CAACge,WAAW,CAAC,CAAC,EAAElW,aAAa,CAAC;EAChD;;EAEA;EACA;EACA;EACA,SAAS5K,MAAMA,CAACuE,GAAG,EAAE;IACjB,IAAIonB,aAAa;IAEjB,IAAIpnB,GAAG,KAAKhC,SAAS,EAAE;MACnB,OAAO,IAAI,CAACkB,OAAO,CAACyX,KAAK;IAC7B,CAAC,MAAM;MACHyQ,aAAa,GAAGnQ,SAAS,CAACjX,GAAG,CAAC;MAC9B,IAAIonB,aAAa,IAAI,IAAI,EAAE;QACvB,IAAI,CAACloB,OAAO,GAAGkoB,aAAa;MAChC;MACA,OAAO,IAAI;IACf;EACJ;EAEA,IAAIC,IAAI,GAAG1nB,SAAS,CAChB,iJAAiJ,EACjJ,UAAUK,GAAG,EAAE;IACX,IAAIA,GAAG,KAAKhC,SAAS,EAAE;MACnB,OAAO,IAAI,CAACuF,UAAU,CAAC,CAAC;IAC5B,CAAC,MAAM;MACH,OAAO,IAAI,CAAC9H,MAAM,CAACuE,GAAG,CAAC;IAC3B;EACJ,CACJ,CAAC;EAED,SAASuD,UAAUA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACrE,OAAO;EACvB;EAEA,IAAIooB,aAAa,GAAG,IAAI;IACpBC,aAAa,GAAG,EAAE,GAAGD,aAAa;IAClCE,WAAW,GAAG,EAAE,GAAGD,aAAa;IAChCE,gBAAgB,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAGD,WAAW;;EAE1D;EACA,SAASE,KAAKA,CAACC,QAAQ,EAAEC,OAAO,EAAE;IAC9B,OAAO,CAAED,QAAQ,GAAGC,OAAO,GAAIA,OAAO,IAAIA,OAAO;EACrD;EAEA,SAASC,gBAAgBA,CAAC3hB,CAAC,EAAEnJ,CAAC,EAAE6I,CAAC,EAAE;IAC/B;IACA,IAAIM,CAAC,GAAG,GAAG,IAAIA,CAAC,IAAI,CAAC,EAAE;MACnB;MACA,OAAO,IAAIpL,IAAI,CAACoL,CAAC,GAAG,GAAG,EAAEnJ,CAAC,EAAE6I,CAAC,CAAC,GAAG6hB,gBAAgB;IACrD,CAAC,MAAM;MACH,OAAO,IAAI3sB,IAAI,CAACoL,CAAC,EAAEnJ,CAAC,EAAE6I,CAAC,CAAC,CAACtK,OAAO,CAAC,CAAC;IACtC;EACJ;EAEA,SAASwsB,cAAcA,CAAC5hB,CAAC,EAAEnJ,CAAC,EAAE6I,CAAC,EAAE;IAC7B;IACA,IAAIM,CAAC,GAAG,GAAG,IAAIA,CAAC,IAAI,CAAC,EAAE;MACnB;MACA,OAAOpL,IAAI,CAACyU,GAAG,CAACrJ,CAAC,GAAG,GAAG,EAAEnJ,CAAC,EAAE6I,CAAC,CAAC,GAAG6hB,gBAAgB;IACrD,CAAC,MAAM;MACH,OAAO3sB,IAAI,CAACyU,GAAG,CAACrJ,CAAC,EAAEnJ,CAAC,EAAE6I,CAAC,CAAC;IAC5B;EACJ;EAEA,SAASqf,OAAOA,CAAC/d,KAAK,EAAE;IACpB,IAAI4d,IAAI,EAAEiD,WAAW;IACrB7gB,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC;IAC7B,IAAIA,KAAK,KAAKlJ,SAAS,IAAIkJ,KAAK,KAAK,aAAa,IAAI,CAAC,IAAI,CAAC7J,OAAO,CAAC,CAAC,EAAE;MACnE,OAAO,IAAI;IACf;IAEA0qB,WAAW,GAAG,IAAI,CAAC/oB,MAAM,GAAG8oB,cAAc,GAAGD,gBAAgB;IAE7D,QAAQ3gB,KAAK;MACT,KAAK,MAAM;QACP4d,IAAI,GAAGiD,WAAW,CAAC,IAAI,CAAChgB,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACrC;MACJ,KAAK,SAAS;QACV+c,IAAI,GAAGiD,WAAW,CACd,IAAI,CAAChgB,IAAI,CAAC,CAAC,EACX,IAAI,CAACa,KAAK,CAAC,CAAC,GAAI,IAAI,CAACA,KAAK,CAAC,CAAC,GAAG,CAAE,EACjC,CACJ,CAAC;QACD;MACJ,KAAK,OAAO;QACRkc,IAAI,GAAGiD,WAAW,CAAC,IAAI,CAAChgB,IAAI,CAAC,CAAC,EAAE,IAAI,CAACa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAChD;MACJ,KAAK,MAAM;QACPkc,IAAI,GAAGiD,WAAW,CACd,IAAI,CAAChgB,IAAI,CAAC,CAAC,EACX,IAAI,CAACa,KAAK,CAAC,CAAC,EACZ,IAAI,CAACC,IAAI,CAAC,CAAC,GAAG,IAAI,CAACqH,OAAO,CAAC,CAC/B,CAAC;QACD;MACJ,KAAK,SAAS;QACV4U,IAAI,GAAGiD,WAAW,CACd,IAAI,CAAChgB,IAAI,CAAC,CAAC,EACX,IAAI,CAACa,KAAK,CAAC,CAAC,EACZ,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,IAAI,CAACmf,UAAU,CAAC,CAAC,GAAG,CAAC,CACxC,CAAC;QACD;MACJ,KAAK,KAAK;MACV,KAAK,MAAM;QACPlD,IAAI,GAAGiD,WAAW,CAAC,IAAI,CAAChgB,IAAI,CAAC,CAAC,EAAE,IAAI,CAACa,KAAK,CAAC,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;QAC1D;MACJ,KAAK,MAAM;QACPic,IAAI,GAAG,IAAI,CAACnnB,EAAE,CAACrC,OAAO,CAAC,CAAC;QACxBwpB,IAAI,IAAI4C,KAAK,CACT5C,IAAI,IAAI,IAAI,CAAC9lB,MAAM,GAAG,CAAC,GAAG,IAAI,CAACyhB,SAAS,CAAC,CAAC,GAAG8G,aAAa,CAAC,EAC3DC,WACJ,CAAC;QACD;MACJ,KAAK,QAAQ;QACT1C,IAAI,GAAG,IAAI,CAACnnB,EAAE,CAACrC,OAAO,CAAC,CAAC;QACxBwpB,IAAI,IAAI4C,KAAK,CAAC5C,IAAI,EAAEyC,aAAa,CAAC;QAClC;MACJ,KAAK,QAAQ;QACTzC,IAAI,GAAG,IAAI,CAACnnB,EAAE,CAACrC,OAAO,CAAC,CAAC;QACxBwpB,IAAI,IAAI4C,KAAK,CAAC5C,IAAI,EAAEwC,aAAa,CAAC;QAClC;IACR;IAEA,IAAI,CAAC3pB,EAAE,CAACwjB,OAAO,CAAC2D,IAAI,CAAC;IACrBzrB,KAAK,CAACgG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;IAC9B,OAAO,IAAI;EACf;EAEA,SAAS+lB,KAAKA,CAACle,KAAK,EAAE;IAClB,IAAI4d,IAAI,EAAEiD,WAAW;IACrB7gB,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC;IAC7B,IAAIA,KAAK,KAAKlJ,SAAS,IAAIkJ,KAAK,KAAK,aAAa,IAAI,CAAC,IAAI,CAAC7J,OAAO,CAAC,CAAC,EAAE;MACnE,OAAO,IAAI;IACf;IAEA0qB,WAAW,GAAG,IAAI,CAAC/oB,MAAM,GAAG8oB,cAAc,GAAGD,gBAAgB;IAE7D,QAAQ3gB,KAAK;MACT,KAAK,MAAM;QACP4d,IAAI,GAAGiD,WAAW,CAAC,IAAI,CAAChgB,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;QAC7C;MACJ,KAAK,SAAS;QACV+c,IAAI,GACAiD,WAAW,CACP,IAAI,CAAChgB,IAAI,CAAC,CAAC,EACX,IAAI,CAACa,KAAK,CAAC,CAAC,GAAI,IAAI,CAACA,KAAK,CAAC,CAAC,GAAG,CAAE,GAAG,CAAC,EACrC,CACJ,CAAC,GAAG,CAAC;QACT;MACJ,KAAK,OAAO;QACRkc,IAAI,GAAGiD,WAAW,CAAC,IAAI,CAAChgB,IAAI,CAAC,CAAC,EAAE,IAAI,CAACa,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;QACxD;MACJ,KAAK,MAAM;QACPkc,IAAI,GACAiD,WAAW,CACP,IAAI,CAAChgB,IAAI,CAAC,CAAC,EACX,IAAI,CAACa,KAAK,CAAC,CAAC,EACZ,IAAI,CAACC,IAAI,CAAC,CAAC,GAAG,IAAI,CAACqH,OAAO,CAAC,CAAC,GAAG,CACnC,CAAC,GAAG,CAAC;QACT;MACJ,KAAK,SAAS;QACV4U,IAAI,GACAiD,WAAW,CACP,IAAI,CAAChgB,IAAI,CAAC,CAAC,EACX,IAAI,CAACa,KAAK,CAAC,CAAC,EACZ,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,IAAI,CAACmf,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAC5C,CAAC,GAAG,CAAC;QACT;MACJ,KAAK,KAAK;MACV,KAAK,MAAM;QACPlD,IAAI,GAAGiD,WAAW,CAAC,IAAI,CAAChgB,IAAI,CAAC,CAAC,EAAE,IAAI,CAACa,KAAK,CAAC,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClE;MACJ,KAAK,MAAM;QACPic,IAAI,GAAG,IAAI,CAACnnB,EAAE,CAACrC,OAAO,CAAC,CAAC;QACxBwpB,IAAI,IACA0C,WAAW,GACXE,KAAK,CACD5C,IAAI,IAAI,IAAI,CAAC9lB,MAAM,GAAG,CAAC,GAAG,IAAI,CAACyhB,SAAS,CAAC,CAAC,GAAG8G,aAAa,CAAC,EAC3DC,WACJ,CAAC,GACD,CAAC;QACL;MACJ,KAAK,QAAQ;QACT1C,IAAI,GAAG,IAAI,CAACnnB,EAAE,CAACrC,OAAO,CAAC,CAAC;QACxBwpB,IAAI,IAAIyC,aAAa,GAAGG,KAAK,CAAC5C,IAAI,EAAEyC,aAAa,CAAC,GAAG,CAAC;QACtD;MACJ,KAAK,QAAQ;QACTzC,IAAI,GAAG,IAAI,CAACnnB,EAAE,CAACrC,OAAO,CAAC,CAAC;QACxBwpB,IAAI,IAAIwC,aAAa,GAAGI,KAAK,CAAC5C,IAAI,EAAEwC,aAAa,CAAC,GAAG,CAAC;QACtD;IACR;IAEA,IAAI,CAAC3pB,EAAE,CAACwjB,OAAO,CAAC2D,IAAI,CAAC;IACrBzrB,KAAK,CAACgG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;IAC9B,OAAO,IAAI;EACf;EAEA,SAAS/D,OAAOA,CAAA,EAAG;IACf,OAAO,IAAI,CAACqC,EAAE,CAACrC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC2D,OAAO,IAAI,CAAC,IAAI,KAAK;EAC1D;EAEA,SAASgpB,IAAIA,CAAA,EAAG;IACZ,OAAO1lB,IAAI,CAAC2F,KAAK,CAAC,IAAI,CAAC5M,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;EAC5C;EAEA,SAASmrB,MAAMA,CAAA,EAAG;IACd,OAAO,IAAI3rB,IAAI,CAAC,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC;EACnC;EAEA,SAASinB,OAAOA,CAAA,EAAG;IACf,IAAIxlB,CAAC,GAAG,IAAI;IACZ,OAAO,CACHA,CAAC,CAACgL,IAAI,CAAC,CAAC,EACRhL,CAAC,CAAC6L,KAAK,CAAC,CAAC,EACT7L,CAAC,CAAC8L,IAAI,CAAC,CAAC,EACR9L,CAAC,CAACigB,IAAI,CAAC,CAAC,EACRjgB,CAAC,CAAC8gB,MAAM,CAAC,CAAC,EACV9gB,CAAC,CAAC+gB,MAAM,CAAC,CAAC,EACV/gB,CAAC,CAACghB,WAAW,CAAC,CAAC,CAClB;EACL;EAEA,SAASmK,QAAQA,CAAA,EAAG;IAChB,IAAInrB,CAAC,GAAG,IAAI;IACZ,OAAO;MACHoiB,KAAK,EAAEpiB,CAAC,CAACgL,IAAI,CAAC,CAAC;MACfuE,MAAM,EAAEvP,CAAC,CAAC6L,KAAK,CAAC,CAAC;MACjBC,IAAI,EAAE9L,CAAC,CAAC8L,IAAI,CAAC,CAAC;MACdwL,KAAK,EAAEtX,CAAC,CAACsX,KAAK,CAAC,CAAC;MAChBE,OAAO,EAAExX,CAAC,CAACwX,OAAO,CAAC,CAAC;MACpBC,OAAO,EAAEzX,CAAC,CAACyX,OAAO,CAAC,CAAC;MACpBiL,YAAY,EAAE1iB,CAAC,CAAC0iB,YAAY,CAAC;IACjC,CAAC;EACL;EAEA,SAAS0I,MAAMA,CAAA,EAAG;IACd;IACA,OAAO,IAAI,CAAC9qB,OAAO,CAAC,CAAC,GAAG,IAAI,CAACkpB,WAAW,CAAC,CAAC,GAAG,IAAI;EACrD;EAEA,SAAS6B,SAASA,CAAA,EAAG;IACjB,OAAO/qB,OAAO,CAAC,IAAI,CAAC;EACxB;EAEA,SAASgrB,YAAYA,CAAA,EAAG;IACpB,OAAOhtB,MAAM,CAAC,CAAC,CAAC,EAAEyB,eAAe,CAAC,IAAI,CAAC,CAAC;EAC5C;EAEA,SAASwrB,SAASA,CAAA,EAAG;IACjB,OAAOxrB,eAAe,CAAC,IAAI,CAAC,CAACb,QAAQ;EACzC;EAEA,SAASssB,YAAYA,CAAA,EAAG;IACpB,OAAO;MACH5uB,KAAK,EAAE,IAAI,CAACiF,EAAE;MACdpD,MAAM,EAAE,IAAI,CAACqD,EAAE;MACfpD,MAAM,EAAE,IAAI,CAACyD,OAAO;MACpBkf,KAAK,EAAE,IAAI,CAACpf,MAAM;MAClBtD,MAAM,EAAE,IAAI,CAACoC;IACjB,CAAC;EACL;EAEAoF,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;EACpCA,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;EACrCA,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;EACtCA,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;EACvCA,cAAc,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC;EAE1CA,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC;EAC9CA,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;EAC5CA,cAAc,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;EAC7CA,cAAc,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;EAE9CkH,aAAa,CAAC,GAAG,EAAEoe,YAAY,CAAC;EAChCpe,aAAa,CAAC,IAAI,EAAEoe,YAAY,CAAC;EACjCpe,aAAa,CAAC,KAAK,EAAEoe,YAAY,CAAC;EAClCpe,aAAa,CAAC,MAAM,EAAEqe,YAAY,CAAC;EACnCre,aAAa,CAAC,OAAO,EAAEse,cAAc,CAAC;EAEtCzd,aAAa,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,UAC/CtR,KAAK,EACLiK,KAAK,EACLxE,MAAM,EACN+D,KAAK,EACP;IACE,IAAIzG,GAAG,GAAG0C,MAAM,CAACF,OAAO,CAACypB,SAAS,CAAChvB,KAAK,EAAEwJ,KAAK,EAAE/D,MAAM,CAACtB,OAAO,CAAC;IAChE,IAAIpB,GAAG,EAAE;MACLI,eAAe,CAACsC,MAAM,CAAC,CAAC1C,GAAG,GAAGA,GAAG;IACrC,CAAC,MAAM;MACHI,eAAe,CAACsC,MAAM,CAAC,CAAChD,UAAU,GAAGzC,KAAK;IAC9C;EACJ,CAAC,CAAC;EAEFyQ,aAAa,CAAC,GAAG,EAAEP,aAAa,CAAC;EACjCO,aAAa,CAAC,IAAI,EAAEP,aAAa,CAAC;EAClCO,aAAa,CAAC,KAAK,EAAEP,aAAa,CAAC;EACnCO,aAAa,CAAC,MAAM,EAAEP,aAAa,CAAC;EACpCO,aAAa,CAAC,IAAI,EAAEwe,mBAAmB,CAAC;EAExC3d,aAAa,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,EAAEK,IAAI,CAAC;EAC/CL,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE+D,KAAK,EAAE;IACzD,IAAIM,KAAK;IACT,IAAIrE,MAAM,CAACF,OAAO,CAAC2pB,oBAAoB,EAAE;MACrCplB,KAAK,GAAG9J,KAAK,CAAC8J,KAAK,CAACrE,MAAM,CAACF,OAAO,CAAC2pB,oBAAoB,CAAC;IAC5D;IAEA,IAAIzpB,MAAM,CAACF,OAAO,CAAC4pB,mBAAmB,EAAE;MACpCllB,KAAK,CAAC0H,IAAI,CAAC,GAAGlM,MAAM,CAACF,OAAO,CAAC4pB,mBAAmB,CAACnvB,KAAK,EAAE8J,KAAK,CAAC;IAClE,CAAC,MAAM;MACHG,KAAK,CAAC0H,IAAI,CAAC,GAAGwD,QAAQ,CAACnV,KAAK,EAAE,EAAE,CAAC;IACrC;EACJ,CAAC,CAAC;EAEF,SAASovB,UAAUA,CAAChsB,CAAC,EAAEvB,MAAM,EAAE;IAC3B,IAAIL,CAAC;MACD4d,CAAC;MACDlQ,IAAI;MACJmgB,IAAI,GAAG,IAAI,CAACC,KAAK,IAAIhS,SAAS,CAAC,IAAI,CAAC,CAACgS,KAAK;IAC9C,KAAK9tB,CAAC,GAAG,CAAC,EAAE4d,CAAC,GAAGiQ,IAAI,CAACvuB,MAAM,EAAEU,CAAC,GAAG4d,CAAC,EAAE,EAAE5d,CAAC,EAAE;MACrC,QAAQ,OAAO6tB,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK;QACxB,KAAK,QAAQ;UACT;UACArgB,IAAI,GAAGxP,KAAK,CAAC2vB,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK,CAAC,CAACjE,OAAO,CAAC,KAAK,CAAC;UAC1C+D,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK,GAAGrgB,IAAI,CAACvN,OAAO,CAAC,CAAC;UAC9B;MACR;MAEA,QAAQ,OAAO0tB,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK;QACxB,KAAK,WAAW;UACZH,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,GAAG,CAACC,QAAQ;UACzB;QACJ,KAAK,QAAQ;UACT;UACAvgB,IAAI,GAAGxP,KAAK,CAAC2vB,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,CAAC,CAAClE,OAAO,CAAC,KAAK,CAAC,CAAC3pB,OAAO,CAAC,CAAC;UACpD0tB,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,GAAGtgB,IAAI,CAACvN,OAAO,CAAC,CAAC;UAC9B;MACR;IACJ;IACA,OAAO0tB,IAAI;EACf;EAEA,SAASK,eAAeA,CAACC,OAAO,EAAE9tB,MAAM,EAAEE,MAAM,EAAE;IAC9C,IAAIP,CAAC;MACD4d,CAAC;MACDiQ,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;MAClBzoB,IAAI;MACJ4W,IAAI;MACJoS,MAAM;IACVD,OAAO,GAAGA,OAAO,CAACzkB,WAAW,CAAC,CAAC;IAE/B,KAAK1J,CAAC,GAAG,CAAC,EAAE4d,CAAC,GAAGiQ,IAAI,CAACvuB,MAAM,EAAEU,CAAC,GAAG4d,CAAC,EAAE,EAAE5d,CAAC,EAAE;MACrCoF,IAAI,GAAGyoB,IAAI,CAAC7tB,CAAC,CAAC,CAACoF,IAAI,CAACsE,WAAW,CAAC,CAAC;MACjCsS,IAAI,GAAG6R,IAAI,CAAC7tB,CAAC,CAAC,CAACgc,IAAI,CAACtS,WAAW,CAAC,CAAC;MACjC0kB,MAAM,GAAGP,IAAI,CAAC7tB,CAAC,CAAC,CAACouB,MAAM,CAAC1kB,WAAW,CAAC,CAAC;MAErC,IAAInJ,MAAM,EAAE;QACR,QAAQF,MAAM;UACV,KAAK,GAAG;UACR,KAAK,IAAI;UACT,KAAK,KAAK;YACN,IAAI2b,IAAI,KAAKmS,OAAO,EAAE;cAClB,OAAON,IAAI,CAAC7tB,CAAC,CAAC;YAClB;YACA;UAEJ,KAAK,MAAM;YACP,IAAIoF,IAAI,KAAK+oB,OAAO,EAAE;cAClB,OAAON,IAAI,CAAC7tB,CAAC,CAAC;YAClB;YACA;UAEJ,KAAK,OAAO;YACR,IAAIouB,MAAM,KAAKD,OAAO,EAAE;cACpB,OAAON,IAAI,CAAC7tB,CAAC,CAAC;YAClB;YACA;QACR;MACJ,CAAC,MAAM,IAAI,CAACoF,IAAI,EAAE4W,IAAI,EAAEoS,MAAM,CAAC,CAACrd,OAAO,CAACod,OAAO,CAAC,IAAI,CAAC,EAAE;QACnD,OAAON,IAAI,CAAC7tB,CAAC,CAAC;MAClB;IACJ;EACJ;EAEA,SAASquB,qBAAqBA,CAAC9sB,GAAG,EAAEqL,IAAI,EAAE;IACtC,IAAI0hB,GAAG,GAAG/sB,GAAG,CAACwsB,KAAK,IAAIxsB,GAAG,CAACysB,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1C,IAAIphB,IAAI,KAAK/J,SAAS,EAAE;MACpB,OAAO3E,KAAK,CAACqD,GAAG,CAACwsB,KAAK,CAAC,CAACnhB,IAAI,CAAC,CAAC;IAClC,CAAC,MAAM;MACH,OAAO1O,KAAK,CAACqD,GAAG,CAACwsB,KAAK,CAAC,CAACnhB,IAAI,CAAC,CAAC,GAAG,CAACA,IAAI,GAAGrL,GAAG,CAAC6jB,MAAM,IAAIkJ,GAAG;IAC9D;EACJ;EAEA,SAASC,UAAUA,CAAA,EAAG;IAClB,IAAIvuB,CAAC;MACD4d,CAAC;MACDra,GAAG;MACHsqB,IAAI,GAAG,IAAI,CAACzlB,UAAU,CAAC,CAAC,CAACylB,IAAI,CAAC,CAAC;IACnC,KAAK7tB,CAAC,GAAG,CAAC,EAAE4d,CAAC,GAAGiQ,IAAI,CAACvuB,MAAM,EAAEU,CAAC,GAAG4d,CAAC,EAAE,EAAE5d,CAAC,EAAE;MACrC;MACAuD,GAAG,GAAG,IAAI,CAACwiB,KAAK,CAAC,CAAC,CAAC+D,OAAO,CAAC,KAAK,CAAC,CAAC3pB,OAAO,CAAC,CAAC;MAE3C,IAAI0tB,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK,IAAIxqB,GAAG,IAAIA,GAAG,IAAIsqB,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,EAAE;QAC9C,OAAOH,IAAI,CAAC7tB,CAAC,CAAC,CAACoF,IAAI;MACvB;MACA,IAAIyoB,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,IAAIzqB,GAAG,IAAIA,GAAG,IAAIsqB,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK,EAAE;QAC9C,OAAOF,IAAI,CAAC7tB,CAAC,CAAC,CAACoF,IAAI;MACvB;IACJ;IAEA,OAAO,EAAE;EACb;EAEA,SAASopB,YAAYA,CAAA,EAAG;IACpB,IAAIxuB,CAAC;MACD4d,CAAC;MACDra,GAAG;MACHsqB,IAAI,GAAG,IAAI,CAACzlB,UAAU,CAAC,CAAC,CAACylB,IAAI,CAAC,CAAC;IACnC,KAAK7tB,CAAC,GAAG,CAAC,EAAE4d,CAAC,GAAGiQ,IAAI,CAACvuB,MAAM,EAAEU,CAAC,GAAG4d,CAAC,EAAE,EAAE5d,CAAC,EAAE;MACrC;MACAuD,GAAG,GAAG,IAAI,CAACwiB,KAAK,CAAC,CAAC,CAAC+D,OAAO,CAAC,KAAK,CAAC,CAAC3pB,OAAO,CAAC,CAAC;MAE3C,IAAI0tB,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK,IAAIxqB,GAAG,IAAIA,GAAG,IAAIsqB,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,EAAE;QAC9C,OAAOH,IAAI,CAAC7tB,CAAC,CAAC,CAACouB,MAAM;MACzB;MACA,IAAIP,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,IAAIzqB,GAAG,IAAIA,GAAG,IAAIsqB,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK,EAAE;QAC9C,OAAOF,IAAI,CAAC7tB,CAAC,CAAC,CAACouB,MAAM;MACzB;IACJ;IAEA,OAAO,EAAE;EACb;EAEA,SAASK,UAAUA,CAAA,EAAG;IAClB,IAAIzuB,CAAC;MACD4d,CAAC;MACDra,GAAG;MACHsqB,IAAI,GAAG,IAAI,CAACzlB,UAAU,CAAC,CAAC,CAACylB,IAAI,CAAC,CAAC;IACnC,KAAK7tB,CAAC,GAAG,CAAC,EAAE4d,CAAC,GAAGiQ,IAAI,CAACvuB,MAAM,EAAEU,CAAC,GAAG4d,CAAC,EAAE,EAAE5d,CAAC,EAAE;MACrC;MACAuD,GAAG,GAAG,IAAI,CAACwiB,KAAK,CAAC,CAAC,CAAC+D,OAAO,CAAC,KAAK,CAAC,CAAC3pB,OAAO,CAAC,CAAC;MAE3C,IAAI0tB,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK,IAAIxqB,GAAG,IAAIA,GAAG,IAAIsqB,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,EAAE;QAC9C,OAAOH,IAAI,CAAC7tB,CAAC,CAAC,CAACgc,IAAI;MACvB;MACA,IAAI6R,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,IAAIzqB,GAAG,IAAIA,GAAG,IAAIsqB,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK,EAAE;QAC9C,OAAOF,IAAI,CAAC7tB,CAAC,CAAC,CAACgc,IAAI;MACvB;IACJ;IAEA,OAAO,EAAE;EACb;EAEA,SAAS0S,UAAUA,CAAA,EAAG;IAClB,IAAI1uB,CAAC;MACD4d,CAAC;MACD0Q,GAAG;MACH/qB,GAAG;MACHsqB,IAAI,GAAG,IAAI,CAACzlB,UAAU,CAAC,CAAC,CAACylB,IAAI,CAAC,CAAC;IACnC,KAAK7tB,CAAC,GAAG,CAAC,EAAE4d,CAAC,GAAGiQ,IAAI,CAACvuB,MAAM,EAAEU,CAAC,GAAG4d,CAAC,EAAE,EAAE5d,CAAC,EAAE;MACrCsuB,GAAG,GAAGT,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK,IAAIF,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;;MAE9C;MACAzqB,GAAG,GAAG,IAAI,CAACwiB,KAAK,CAAC,CAAC,CAAC+D,OAAO,CAAC,KAAK,CAAC,CAAC3pB,OAAO,CAAC,CAAC;MAE3C,IACK0tB,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK,IAAIxqB,GAAG,IAAIA,GAAG,IAAIsqB,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,IAC5CH,IAAI,CAAC7tB,CAAC,CAAC,CAACguB,KAAK,IAAIzqB,GAAG,IAAIA,GAAG,IAAIsqB,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAM,EAChD;QACE,OACI,CAAC,IAAI,CAACnhB,IAAI,CAAC,CAAC,GAAG1O,KAAK,CAAC2vB,IAAI,CAAC7tB,CAAC,CAAC,CAAC+tB,KAAK,CAAC,CAACnhB,IAAI,CAAC,CAAC,IAAI0hB,GAAG,GACjDT,IAAI,CAAC7tB,CAAC,CAAC,CAAColB,MAAM;MAEtB;IACJ;IAEA,OAAO,IAAI,CAACxY,IAAI,CAAC,CAAC;EACtB;EAEA,SAAS+hB,aAAaA,CAACvf,QAAQ,EAAE;IAC7B,IAAI,CAACrQ,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC,EAAE;MACrC6vB,gBAAgB,CAAC/vB,IAAI,CAAC,IAAI,CAAC;IAC/B;IACA,OAAOuQ,QAAQ,GAAG,IAAI,CAACyf,cAAc,GAAG,IAAI,CAACC,UAAU;EAC3D;EAEA,SAASC,aAAaA,CAAC3f,QAAQ,EAAE;IAC7B,IAAI,CAACrQ,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC,EAAE;MACrC6vB,gBAAgB,CAAC/vB,IAAI,CAAC,IAAI,CAAC;IAC/B;IACA,OAAOuQ,QAAQ,GAAG,IAAI,CAAC4f,cAAc,GAAG,IAAI,CAACF,UAAU;EAC3D;EAEA,SAASG,eAAeA,CAAC7f,QAAQ,EAAE;IAC/B,IAAI,CAACrQ,UAAU,CAAC,IAAI,EAAE,kBAAkB,CAAC,EAAE;MACvC6vB,gBAAgB,CAAC/vB,IAAI,CAAC,IAAI,CAAC;IAC/B;IACA,OAAOuQ,QAAQ,GAAG,IAAI,CAAC8f,gBAAgB,GAAG,IAAI,CAACJ,UAAU;EAC7D;EAEA,SAASzB,YAAYA,CAACje,QAAQ,EAAE9O,MAAM,EAAE;IACpC,OAAOA,MAAM,CAACyuB,aAAa,CAAC3f,QAAQ,CAAC;EACzC;EAEA,SAASke,YAAYA,CAACle,QAAQ,EAAE9O,MAAM,EAAE;IACpC,OAAOA,MAAM,CAACquB,aAAa,CAACvf,QAAQ,CAAC;EACzC;EAEA,SAASme,cAAcA,CAACne,QAAQ,EAAE9O,MAAM,EAAE;IACtC,OAAOA,MAAM,CAAC2uB,eAAe,CAAC7f,QAAQ,CAAC;EAC3C;EAEA,SAASqe,mBAAmBA,CAACre,QAAQ,EAAE9O,MAAM,EAAE;IAC3C,OAAOA,MAAM,CAACotB,oBAAoB,IAAIhf,aAAa;EACvD;EAEA,SAASkgB,gBAAgBA,CAAA,EAAG;IACxB,IAAIO,UAAU,GAAG,EAAE;MACfC,UAAU,GAAG,EAAE;MACfC,YAAY,GAAG,EAAE;MACjB5b,WAAW,GAAG,EAAE;MAChBzT,CAAC;MACD4d,CAAC;MACDiQ,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;IAEtB,KAAK7tB,CAAC,GAAG,CAAC,EAAE4d,CAAC,GAAGiQ,IAAI,CAACvuB,MAAM,EAAEU,CAAC,GAAG4d,CAAC,EAAE,EAAE5d,CAAC,EAAE;MACrCovB,UAAU,CAACnvB,IAAI,CAACsP,WAAW,CAACse,IAAI,CAAC7tB,CAAC,CAAC,CAACoF,IAAI,CAAC,CAAC;MAC1C+pB,UAAU,CAAClvB,IAAI,CAACsP,WAAW,CAACse,IAAI,CAAC7tB,CAAC,CAAC,CAACgc,IAAI,CAAC,CAAC;MAC1CqT,YAAY,CAACpvB,IAAI,CAACsP,WAAW,CAACse,IAAI,CAAC7tB,CAAC,CAAC,CAACouB,MAAM,CAAC,CAAC;MAE9C3a,WAAW,CAACxT,IAAI,CAACsP,WAAW,CAACse,IAAI,CAAC7tB,CAAC,CAAC,CAACoF,IAAI,CAAC,CAAC;MAC3CqO,WAAW,CAACxT,IAAI,CAACsP,WAAW,CAACse,IAAI,CAAC7tB,CAAC,CAAC,CAACgc,IAAI,CAAC,CAAC;MAC3CvI,WAAW,CAACxT,IAAI,CAACsP,WAAW,CAACse,IAAI,CAAC7tB,CAAC,CAAC,CAACouB,MAAM,CAAC,CAAC;IACjD;IAEA,IAAI,CAACU,UAAU,GAAG,IAAIppB,MAAM,CAAC,IAAI,GAAG+N,WAAW,CAAC1O,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC;IACrE,IAAI,CAAC8pB,cAAc,GAAG,IAAInpB,MAAM,CAAC,IAAI,GAAG0pB,UAAU,CAACrqB,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC;IACxE,IAAI,CAACiqB,cAAc,GAAG,IAAItpB,MAAM,CAAC,IAAI,GAAGypB,UAAU,CAACpqB,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC;IACxE,IAAI,CAACmqB,gBAAgB,GAAG,IAAIxpB,MAAM,CAC9B,IAAI,GAAG2pB,YAAY,CAACtqB,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EACnC,GACJ,CAAC;EACL;;EAEA;;EAEAgD,cAAc,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY;IACxC,OAAO,IAAI,CAAC8Y,QAAQ,CAAC,CAAC,GAAG,GAAG;EAChC,CAAC,CAAC;EAEF9Y,cAAc,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY;IACxC,OAAO,IAAI,CAACunB,WAAW,CAAC,CAAC,GAAG,GAAG;EACnC,CAAC,CAAC;EAEF,SAASC,sBAAsBA,CAACvnB,KAAK,EAAEwnB,MAAM,EAAE;IAC3CznB,cAAc,CAAC,CAAC,EAAE,CAACC,KAAK,EAAEA,KAAK,CAAC1I,MAAM,CAAC,EAAE,CAAC,EAAEkwB,MAAM,CAAC;EACvD;EAEAD,sBAAsB,CAAC,MAAM,EAAE,UAAU,CAAC;EAC1CA,sBAAsB,CAAC,OAAO,EAAE,UAAU,CAAC;EAC3CA,sBAAsB,CAAC,MAAM,EAAE,aAAa,CAAC;EAC7CA,sBAAsB,CAAC,OAAO,EAAE,aAAa,CAAC;;EAE9C;;EAEA9jB,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;EAC9BA,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC;;EAEjC;;EAEAY,eAAe,CAAC,UAAU,EAAE,CAAC,CAAC;EAC9BA,eAAe,CAAC,aAAa,EAAE,CAAC,CAAC;;EAEjC;;EAEA4C,aAAa,CAAC,GAAG,EAAEN,WAAW,CAAC;EAC/BM,aAAa,CAAC,GAAG,EAAEN,WAAW,CAAC;EAC/BM,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EACtCiB,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EACtCiB,aAAa,CAAC,MAAM,EAAET,SAAS,EAAEN,MAAM,CAAC;EACxCe,aAAa,CAAC,MAAM,EAAET,SAAS,EAAEN,MAAM,CAAC;EACxCe,aAAa,CAAC,OAAO,EAAER,SAAS,EAAEN,MAAM,CAAC;EACzCc,aAAa,CAAC,OAAO,EAAER,SAAS,EAAEN,MAAM,CAAC;EAEzC4B,iBAAiB,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,UAClDvR,KAAK,EACLsW,IAAI,EACJ7Q,MAAM,EACN+D,KAAK,EACP;IACE8M,IAAI,CAAC9M,KAAK,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGsF,KAAK,CAACxO,KAAK,CAAC;EAC3C,CAAC,CAAC;EAEFuR,iBAAiB,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,UAAUvR,KAAK,EAAEsW,IAAI,EAAE7Q,MAAM,EAAE+D,KAAK,EAAE;IAClE8M,IAAI,CAAC9M,KAAK,CAAC,GAAG9J,KAAK,CAACwV,iBAAiB,CAAClV,KAAK,CAAC;EAChD,CAAC,CAAC;;EAEF;;EAEA,SAASixB,cAAcA,CAACjxB,KAAK,EAAE;IAC3B,OAAOkxB,oBAAoB,CAAC7wB,IAAI,CAC5B,IAAI,EACJL,KAAK,EACL,IAAI,CAACsW,IAAI,CAAC,CAAC,EACX,IAAI,CAACC,OAAO,CAAC,CAAC,EACd,IAAI,CAAC3M,UAAU,CAAC,CAAC,CAACsN,KAAK,CAAClB,GAAG,EAC3B,IAAI,CAACpM,UAAU,CAAC,CAAC,CAACsN,KAAK,CAACjB,GAC5B,CAAC;EACL;EAEA,SAASkb,iBAAiBA,CAACnxB,KAAK,EAAE;IAC9B,OAAOkxB,oBAAoB,CAAC7wB,IAAI,CAC5B,IAAI,EACJL,KAAK,EACL,IAAI,CAAC4lB,OAAO,CAAC,CAAC,EACd,IAAI,CAACyI,UAAU,CAAC,CAAC,EACjB,CAAC,EACD,CACJ,CAAC;EACL;EAEA,SAAS+C,iBAAiBA,CAAA,EAAG;IACzB,OAAOra,WAAW,CAAC,IAAI,CAAC3I,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC;EAEA,SAASijB,wBAAwBA,CAAA,EAAG;IAChC,OAAOta,WAAW,CAAC,IAAI,CAAC+Z,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChD;EAEA,SAASQ,cAAcA,CAAA,EAAG;IACtB,IAAIC,QAAQ,GAAG,IAAI,CAAC3nB,UAAU,CAAC,CAAC,CAACsN,KAAK;IACtC,OAAOH,WAAW,CAAC,IAAI,CAAC3I,IAAI,CAAC,CAAC,EAAEmjB,QAAQ,CAACvb,GAAG,EAAEub,QAAQ,CAACtb,GAAG,CAAC;EAC/D;EAEA,SAASub,kBAAkBA,CAAA,EAAG;IAC1B,IAAID,QAAQ,GAAG,IAAI,CAAC3nB,UAAU,CAAC,CAAC,CAACsN,KAAK;IACtC,OAAOH,WAAW,CAAC,IAAI,CAACsL,QAAQ,CAAC,CAAC,EAAEkP,QAAQ,CAACvb,GAAG,EAAEub,QAAQ,CAACtb,GAAG,CAAC;EACnE;EAEA,SAASib,oBAAoBA,CAAClxB,KAAK,EAAEsW,IAAI,EAAEC,OAAO,EAAEP,GAAG,EAAEC,GAAG,EAAE;IAC1D,IAAIwb,WAAW;IACf,IAAIzxB,KAAK,IAAI,IAAI,EAAE;MACf,OAAO6W,UAAU,CAAC,IAAI,EAAEb,GAAG,EAAEC,GAAG,CAAC,CAAC7H,IAAI;IAC1C,CAAC,MAAM;MACHqjB,WAAW,GAAG1a,WAAW,CAAC/W,KAAK,EAAEgW,GAAG,EAAEC,GAAG,CAAC;MAC1C,IAAIK,IAAI,GAAGmb,WAAW,EAAE;QACpBnb,IAAI,GAAGmb,WAAW;MACtB;MACA,OAAOC,UAAU,CAACrxB,IAAI,CAAC,IAAI,EAAEL,KAAK,EAAEsW,IAAI,EAAEC,OAAO,EAAEP,GAAG,EAAEC,GAAG,CAAC;IAChE;EACJ;EAEA,SAASyb,UAAUA,CAACrP,QAAQ,EAAE/L,IAAI,EAAEC,OAAO,EAAEP,GAAG,EAAEC,GAAG,EAAE;IACnD,IAAI0b,aAAa,GAAGtb,kBAAkB,CAACgM,QAAQ,EAAE/L,IAAI,EAAEC,OAAO,EAAEP,GAAG,EAAEC,GAAG,CAAC;MACrE/G,IAAI,GAAGyG,aAAa,CAACgc,aAAa,CAACvjB,IAAI,EAAE,CAAC,EAAEujB,aAAa,CAACjb,SAAS,CAAC;IAExE,IAAI,CAACtI,IAAI,CAACc,IAAI,CAAC2G,cAAc,CAAC,CAAC,CAAC;IAChC,IAAI,CAAC5G,KAAK,CAACC,IAAI,CAACwS,WAAW,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACxS,IAAI,CAACA,IAAI,CAACyS,UAAU,CAAC,CAAC,CAAC;IAC5B,OAAO,IAAI;EACf;;EAEA;;EAEApY,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC;;EAEvC;;EAEA0D,YAAY,CAAC,SAAS,EAAE,GAAG,CAAC;;EAE5B;;EAEAY,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC;;EAE7B;;EAEA4C,aAAa,CAAC,GAAG,EAAElB,MAAM,CAAC;EAC1B+B,aAAa,CAAC,GAAG,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAE;IACvCA,KAAK,CAAC2H,KAAK,CAAC,GAAG,CAACpD,KAAK,CAACxO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;EACzC,CAAC,CAAC;;EAEF;;EAEA,SAAS4xB,aAAaA,CAAC5xB,KAAK,EAAE;IAC1B,OAAOA,KAAK,IAAI,IAAI,GACd4I,IAAI,CAAC0F,IAAI,CAAC,CAAC,IAAI,CAACW,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GACjC,IAAI,CAACA,KAAK,CAAC,CAACjP,KAAK,GAAG,CAAC,IAAI,CAAC,GAAI,IAAI,CAACiP,KAAK,CAAC,CAAC,GAAG,CAAE,CAAC;EAC1D;;EAEA;;EAEA1F,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC;;EAE5C;;EAEA0D,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;;EAEzB;EACAY,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;;EAE1B;;EAEA4C,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EACtCiB,aAAa,CAAC,IAAI,EAAE,UAAUG,QAAQ,EAAE9O,MAAM,EAAE;IAC5C;IACA,OAAO8O,QAAQ,GACT9O,MAAM,CAACqF,uBAAuB,IAAIrF,MAAM,CAACuF,aAAa,GACtDvF,MAAM,CAACmF,8BAA8B;EAC/C,CAAC,CAAC;EAEFqK,aAAa,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAEO,IAAI,CAAC;EAChCP,aAAa,CAAC,IAAI,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAE;IACxCA,KAAK,CAAC4H,IAAI,CAAC,GAAGrD,KAAK,CAACxO,KAAK,CAAC8J,KAAK,CAAC8F,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC,CAAC;;EAEF;;EAEA,IAAIiiB,gBAAgB,GAAGhjB,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC;;EAE/C;;EAEAtF,cAAc,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,CAAC;;EAEvD;;EAEA0D,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC;;EAEhC;EACAY,eAAe,CAAC,WAAW,EAAE,CAAC,CAAC;;EAE/B;;EAEA4C,aAAa,CAAC,KAAK,EAAEV,SAAS,CAAC;EAC/BU,aAAa,CAAC,MAAM,EAAEhB,MAAM,CAAC;EAC7B6B,aAAa,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE;IAC3DA,MAAM,CAAC0c,UAAU,GAAG3T,KAAK,CAACxO,KAAK,CAAC;EACpC,CAAC,CAAC;;EAEF;;EAEA;;EAEA,SAAS8xB,eAAeA,CAAC9xB,KAAK,EAAE;IAC5B,IAAI0W,SAAS,GACT9N,IAAI,CAACyd,KAAK,CACN,CAAC,IAAI,CAACkB,KAAK,CAAC,CAAC,CAAC+D,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC/D,KAAK,CAAC,CAAC,CAAC+D,OAAO,CAAC,MAAM,CAAC,IAAI,KACnE,CAAC,GAAG,CAAC;IACT,OAAOtrB,KAAK,IAAI,IAAI,GAAG0W,SAAS,GAAG,IAAI,CAACa,GAAG,CAACvX,KAAK,GAAG0W,SAAS,EAAE,GAAG,CAAC;EACvE;;EAEA;;EAEAnN,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;;EAE3C;;EAEA0D,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC;;EAE3B;;EAEAY,eAAe,CAAC,QAAQ,EAAE,EAAE,CAAC;;EAE7B;;EAEA4C,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EACtC8B,aAAa,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAES,MAAM,CAAC;;EAElC;;EAEA,IAAIggB,YAAY,GAAGljB,UAAU,CAAC,SAAS,EAAE,KAAK,CAAC;;EAE/C;;EAEAtF,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;;EAE3C;;EAEA0D,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC;;EAE3B;;EAEAY,eAAe,CAAC,QAAQ,EAAE,EAAE,CAAC;;EAE7B;;EAEA4C,aAAa,CAAC,GAAG,EAAEb,SAAS,CAAC;EAC7Ba,aAAa,CAAC,IAAI,EAAEb,SAAS,EAAEJ,MAAM,CAAC;EACtC8B,aAAa,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAEU,MAAM,CAAC;;EAElC;;EAEA,IAAIggB,YAAY,GAAGnjB,UAAU,CAAC,SAAS,EAAE,KAAK,CAAC;;EAE/C;;EAEAtF,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY;IAClC,OAAO,CAAC,EAAE,IAAI,CAAC6a,WAAW,CAAC,CAAC,GAAG,GAAG,CAAC;EACvC,CAAC,CAAC;EAEF7a,cAAc,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY;IACxC,OAAO,CAAC,EAAE,IAAI,CAAC6a,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC;EACtC,CAAC,CAAC;EAEF7a,cAAc,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC;EAC/CA,cAAc,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY;IAC1C,OAAO,IAAI,CAAC6a,WAAW,CAAC,CAAC,GAAG,EAAE;EAClC,CAAC,CAAC;EACF7a,cAAc,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY;IAC3C,OAAO,IAAI,CAAC6a,WAAW,CAAC,CAAC,GAAG,GAAG;EACnC,CAAC,CAAC;EACF7a,cAAc,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY;IAC5C,OAAO,IAAI,CAAC6a,WAAW,CAAC,CAAC,GAAG,IAAI;EACpC,CAAC,CAAC;EACF7a,cAAc,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY;IAC7C,OAAO,IAAI,CAAC6a,WAAW,CAAC,CAAC,GAAG,KAAK;EACrC,CAAC,CAAC;EACF7a,cAAc,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY;IAC9C,OAAO,IAAI,CAAC6a,WAAW,CAAC,CAAC,GAAG,MAAM;EACtC,CAAC,CAAC;EACF7a,cAAc,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY;IAC/C,OAAO,IAAI,CAAC6a,WAAW,CAAC,CAAC,GAAG,OAAO;EACvC,CAAC,CAAC;;EAEF;;EAEAnX,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC;;EAEjC;;EAEAY,eAAe,CAAC,aAAa,EAAE,EAAE,CAAC;;EAElC;;EAEA4C,aAAa,CAAC,GAAG,EAAEV,SAAS,EAAER,MAAM,CAAC;EACrCkB,aAAa,CAAC,IAAI,EAAEV,SAAS,EAAEP,MAAM,CAAC;EACtCiB,aAAa,CAAC,KAAK,EAAEV,SAAS,EAAEN,MAAM,CAAC;EAEvC,IAAIjG,KAAK,EAAEyoB,iBAAiB;EAC5B,KAAKzoB,KAAK,GAAG,MAAM,EAAEA,KAAK,CAAC1I,MAAM,IAAI,CAAC,EAAE0I,KAAK,IAAI,GAAG,EAAE;IAClDiH,aAAa,CAACjH,KAAK,EAAE0G,aAAa,CAAC;EACvC;EAEA,SAASgiB,OAAOA,CAAClyB,KAAK,EAAEiK,KAAK,EAAE;IAC3BA,KAAK,CAACgI,WAAW,CAAC,GAAGzD,KAAK,CAAC,CAAC,IAAI,GAAGxO,KAAK,IAAI,IAAI,CAAC;EACrD;EAEA,KAAKwJ,KAAK,GAAG,GAAG,EAAEA,KAAK,CAAC1I,MAAM,IAAI,CAAC,EAAE0I,KAAK,IAAI,GAAG,EAAE;IAC/C8H,aAAa,CAAC9H,KAAK,EAAE0oB,OAAO,CAAC;EACjC;EAEAD,iBAAiB,GAAGpjB,UAAU,CAAC,cAAc,EAAE,KAAK,CAAC;;EAErD;;EAEAtF,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC;EACrCA,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC;;EAEtC;;EAEA,SAAS4oB,WAAWA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC9sB,MAAM,GAAG,KAAK,GAAG,EAAE;EACnC;EAEA,SAAS+sB,WAAWA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC/sB,MAAM,GAAG,4BAA4B,GAAG,EAAE;EAC1D;EAEA,IAAIgtB,KAAK,GAAG7sB,MAAM,CAACrF,SAAS;EAE5BkyB,KAAK,CAAC9a,GAAG,GAAGA,GAAG;EACf8a,KAAK,CAACnqB,QAAQ,GAAGgjB,UAAU;EAC3BmH,KAAK,CAAC9K,KAAK,GAAGA,KAAK;EACnB8K,KAAK,CAACtlB,IAAI,GAAGA,IAAI;EACjBslB,KAAK,CAAC5G,KAAK,GAAGA,KAAK;EACnB4G,KAAK,CAACxwB,MAAM,GAAGA,MAAM;EACrBwwB,KAAK,CAACxtB,IAAI,GAAGA,IAAI;EACjBwtB,KAAK,CAAC9E,OAAO,GAAGA,OAAO;EACvB8E,KAAK,CAACztB,EAAE,GAAGA,EAAE;EACbytB,KAAK,CAAC7E,KAAK,GAAGA,KAAK;EACnB6E,KAAK,CAACrjB,GAAG,GAAGI,SAAS;EACrBijB,KAAK,CAAC1D,SAAS,GAAGA,SAAS;EAC3B0D,KAAK,CAAC3I,OAAO,GAAGA,OAAO;EACvB2I,KAAK,CAAC1I,QAAQ,GAAGA,QAAQ;EACzB0I,KAAK,CAAC3G,SAAS,GAAGA,SAAS;EAC3B2G,KAAK,CAACvG,MAAM,GAAGA,MAAM;EACrBuG,KAAK,CAACrG,aAAa,GAAGA,aAAa;EACnCqG,KAAK,CAACpG,cAAc,GAAGA,cAAc;EACrCoG,KAAK,CAAC3uB,OAAO,GAAG+qB,SAAS;EACzB4D,KAAK,CAAC3E,IAAI,GAAGA,IAAI;EACjB2E,KAAK,CAACvwB,MAAM,GAAGA,MAAM;EACrBuwB,KAAK,CAACzoB,UAAU,GAAGA,UAAU;EAC7ByoB,KAAK,CAACppB,GAAG,GAAG2b,YAAY;EACxByN,KAAK,CAAC/d,GAAG,GAAGoQ,YAAY;EACxB2N,KAAK,CAAC3D,YAAY,GAAGA,YAAY;EACjC2D,KAAK,CAACtrB,GAAG,GAAGsI,SAAS;EACrBgjB,KAAK,CAAC/G,OAAO,GAAGA,OAAO;EACvB+G,KAAK,CAAChK,QAAQ,GAAGA,QAAQ;EACzBgK,KAAK,CAACzJ,OAAO,GAAGA,OAAO;EACvByJ,KAAK,CAAC9D,QAAQ,GAAGA,QAAQ;EACzB8D,KAAK,CAACvF,MAAM,GAAGA,MAAM;EACrBuF,KAAK,CAACzF,WAAW,GAAGA,WAAW;EAC/ByF,KAAK,CAACtF,OAAO,GAAGA,OAAO;EACvB,IAAI,OAAOuF,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,GAAG,IAAI,IAAI,EAAE;IACrDF,KAAK,CAACC,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAAG,YAAY;MAC1D,OAAO,SAAS,GAAG,IAAI,CAAC1wB,MAAM,CAAC,CAAC,GAAG,GAAG;IAC1C,CAAC;EACL;EACAwwB,KAAK,CAAC7D,MAAM,GAAGA,MAAM;EACrB6D,KAAK,CAACjyB,QAAQ,GAAGA,QAAQ;EACzBiyB,KAAK,CAAC/D,IAAI,GAAGA,IAAI;EACjB+D,KAAK,CAAC1wB,OAAO,GAAGA,OAAO;EACvB0wB,KAAK,CAACzD,YAAY,GAAGA,YAAY;EACjCyD,KAAK,CAAC1C,OAAO,GAAGI,UAAU;EAC1BsC,KAAK,CAACG,SAAS,GAAGxC,YAAY;EAC9BqC,KAAK,CAACI,OAAO,GAAGxC,UAAU;EAC1BoC,KAAK,CAACK,OAAO,GAAGxC,UAAU;EAC1BmC,KAAK,CAACjkB,IAAI,GAAGiH,UAAU;EACvBgd,KAAK,CAAClkB,UAAU,GAAGmH,aAAa;EAChC+c,KAAK,CAAChQ,QAAQ,GAAG4O,cAAc;EAC/BoB,KAAK,CAACvB,WAAW,GAAGK,iBAAiB;EACrCkB,KAAK,CAAC3M,OAAO,GAAG2M,KAAK,CAAC5M,QAAQ,GAAGmM,aAAa;EAC9CS,KAAK,CAACpjB,KAAK,GAAGsF,WAAW;EACzB8d,KAAK,CAACljB,WAAW,GAAGqF,cAAc;EAClC6d,KAAK,CAAC/b,IAAI,GAAG+b,KAAK,CAAC1M,KAAK,GAAGrO,UAAU;EACrC+a,KAAK,CAACzM,OAAO,GAAGyM,KAAK,CAACM,QAAQ,GAAGnb,aAAa;EAC9C6a,KAAK,CAACtb,WAAW,GAAGua,cAAc;EAClCe,KAAK,CAACO,eAAe,GAAGpB,kBAAkB;EAC1Ca,KAAK,CAACQ,cAAc,GAAGzB,iBAAiB;EACxCiB,KAAK,CAACS,qBAAqB,GAAGzB,wBAAwB;EACtDgB,KAAK,CAACnjB,IAAI,GAAG2iB,gBAAgB;EAC7BQ,KAAK,CAACxZ,GAAG,GAAGwZ,KAAK,CAACxM,IAAI,GAAGnM,eAAe;EACxC2Y,KAAK,CAAC9b,OAAO,GAAGqD,qBAAqB;EACrCyY,KAAK,CAAChE,UAAU,GAAGxU,kBAAkB;EACrCwY,KAAK,CAAC3b,SAAS,GAAGob,eAAe;EACjCO,KAAK,CAAChP,IAAI,GAAGgP,KAAK,CAAC3X,KAAK,GAAGiB,UAAU;EACrC0W,KAAK,CAACnO,MAAM,GAAGmO,KAAK,CAACzX,OAAO,GAAGmX,YAAY;EAC3CM,KAAK,CAAClO,MAAM,GAAGkO,KAAK,CAACxX,OAAO,GAAGmX,YAAY;EAC3CK,KAAK,CAACjO,WAAW,GAAGiO,KAAK,CAACvM,YAAY,GAAGmM,iBAAiB;EAC1DI,KAAK,CAACvL,SAAS,GAAGc,YAAY;EAC9ByK,KAAK,CAACpwB,GAAG,GAAGkmB,cAAc;EAC1BkK,KAAK,CAAC5K,KAAK,GAAGW,gBAAgB;EAC9BiK,KAAK,CAACU,SAAS,GAAGzK,uBAAuB;EACzC+J,KAAK,CAAC7J,oBAAoB,GAAGA,oBAAoB;EACjD6J,KAAK,CAACW,KAAK,GAAGvK,oBAAoB;EAClC4J,KAAK,CAACxJ,OAAO,GAAGA,OAAO;EACvBwJ,KAAK,CAACvJ,WAAW,GAAGA,WAAW;EAC/BuJ,KAAK,CAACtJ,KAAK,GAAGA,KAAK;EACnBsJ,KAAK,CAAC5N,KAAK,GAAGsE,KAAK;EACnBsJ,KAAK,CAACY,QAAQ,GAAGd,WAAW;EAC5BE,KAAK,CAACa,QAAQ,GAAGd,WAAW;EAC5BC,KAAK,CAACc,KAAK,GAAGntB,SAAS,CACnB,iDAAiD,EACjD6rB,gBACJ,CAAC;EACDQ,KAAK,CAAC1f,MAAM,GAAG3M,SAAS,CACpB,kDAAkD,EAClDuO,WACJ,CAAC;EACD8d,KAAK,CAAC7M,KAAK,GAAGxf,SAAS,CACnB,gDAAgD,EAChDqP,UACJ,CAAC;EACDgd,KAAK,CAACrF,IAAI,GAAGhnB,SAAS,CAClB,0GAA0G,EAC1GkiB,UACJ,CAAC;EACDmK,KAAK,CAACe,YAAY,GAAGptB,SAAS,CAC1B,yGAAyG,EACzG0iB,2BACJ,CAAC;EAED,SAAS2K,UAAUA,CAACrzB,KAAK,EAAE;IACvB,OAAO4iB,WAAW,CAAC5iB,KAAK,GAAG,IAAI,CAAC;EACpC;EAEA,SAASszB,YAAYA,CAAA,EAAG;IACpB,OAAO1Q,WAAW,CAACjjB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAACmzB,SAAS,CAAC,CAAC;EACzD;EAEA,SAASQ,kBAAkBA,CAAC5mB,MAAM,EAAE;IAChC,OAAOA,MAAM;EACjB;EAEA,IAAI6mB,OAAO,GAAG/rB,MAAM,CAACtH,SAAS;EAE9BqzB,OAAO,CAACtrB,QAAQ,GAAGA,QAAQ;EAC3BsrB,OAAO,CAAClpB,cAAc,GAAGA,cAAc;EACvCkpB,OAAO,CAACrpB,WAAW,GAAGA,WAAW;EACjCqpB,OAAO,CAAC9pB,OAAO,GAAGA,OAAO;EACzB8pB,OAAO,CAACjP,QAAQ,GAAGgP,kBAAkB;EACrCC,OAAO,CAACnG,UAAU,GAAGkG,kBAAkB;EACvCC,OAAO,CAAC/mB,YAAY,GAAGA,YAAY;EACnC+mB,OAAO,CAAC1mB,UAAU,GAAGA,UAAU;EAC/B0mB,OAAO,CAACzsB,GAAG,GAAGA,GAAG;EACjBysB,OAAO,CAACnE,IAAI,GAAGD,UAAU;EACzBoE,OAAO,CAACxE,SAAS,GAAGU,eAAe;EACnC8D,OAAO,CAACpQ,eAAe,GAAGyM,qBAAqB;EAC/C2D,OAAO,CAACjD,aAAa,GAAGA,aAAa;EACrCiD,OAAO,CAACrD,aAAa,GAAGA,aAAa;EACrCqD,OAAO,CAAC/C,eAAe,GAAGA,eAAe;EAEzC+C,OAAO,CAAC7gB,MAAM,GAAGU,YAAY;EAC7BmgB,OAAO,CAAC9gB,WAAW,GAAGc,iBAAiB;EACvCggB,OAAO,CAAC1gB,WAAW,GAAGoB,iBAAiB;EACvCsf,OAAO,CAAC3gB,WAAW,GAAGA,WAAW;EACjC2gB,OAAO,CAAC5gB,gBAAgB,GAAGA,gBAAgB;EAC3C4gB,OAAO,CAACld,IAAI,GAAGW,UAAU;EACzBuc,OAAO,CAACC,cAAc,GAAGpc,oBAAoB;EAC7Cmc,OAAO,CAACE,cAAc,GAAGtc,oBAAoB;EAE7Coc,OAAO,CAAC7b,QAAQ,GAAGgB,cAAc;EACjC6a,OAAO,CAAC/b,WAAW,GAAGuB,iBAAiB;EACvCwa,OAAO,CAAC9b,aAAa,GAAGoB,mBAAmB;EAC3C0a,OAAO,CAACzb,aAAa,GAAGwB,mBAAmB;EAE3Cia,OAAO,CAAC1b,aAAa,GAAGA,aAAa;EACrC0b,OAAO,CAAC3b,kBAAkB,GAAGA,kBAAkB;EAC/C2b,OAAO,CAAC5b,gBAAgB,GAAGA,gBAAgB;EAE3C4b,OAAO,CAACrY,IAAI,GAAGK,UAAU;EACzBgY,OAAO,CAACxwB,QAAQ,GAAG4Y,cAAc;EAEjC,SAAS+X,KAAKA,CAAC9xB,MAAM,EAAE+xB,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAE;IACzC,IAAIhyB,MAAM,GAAGwb,SAAS,CAAC,CAAC;MACpBrb,GAAG,GAAGL,SAAS,CAAC,CAAC,CAACmF,GAAG,CAAC+sB,MAAM,EAAEF,KAAK,CAAC;IACxC,OAAO9xB,MAAM,CAAC+xB,KAAK,CAAC,CAAC5xB,GAAG,EAAEJ,MAAM,CAAC;EACrC;EAEA,SAASkyB,cAAcA,CAAClyB,MAAM,EAAE+xB,KAAK,EAAEC,KAAK,EAAE;IAC1C,IAAI5yB,QAAQ,CAACY,MAAM,CAAC,EAAE;MAClB+xB,KAAK,GAAG/xB,MAAM;MACdA,MAAM,GAAGwC,SAAS;IACtB;IAEAxC,MAAM,GAAGA,MAAM,IAAI,EAAE;IAErB,IAAI+xB,KAAK,IAAI,IAAI,EAAE;MACf,OAAOD,KAAK,CAAC9xB,MAAM,EAAE+xB,KAAK,EAAEC,KAAK,EAAE,OAAO,CAAC;IAC/C;IAEA,IAAIryB,CAAC;MACDwyB,GAAG,GAAG,EAAE;IACZ,KAAKxyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACrBwyB,GAAG,CAACxyB,CAAC,CAAC,GAAGmyB,KAAK,CAAC9xB,MAAM,EAAEL,CAAC,EAAEqyB,KAAK,EAAE,OAAO,CAAC;IAC7C;IACA,OAAOG,GAAG;EACd;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASC,gBAAgBA,CAACC,YAAY,EAAEryB,MAAM,EAAE+xB,KAAK,EAAEC,KAAK,EAAE;IAC1D,IAAI,OAAOK,YAAY,KAAK,SAAS,EAAE;MACnC,IAAIjzB,QAAQ,CAACY,MAAM,CAAC,EAAE;QAClB+xB,KAAK,GAAG/xB,MAAM;QACdA,MAAM,GAAGwC,SAAS;MACtB;MAEAxC,MAAM,GAAGA,MAAM,IAAI,EAAE;IACzB,CAAC,MAAM;MACHA,MAAM,GAAGqyB,YAAY;MACrBN,KAAK,GAAG/xB,MAAM;MACdqyB,YAAY,GAAG,KAAK;MAEpB,IAAIjzB,QAAQ,CAACY,MAAM,CAAC,EAAE;QAClB+xB,KAAK,GAAG/xB,MAAM;QACdA,MAAM,GAAGwC,SAAS;MACtB;MAEAxC,MAAM,GAAGA,MAAM,IAAI,EAAE;IACzB;IAEA,IAAIC,MAAM,GAAGwb,SAAS,CAAC,CAAC;MACpB6W,KAAK,GAAGD,YAAY,GAAGpyB,MAAM,CAACoV,KAAK,CAAClB,GAAG,GAAG,CAAC;MAC3CxU,CAAC;MACDwyB,GAAG,GAAG,EAAE;IAEZ,IAAIJ,KAAK,IAAI,IAAI,EAAE;MACf,OAAOD,KAAK,CAAC9xB,MAAM,EAAE,CAAC+xB,KAAK,GAAGO,KAAK,IAAI,CAAC,EAAEN,KAAK,EAAE,KAAK,CAAC;IAC3D;IAEA,KAAKryB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpBwyB,GAAG,CAACxyB,CAAC,CAAC,GAAGmyB,KAAK,CAAC9xB,MAAM,EAAE,CAACL,CAAC,GAAG2yB,KAAK,IAAI,CAAC,EAAEN,KAAK,EAAE,KAAK,CAAC;IACzD;IACA,OAAOG,GAAG;EACd;EAEA,SAASI,UAAUA,CAACvyB,MAAM,EAAE+xB,KAAK,EAAE;IAC/B,OAAOG,cAAc,CAAClyB,MAAM,EAAE+xB,KAAK,EAAE,QAAQ,CAAC;EAClD;EAEA,SAASS,eAAeA,CAACxyB,MAAM,EAAE+xB,KAAK,EAAE;IACpC,OAAOG,cAAc,CAAClyB,MAAM,EAAE+xB,KAAK,EAAE,aAAa,CAAC;EACvD;EAEA,SAASU,YAAYA,CAACJ,YAAY,EAAEryB,MAAM,EAAE+xB,KAAK,EAAE;IAC/C,OAAOK,gBAAgB,CAACC,YAAY,EAAEryB,MAAM,EAAE+xB,KAAK,EAAE,UAAU,CAAC;EACpE;EAEA,SAASW,iBAAiBA,CAACL,YAAY,EAAEryB,MAAM,EAAE+xB,KAAK,EAAE;IACpD,OAAOK,gBAAgB,CAACC,YAAY,EAAEryB,MAAM,EAAE+xB,KAAK,EAAE,eAAe,CAAC;EACzE;EAEA,SAASY,eAAeA,CAACN,YAAY,EAAEryB,MAAM,EAAE+xB,KAAK,EAAE;IAClD,OAAOK,gBAAgB,CAACC,YAAY,EAAEryB,MAAM,EAAE+xB,KAAK,EAAE,aAAa,CAAC;EACvE;EAEA1W,kBAAkB,CAAC,IAAI,EAAE;IACrBmS,IAAI,EAAE,CACF;MACIE,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,CAACC,QAAQ;MAChB7I,MAAM,EAAE,CAAC;MACThgB,IAAI,EAAE,aAAa;MACnBgpB,MAAM,EAAE,IAAI;MACZpS,IAAI,EAAE;IACV,CAAC,EACD;MACI+R,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,CAACC,QAAQ;MAChB7I,MAAM,EAAE,CAAC;MACThgB,IAAI,EAAE,eAAe;MACrBgpB,MAAM,EAAE,IAAI;MACZpS,IAAI,EAAE;IACV,CAAC,CACJ;IACDzB,sBAAsB,EAAE,sBAAsB;IAC9CrS,OAAO,EAAE,SAAAA,CAAUlB,MAAM,EAAE;MACvB,IAAI/H,CAAC,GAAG+H,MAAM,GAAG,EAAE;QACfH,MAAM,GACFmG,KAAK,CAAEhG,MAAM,GAAG,GAAG,GAAI,EAAE,CAAC,KAAK,CAAC,GAC1B,IAAI,GACJ/H,CAAC,KAAK,CAAC,GACP,IAAI,GACJA,CAAC,KAAK,CAAC,GACP,IAAI,GACJA,CAAC,KAAK,CAAC,GACP,IAAI,GACJ,IAAI;MAClB,OAAO+H,MAAM,GAAGH,MAAM;IAC1B;EACJ,CAAC,CAAC;;EAEF;;EAEA3I,KAAK,CAACguB,IAAI,GAAG1nB,SAAS,CAClB,uDAAuD,EACvDkX,kBACJ,CAAC;EACDxd,KAAK,CAAC+0B,QAAQ,GAAGzuB,SAAS,CACtB,+DAA+D,EAC/DsX,SACJ,CAAC;EAED,IAAIoX,OAAO,GAAG9rB,IAAI,CAACC,GAAG;EAEtB,SAASA,GAAGA,CAAA,EAAG;IACX,IAAIwU,IAAI,GAAG,IAAI,CAAC4I,KAAK;IAErB,IAAI,CAACF,aAAa,GAAG2O,OAAO,CAAC,IAAI,CAAC3O,aAAa,CAAC;IAChD,IAAI,CAACC,KAAK,GAAG0O,OAAO,CAAC,IAAI,CAAC1O,KAAK,CAAC;IAChC,IAAI,CAAC1S,OAAO,GAAGohB,OAAO,CAAC,IAAI,CAACphB,OAAO,CAAC;IAEpC+J,IAAI,CAACyI,YAAY,GAAG4O,OAAO,CAACrX,IAAI,CAACyI,YAAY,CAAC;IAC9CzI,IAAI,CAACxC,OAAO,GAAG6Z,OAAO,CAACrX,IAAI,CAACxC,OAAO,CAAC;IACpCwC,IAAI,CAACzC,OAAO,GAAG8Z,OAAO,CAACrX,IAAI,CAACzC,OAAO,CAAC;IACpCyC,IAAI,CAAC3C,KAAK,GAAGga,OAAO,CAACrX,IAAI,CAAC3C,KAAK,CAAC;IAChC2C,IAAI,CAAC1K,MAAM,GAAG+hB,OAAO,CAACrX,IAAI,CAAC1K,MAAM,CAAC;IAClC0K,IAAI,CAACmI,KAAK,GAAGkP,OAAO,CAACrX,IAAI,CAACmI,KAAK,CAAC;IAEhC,OAAO,IAAI;EACf;EAEA,SAASmP,aAAaA,CAACpP,QAAQ,EAAEvlB,KAAK,EAAE2O,KAAK,EAAEkb,SAAS,EAAE;IACtD,IAAIlF,KAAK,GAAGU,cAAc,CAACrlB,KAAK,EAAE2O,KAAK,CAAC;IAExC4W,QAAQ,CAACQ,aAAa,IAAI8D,SAAS,GAAGlF,KAAK,CAACoB,aAAa;IACzDR,QAAQ,CAACS,KAAK,IAAI6D,SAAS,GAAGlF,KAAK,CAACqB,KAAK;IACzCT,QAAQ,CAACjS,OAAO,IAAIuW,SAAS,GAAGlF,KAAK,CAACrR,OAAO;IAE7C,OAAOiS,QAAQ,CAACW,OAAO,CAAC,CAAC;EAC7B;;EAEA;EACA,SAAS0O,KAAKA,CAAC50B,KAAK,EAAE2O,KAAK,EAAE;IACzB,OAAOgmB,aAAa,CAAC,IAAI,EAAE30B,KAAK,EAAE2O,KAAK,EAAE,CAAC,CAAC;EAC/C;;EAEA;EACA,SAASkmB,UAAUA,CAAC70B,KAAK,EAAE2O,KAAK,EAAE;IAC9B,OAAOgmB,aAAa,CAAC,IAAI,EAAE30B,KAAK,EAAE2O,KAAK,EAAE,CAAC,CAAC,CAAC;EAChD;EAEA,SAASmmB,OAAOA,CAACtsB,MAAM,EAAE;IACrB,IAAIA,MAAM,GAAG,CAAC,EAAE;MACZ,OAAOI,IAAI,CAAC2F,KAAK,CAAC/F,MAAM,CAAC;IAC7B,CAAC,MAAM;MACH,OAAOI,IAAI,CAAC0F,IAAI,CAAC9F,MAAM,CAAC;IAC5B;EACJ;EAEA,SAASusB,MAAMA,CAAA,EAAG;IACd,IAAIjP,YAAY,GAAG,IAAI,CAACC,aAAa;MACjCF,IAAI,GAAG,IAAI,CAACG,KAAK;MACjBrT,MAAM,GAAG,IAAI,CAACW,OAAO;MACrB+J,IAAI,GAAG,IAAI,CAAC4I,KAAK;MACjBpL,OAAO;MACPD,OAAO;MACPF,KAAK;MACL8K,KAAK;MACLwP,cAAc;;IAElB;IACA;IACA,IACI,EACKlP,YAAY,IAAI,CAAC,IAAID,IAAI,IAAI,CAAC,IAAIlT,MAAM,IAAI,CAAC,IAC7CmT,YAAY,IAAI,CAAC,IAAID,IAAI,IAAI,CAAC,IAAIlT,MAAM,IAAI,CAAE,CAClD,EACH;MACEmT,YAAY,IAAIgP,OAAO,CAACG,YAAY,CAACtiB,MAAM,CAAC,GAAGkT,IAAI,CAAC,GAAG,KAAK;MAC5DA,IAAI,GAAG,CAAC;MACRlT,MAAM,GAAG,CAAC;IACd;;IAEA;IACA;IACA0K,IAAI,CAACyI,YAAY,GAAGA,YAAY,GAAG,IAAI;IAEvCjL,OAAO,GAAGxM,QAAQ,CAACyX,YAAY,GAAG,IAAI,CAAC;IACvCzI,IAAI,CAACxC,OAAO,GAAGA,OAAO,GAAG,EAAE;IAE3BD,OAAO,GAAGvM,QAAQ,CAACwM,OAAO,GAAG,EAAE,CAAC;IAChCwC,IAAI,CAACzC,OAAO,GAAGA,OAAO,GAAG,EAAE;IAE3BF,KAAK,GAAGrM,QAAQ,CAACuM,OAAO,GAAG,EAAE,CAAC;IAC9ByC,IAAI,CAAC3C,KAAK,GAAGA,KAAK,GAAG,EAAE;IAEvBmL,IAAI,IAAIxX,QAAQ,CAACqM,KAAK,GAAG,EAAE,CAAC;;IAE5B;IACAsa,cAAc,GAAG3mB,QAAQ,CAAC6mB,YAAY,CAACrP,IAAI,CAAC,CAAC;IAC7ClT,MAAM,IAAIqiB,cAAc;IACxBnP,IAAI,IAAIiP,OAAO,CAACG,YAAY,CAACD,cAAc,CAAC,CAAC;;IAE7C;IACAxP,KAAK,GAAGnX,QAAQ,CAACsE,MAAM,GAAG,EAAE,CAAC;IAC7BA,MAAM,IAAI,EAAE;IAEZ0K,IAAI,CAACwI,IAAI,GAAGA,IAAI;IAChBxI,IAAI,CAAC1K,MAAM,GAAGA,MAAM;IACpB0K,IAAI,CAACmI,KAAK,GAAGA,KAAK;IAElB,OAAO,IAAI;EACf;EAEA,SAAS0P,YAAYA,CAACrP,IAAI,EAAE;IACxB;IACA;IACA,OAAQA,IAAI,GAAG,IAAI,GAAI,MAAM;EACjC;EAEA,SAASoP,YAAYA,CAACtiB,MAAM,EAAE;IAC1B;IACA,OAAQA,MAAM,GAAG,MAAM,GAAI,IAAI;EACnC;EAEA,SAASwiB,EAAEA,CAAC5nB,KAAK,EAAE;IACf,IAAI,CAAC,IAAI,CAAC7J,OAAO,CAAC,CAAC,EAAE;MACjB,OAAOc,GAAG;IACd;IACA,IAAIqhB,IAAI;MACJlT,MAAM;MACNmT,YAAY,GAAG,IAAI,CAACC,aAAa;IAErCxY,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC;IAE7B,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,MAAM,EAAE;MAC9DsY,IAAI,GAAG,IAAI,CAACG,KAAK,GAAGF,YAAY,GAAG,KAAK;MACxCnT,MAAM,GAAG,IAAI,CAACW,OAAO,GAAG4hB,YAAY,CAACrP,IAAI,CAAC;MAC1C,QAAQtY,KAAK;QACT,KAAK,OAAO;UACR,OAAOoF,MAAM;QACjB,KAAK,SAAS;UACV,OAAOA,MAAM,GAAG,CAAC;QACrB,KAAK,MAAM;UACP,OAAOA,MAAM,GAAG,EAAE;MAC1B;IACJ,CAAC,MAAM;MACH;MACAkT,IAAI,GAAG,IAAI,CAACG,KAAK,GAAGpd,IAAI,CAACyd,KAAK,CAAC4O,YAAY,CAAC,IAAI,CAAC3hB,OAAO,CAAC,CAAC;MAC1D,QAAQ/F,KAAK;QACT,KAAK,MAAM;UACP,OAAOsY,IAAI,GAAG,CAAC,GAAGC,YAAY,GAAG,MAAM;QAC3C,KAAK,KAAK;UACN,OAAOD,IAAI,GAAGC,YAAY,GAAG,KAAK;QACtC,KAAK,MAAM;UACP,OAAOD,IAAI,GAAG,EAAE,GAAGC,YAAY,GAAG,IAAI;QAC1C,KAAK,QAAQ;UACT,OAAOD,IAAI,GAAG,IAAI,GAAGC,YAAY,GAAG,GAAG;QAC3C,KAAK,QAAQ;UACT,OAAOD,IAAI,GAAG,KAAK,GAAGC,YAAY,GAAG,IAAI;QAC7C;QACA,KAAK,aAAa;UACd,OAAOld,IAAI,CAAC2F,KAAK,CAACsX,IAAI,GAAG,KAAK,CAAC,GAAGC,YAAY;QAClD;UACI,MAAM,IAAItf,KAAK,CAAC,eAAe,GAAG+G,KAAK,CAAC;MAChD;IACJ;EACJ;;EAEA;EACA,SAAS6nB,SAASA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC1xB,OAAO,CAAC,CAAC,EAAE;MACjB,OAAOc,GAAG;IACd;IACA,OACI,IAAI,CAACuhB,aAAa,GAClB,IAAI,CAACC,KAAK,GAAG,KAAK,GACjB,IAAI,CAAC1S,OAAO,GAAG,EAAE,GAAI,MAAM,GAC5B9E,KAAK,CAAC,IAAI,CAAC8E,OAAO,GAAG,EAAE,CAAC,GAAG,OAAO;EAE1C;EAEA,SAAS+hB,MAAMA,CAACC,KAAK,EAAE;IACnB,OAAO,YAAY;MACf,OAAO,IAAI,CAACH,EAAE,CAACG,KAAK,CAAC;IACzB,CAAC;EACL;EAEA,IAAIC,cAAc,GAAGF,MAAM,CAAC,IAAI,CAAC;IAC7BG,SAAS,GAAGH,MAAM,CAAC,GAAG,CAAC;IACvBI,SAAS,GAAGJ,MAAM,CAAC,GAAG,CAAC;IACvBK,OAAO,GAAGL,MAAM,CAAC,GAAG,CAAC;IACrBM,MAAM,GAAGN,MAAM,CAAC,GAAG,CAAC;IACpBO,OAAO,GAAGP,MAAM,CAAC,GAAG,CAAC;IACrBQ,QAAQ,GAAGR,MAAM,CAAC,GAAG,CAAC;IACtBS,UAAU,GAAGT,MAAM,CAAC,GAAG,CAAC;IACxBU,OAAO,GAAGV,MAAM,CAAC,GAAG,CAAC;EAEzB,SAASW,OAAOA,CAAA,EAAG;IACf,OAAO3Q,cAAc,CAAC,IAAI,CAAC;EAC/B;EAEA,SAAS4Q,KAAKA,CAAC1oB,KAAK,EAAE;IAClBA,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC;IAC7B,OAAO,IAAI,CAAC7J,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC6J,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG/I,GAAG;EACrD;EAEA,SAAS0xB,UAAUA,CAACtvB,IAAI,EAAE;IACtB,OAAO,YAAY;MACf,OAAO,IAAI,CAAClD,OAAO,CAAC,CAAC,GAAG,IAAI,CAACuiB,KAAK,CAACrf,IAAI,CAAC,GAAGpC,GAAG;IAClD,CAAC;EACL;EAEA,IAAIshB,YAAY,GAAGoQ,UAAU,CAAC,cAAc,CAAC;IACzCrb,OAAO,GAAGqb,UAAU,CAAC,SAAS,CAAC;IAC/Btb,OAAO,GAAGsb,UAAU,CAAC,SAAS,CAAC;IAC/Bxb,KAAK,GAAGwb,UAAU,CAAC,OAAO,CAAC;IAC3BrQ,IAAI,GAAGqQ,UAAU,CAAC,MAAM,CAAC;IACzBvjB,MAAM,GAAGujB,UAAU,CAAC,QAAQ,CAAC;IAC7B1Q,KAAK,GAAG0Q,UAAU,CAAC,OAAO,CAAC;EAE/B,SAASvQ,KAAKA,CAAA,EAAG;IACb,OAAOtX,QAAQ,CAAC,IAAI,CAACwX,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACpC;EAEA,IAAIQ,KAAK,GAAGzd,IAAI,CAACyd,KAAK;IAClB8P,UAAU,GAAG;MACTtqB,EAAE,EAAE,EAAE;MAAE;MACRD,CAAC,EAAE,EAAE;MAAE;MACPxI,CAAC,EAAE,EAAE;MAAE;MACP2I,CAAC,EAAE,EAAE;MAAE;MACPE,CAAC,EAAE,EAAE;MAAE;MACPE,CAAC,EAAE,IAAI;MAAE;MACTE,CAAC,EAAE,EAAE,CAAE;IACX,CAAC;;EAEL;EACA,SAAS+pB,iBAAiBA,CAACzpB,MAAM,EAAEnE,MAAM,EAAEkE,aAAa,EAAEE,QAAQ,EAAE9K,MAAM,EAAE;IACxE,OAAOA,MAAM,CAAC2K,YAAY,CAACjE,MAAM,IAAI,CAAC,EAAE,CAAC,CAACkE,aAAa,EAAEC,MAAM,EAAEC,QAAQ,CAAC;EAC9E;EAEA,SAASypB,cAAcA,CAACC,cAAc,EAAE5pB,aAAa,EAAEypB,UAAU,EAAEr0B,MAAM,EAAE;IACvE,IAAIyjB,QAAQ,GAAGF,cAAc,CAACiR,cAAc,CAAC,CAACztB,GAAG,CAAC,CAAC;MAC/CgS,OAAO,GAAGwL,KAAK,CAACd,QAAQ,CAAC4P,EAAE,CAAC,GAAG,CAAC,CAAC;MACjCva,OAAO,GAAGyL,KAAK,CAACd,QAAQ,CAAC4P,EAAE,CAAC,GAAG,CAAC,CAAC;MACjCza,KAAK,GAAG2L,KAAK,CAACd,QAAQ,CAAC4P,EAAE,CAAC,GAAG,CAAC,CAAC;MAC/BtP,IAAI,GAAGQ,KAAK,CAACd,QAAQ,CAAC4P,EAAE,CAAC,GAAG,CAAC,CAAC;MAC9BxiB,MAAM,GAAG0T,KAAK,CAACd,QAAQ,CAAC4P,EAAE,CAAC,GAAG,CAAC,CAAC;MAChCxP,KAAK,GAAGU,KAAK,CAACd,QAAQ,CAAC4P,EAAE,CAAC,GAAG,CAAC,CAAC;MAC/B3P,KAAK,GAAGa,KAAK,CAACd,QAAQ,CAAC4P,EAAE,CAAC,GAAG,CAAC,CAAC;MAC/B30B,CAAC,GACIqa,OAAO,IAAIsb,UAAU,CAACtqB,EAAE,IAAI,CAAC,GAAG,EAAEgP,OAAO,CAAC,IAC1CA,OAAO,GAAGsb,UAAU,CAACvqB,CAAC,IAAI,CAAC,IAAI,EAAEiP,OAAO,CAAE,IAC1CD,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAE,IACtBA,OAAO,GAAGub,UAAU,CAAC/yB,CAAC,IAAI,CAAC,IAAI,EAAEwX,OAAO,CAAE,IAC1CF,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAE,IACpBA,KAAK,GAAGyb,UAAU,CAACpqB,CAAC,IAAI,CAAC,IAAI,EAAE2O,KAAK,CAAE,IACtCmL,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAE,IACnBA,IAAI,GAAGsQ,UAAU,CAAClqB,CAAC,IAAI,CAAC,IAAI,EAAE4Z,IAAI,CAAE;IAE7C,IAAIsQ,UAAU,CAAChqB,CAAC,IAAI,IAAI,EAAE;MACtB3L,CAAC,GACGA,CAAC,IACAmlB,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAE,IACpBA,KAAK,GAAGwQ,UAAU,CAAChqB,CAAC,IAAI,CAAC,IAAI,EAAEwZ,KAAK,CAAE;IAC/C;IACAnlB,CAAC,GAAGA,CAAC,IACAmS,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAE,IACrBA,MAAM,GAAGwjB,UAAU,CAAC9pB,CAAC,IAAI,CAAC,IAAI,EAAEsG,MAAM,CAAE,IACxC6S,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAE,IAAI,CAAC,IAAI,EAAEA,KAAK,CAAC;IAE1ChlB,CAAC,CAAC,CAAC,CAAC,GAAGkM,aAAa;IACpBlM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC81B,cAAc,GAAG,CAAC;IAC1B91B,CAAC,CAAC,CAAC,CAAC,GAAGsB,MAAM;IACb,OAAOs0B,iBAAiB,CAACz2B,KAAK,CAAC,IAAI,EAAEa,CAAC,CAAC;EAC3C;;EAEA;EACA,SAAS+1B,0BAA0BA,CAACC,gBAAgB,EAAE;IAClD,IAAIA,gBAAgB,KAAKnyB,SAAS,EAAE;MAChC,OAAOgiB,KAAK;IAChB;IACA,IAAI,OAAOmQ,gBAAgB,KAAK,UAAU,EAAE;MACxCnQ,KAAK,GAAGmQ,gBAAgB;MACxB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;;EAEA;EACA,SAASC,2BAA2BA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACnD,IAAIR,UAAU,CAACO,SAAS,CAAC,KAAKryB,SAAS,EAAE;MACrC,OAAO,KAAK;IAChB;IACA,IAAIsyB,KAAK,KAAKtyB,SAAS,EAAE;MACrB,OAAO8xB,UAAU,CAACO,SAAS,CAAC;IAChC;IACAP,UAAU,CAACO,SAAS,CAAC,GAAGC,KAAK;IAC7B,IAAID,SAAS,KAAK,GAAG,EAAE;MACnBP,UAAU,CAACtqB,EAAE,GAAG8qB,KAAK,GAAG,CAAC;IAC7B;IACA,OAAO,IAAI;EACf;EAEA,SAASrJ,QAAQA,CAACsJ,aAAa,EAAEC,aAAa,EAAE;IAC5C,IAAI,CAAC,IAAI,CAACnzB,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO,IAAI,CAACkG,UAAU,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;IAC1C;IAEA,IAAI2sB,UAAU,GAAG,KAAK;MAClBC,EAAE,GAAGZ,UAAU;MACfr0B,MAAM;MACNuG,MAAM;IAEV,IAAI,OAAOuuB,aAAa,KAAK,QAAQ,EAAE;MACnCC,aAAa,GAAGD,aAAa;MAC7BA,aAAa,GAAG,KAAK;IACzB;IACA,IAAI,OAAOA,aAAa,KAAK,SAAS,EAAE;MACpCE,UAAU,GAAGF,aAAa;IAC9B;IACA,IAAI,OAAOC,aAAa,KAAK,QAAQ,EAAE;MACnCE,EAAE,GAAG72B,MAAM,CAAC82B,MAAM,CAAC,CAAC,CAAC,EAAEb,UAAU,EAAEU,aAAa,CAAC;MACjD,IAAIA,aAAa,CAACjrB,CAAC,IAAI,IAAI,IAAIirB,aAAa,CAAChrB,EAAE,IAAI,IAAI,EAAE;QACrDkrB,EAAE,CAAClrB,EAAE,GAAGgrB,aAAa,CAACjrB,CAAC,GAAG,CAAC;MAC/B;IACJ;IAEA9J,MAAM,GAAG,IAAI,CAAC8H,UAAU,CAAC,CAAC;IAC1BvB,MAAM,GAAGguB,cAAc,CAAC,IAAI,EAAE,CAACS,UAAU,EAAEC,EAAE,EAAEj1B,MAAM,CAAC;IAEtD,IAAIg1B,UAAU,EAAE;MACZzuB,MAAM,GAAGvG,MAAM,CAACgL,UAAU,CAAC,CAAC,IAAI,EAAEzE,MAAM,CAAC;IAC7C;IAEA,OAAOvG,MAAM,CAACurB,UAAU,CAAChlB,MAAM,CAAC;EACpC;EAEA,IAAI4uB,KAAK,GAAGruB,IAAI,CAACC,GAAG;EAEpB,SAASE,IAAIA,CAACuJ,CAAC,EAAE;IACb,OAAO,CAACA,CAAC,GAAG,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,IAAI,CAACA,CAAC;EAClC;EAEA,SAAS4kB,aAAaA,CAAA,EAAG;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACxzB,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO,IAAI,CAACkG,UAAU,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;IAC1C;IAEA,IAAI0Q,OAAO,GAAGoc,KAAK,CAAC,IAAI,CAAClR,aAAa,CAAC,GAAG,IAAI;MAC1CF,IAAI,GAAGoR,KAAK,CAAC,IAAI,CAACjR,KAAK,CAAC;MACxBrT,MAAM,GAAGskB,KAAK,CAAC,IAAI,CAAC3jB,OAAO,CAAC;MAC5BsH,OAAO;MACPF,KAAK;MACL8K,KAAK;MACL5Z,CAAC;MACDurB,KAAK,GAAG,IAAI,CAAC3B,SAAS,CAAC,CAAC;MACxB4B,SAAS;MACTC,MAAM;MACNC,QAAQ;MACRC,OAAO;IAEX,IAAI,CAACJ,KAAK,EAAE;MACR;MACA;MACA,OAAO,KAAK;IAChB;;IAEA;IACAvc,OAAO,GAAGvM,QAAQ,CAACwM,OAAO,GAAG,EAAE,CAAC;IAChCH,KAAK,GAAGrM,QAAQ,CAACuM,OAAO,GAAG,EAAE,CAAC;IAC9BC,OAAO,IAAI,EAAE;IACbD,OAAO,IAAI,EAAE;;IAEb;IACA4K,KAAK,GAAGnX,QAAQ,CAACsE,MAAM,GAAG,EAAE,CAAC;IAC7BA,MAAM,IAAI,EAAE;;IAEZ;IACA/G,CAAC,GAAGiP,OAAO,GAAGA,OAAO,CAAC2c,OAAO,CAAC,CAAC,CAAC,CAACztB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,EAAE;IAE3DqtB,SAAS,GAAGD,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;IAChCE,MAAM,GAAGtuB,IAAI,CAAC,IAAI,CAACuK,OAAO,CAAC,KAAKvK,IAAI,CAACouB,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;IACtDG,QAAQ,GAAGvuB,IAAI,CAAC,IAAI,CAACid,KAAK,CAAC,KAAKjd,IAAI,CAACouB,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;IACtDI,OAAO,GAAGxuB,IAAI,CAAC,IAAI,CAACgd,aAAa,CAAC,KAAKhd,IAAI,CAACouB,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;IAE7D,OACIC,SAAS,GACT,GAAG,IACF5R,KAAK,GAAG6R,MAAM,GAAG7R,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,IAClC7S,MAAM,GAAG0kB,MAAM,GAAG1kB,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC,IACpCkT,IAAI,GAAGyR,QAAQ,GAAGzR,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,IAClCnL,KAAK,IAAIE,OAAO,IAAIC,OAAO,GAAG,GAAG,GAAG,EAAE,CAAC,IACvCH,KAAK,GAAG6c,OAAO,GAAG7c,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,IACnCE,OAAO,GAAG2c,OAAO,GAAG3c,OAAO,GAAG,GAAG,GAAG,EAAE,CAAC,IACvCC,OAAO,GAAG0c,OAAO,GAAG3rB,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;EAE1C;EAEA,IAAI6rB,OAAO,GAAGnS,QAAQ,CAACnlB,SAAS;EAEhCs3B,OAAO,CAAC/zB,OAAO,GAAGyhB,SAAS;EAC3BsS,OAAO,CAAC5uB,GAAG,GAAGA,GAAG;EACjB4uB,OAAO,CAAClgB,GAAG,GAAGqd,KAAK;EACnB6C,OAAO,CAACpP,QAAQ,GAAGwM,UAAU;EAC7B4C,OAAO,CAACtC,EAAE,GAAGA,EAAE;EACfsC,OAAO,CAAClC,cAAc,GAAGA,cAAc;EACvCkC,OAAO,CAACjC,SAAS,GAAGA,SAAS;EAC7BiC,OAAO,CAAChC,SAAS,GAAGA,SAAS;EAC7BgC,OAAO,CAAC/B,OAAO,GAAGA,OAAO;EACzB+B,OAAO,CAAC9B,MAAM,GAAGA,MAAM;EACvB8B,OAAO,CAAC7B,OAAO,GAAGA,OAAO;EACzB6B,OAAO,CAAC5B,QAAQ,GAAGA,QAAQ;EAC3B4B,OAAO,CAAC3B,UAAU,GAAGA,UAAU;EAC/B2B,OAAO,CAAC1B,OAAO,GAAGA,OAAO;EACzB0B,OAAO,CAAC91B,OAAO,GAAGyzB,SAAS;EAC3BqC,OAAO,CAACvR,OAAO,GAAG6O,MAAM;EACxB0C,OAAO,CAAClQ,KAAK,GAAGyO,OAAO;EACvByB,OAAO,CAACzoB,GAAG,GAAGinB,KAAK;EACnBwB,OAAO,CAAC3R,YAAY,GAAGA,YAAY;EACnC2R,OAAO,CAAC5c,OAAO,GAAGA,OAAO;EACzB4c,OAAO,CAAC7c,OAAO,GAAGA,OAAO;EACzB6c,OAAO,CAAC/c,KAAK,GAAGA,KAAK;EACrB+c,OAAO,CAAC5R,IAAI,GAAGA,IAAI;EACnB4R,OAAO,CAAC9R,KAAK,GAAGA,KAAK;EACrB8R,OAAO,CAAC9kB,MAAM,GAAGA,MAAM;EACvB8kB,OAAO,CAACjS,KAAK,GAAGA,KAAK;EACrBiS,OAAO,CAACnK,QAAQ,GAAGA,QAAQ;EAC3BmK,OAAO,CAAC7K,WAAW,GAAGsK,aAAa;EACnCO,OAAO,CAACr3B,QAAQ,GAAG82B,aAAa;EAChCO,OAAO,CAACjJ,MAAM,GAAG0I,aAAa;EAC9BO,OAAO,CAAC31B,MAAM,GAAGA,MAAM;EACvB21B,OAAO,CAAC7tB,UAAU,GAAGA,UAAU;EAE/B6tB,OAAO,CAACC,WAAW,GAAG1xB,SAAS,CAC3B,qFAAqF,EACrFkxB,aACJ,CAAC;EACDO,OAAO,CAAC/J,IAAI,GAAGA,IAAI;;EAEnB;;EAEAnkB,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;EACjCA,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;;EAEpC;;EAEAkH,aAAa,CAAC,GAAG,EAAEN,WAAW,CAAC;EAC/BM,aAAa,CAAC,GAAG,EAAEH,cAAc,CAAC;EAClCgB,aAAa,CAAC,GAAG,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE;IAC/CA,MAAM,CAACzB,EAAE,GAAG,IAAI7C,IAAI,CAAC+jB,UAAU,CAACllB,KAAK,CAAC,GAAG,IAAI,CAAC;EAClD,CAAC,CAAC;EACFsR,aAAa,CAAC,GAAG,EAAE,UAAUtR,KAAK,EAAEiK,KAAK,EAAExE,MAAM,EAAE;IAC/CA,MAAM,CAACzB,EAAE,GAAG,IAAI7C,IAAI,CAACqN,KAAK,CAACxO,KAAK,CAAC,CAAC;EACtC,CAAC,CAAC;;EAEF;;EAEAN,KAAK,CAACi4B,OAAO,GAAG,QAAQ;EAExB93B,eAAe,CAAC+iB,WAAW,CAAC;EAE5BljB,KAAK,CAAC4B,EAAE,GAAG+wB,KAAK;EAChB3yB,KAAK,CAAC4U,GAAG,GAAGA,GAAG;EACf5U,KAAK,CAACuJ,GAAG,GAAGA,GAAG;EACfvJ,KAAK,CAAC0I,GAAG,GAAGA,GAAG;EACf1I,KAAK,CAACuC,GAAG,GAAGL,SAAS;EACrBlC,KAAK,CAAC4uB,IAAI,GAAG+E,UAAU;EACvB3zB,KAAK,CAACiT,MAAM,GAAGyhB,UAAU;EACzB10B,KAAK,CAACwB,MAAM,GAAGA,MAAM;EACrBxB,KAAK,CAACoC,MAAM,GAAGob,kBAAkB;EACjCxd,KAAK,CAAC4pB,OAAO,GAAG/kB,aAAa;EAC7B7E,KAAK,CAAC6lB,QAAQ,GAAGF,cAAc;EAC/B3lB,KAAK,CAACiG,QAAQ,GAAGA,QAAQ;EACzBjG,KAAK,CAACiY,QAAQ,GAAG2c,YAAY;EAC7B50B,KAAK,CAACqzB,SAAS,GAAGO,YAAY;EAC9B5zB,KAAK,CAACkK,UAAU,GAAG0T,SAAS;EAC5B5d,KAAK,CAACymB,UAAU,GAAGA,UAAU;EAC7BzmB,KAAK,CAACgT,WAAW,GAAG2hB,eAAe;EACnC30B,KAAK,CAAC+X,WAAW,GAAG+c,eAAe;EACnC90B,KAAK,CAAC6d,YAAY,GAAGA,YAAY;EACjC7d,KAAK,CAACie,YAAY,GAAGA,YAAY;EACjCje,KAAK,CAACuc,OAAO,GAAG4B,WAAW;EAC3Bne,KAAK,CAACgY,aAAa,GAAG6c,iBAAiB;EACvC70B,KAAK,CAAC4N,cAAc,GAAGA,cAAc;EACrC5N,KAAK,CAACk4B,oBAAoB,GAAGrB,0BAA0B;EACvD72B,KAAK,CAACm4B,qBAAqB,GAAGpB,2BAA2B;EACzD/2B,KAAK,CAAC6rB,cAAc,GAAGP,iBAAiB;EACxCtrB,KAAK,CAACS,SAAS,GAAGkyB,KAAK;;EAEvB;EACA3yB,KAAK,CAACo4B,SAAS,GAAG;IACdC,cAAc,EAAE,kBAAkB;IAAE;IACpCC,sBAAsB,EAAE,qBAAqB;IAAE;IAC/CC,iBAAiB,EAAE,yBAAyB;IAAE;IAC9CpmB,IAAI,EAAE,YAAY;IAAE;IACpBqmB,IAAI,EAAE,OAAO;IAAE;IACfC,YAAY,EAAE,UAAU;IAAE;IAC1BC,OAAO,EAAE,cAAc;IAAE;IACzBlmB,IAAI,EAAE,YAAY;IAAE;IACpBN,KAAK,EAAE,SAAS,CAAE;EACtB,CAAC;EAED,OAAOlS,KAAK;AAEhB,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}