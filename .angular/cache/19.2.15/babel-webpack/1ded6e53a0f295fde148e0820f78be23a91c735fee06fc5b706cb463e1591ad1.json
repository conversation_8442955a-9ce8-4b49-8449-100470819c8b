{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { DocsComponent } from './docs.component';\ndescribe('DocsComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [DocsComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(DocsComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "DocsComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/styles/docs/docs.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\n\nimport { DocsComponent } from './docs.component';\n\ndescribe('DocsComponent', () => {\n  let component: DocsComponent;\n  let fixture: ComponentFixture<DocsComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [DocsComponent]\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(DocsComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,aAAa,QAAQ,kBAAkB;AAEhDC,QAAQ,CAAC,eAAe,EAAE,MAAK;EAC7B,IAAIC,SAAwB;EAC5B,IAAIC,OAAwC;EAE5CC,UAAU,CAACL,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACO,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACN,aAAa;KAC7B,CAAC,CACCO,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHH,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGL,OAAO,CAACU,eAAe,CAACR,aAAa,CAAC;IAChDE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}