{"ast": null, "code": "var _InputSwitchComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-switch.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-switch.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nlet InputSwitchComponent = (_InputSwitchComponent = class InputSwitchComponent {\n  constructor() {\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n    this.title = '';\n  }\n  set componentOptions(value) {\n    var _value$validation;\n    this.title = (value === null || value === void 0 ? void 0 : value.title) || '';\n    this.errorMessages = value === null || value === void 0 || (_value$validation = value.validation) === null || _value$validation === void 0 ? void 0 : _value$validation.messages;\n  }\n}, _InputSwitchComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }]\n}, _InputSwitchComponent);\nInputSwitchComponent = __decorate([Component({\n  selector: 'lib-input-switch',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], InputSwitchComponent);\nexport { InputSwitchComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "InputSwitchComponent", "_InputSwitchComponent", "constructor", "id", "readonly", "submitted", "title", "componentOptions", "value", "_value$validation", "errorMessages", "validation", "messages", "propDecorators", "control", "type", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-switch/input-switch.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-switch.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-switch.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nlet InputSwitchComponent = class InputSwitchComponent {\n    constructor() {\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n        this.title = '';\n    }\n    set componentOptions(value) {\n        this.title = value?.title || '';\n        this.errorMessages = value?.validation?.messages;\n    }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }]\n    }; }\n};\nInputSwitchComponent = __decorate([\n    Component({\n        selector: 'lib-input-switch',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], InputSwitchComponent);\nexport { InputSwitchComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,IAAIC,oBAAoB,IAAAC,qBAAA,GAAG,MAAMD,oBAAoB,CAAC;EAClDE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,KAAK,GAAG,EAAE;EACnB;EACA,IAAIC,gBAAgBA,CAACC,KAAK,EAAE;IAAA,IAAAC,iBAAA;IACxB,IAAI,CAACH,KAAK,GAAG,CAAAE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEF,KAAK,KAAI,EAAE;IAC/B,IAAI,CAACI,aAAa,GAAGF,KAAK,aAALA,KAAK,gBAAAC,iBAAA,GAALD,KAAK,CAAEG,UAAU,cAAAF,iBAAA,uBAAjBA,iBAAA,CAAmBG,QAAQ;EACpD;AAQJ,CAAC,EAPYX,qBAAA,CAAKY,cAAc,GAAG;EAC3BC,OAAO,EAAE,CAAC;IAAEC,IAAI,EAAEhB;EAAM,CAAC,CAAC;EAC1BI,EAAE,EAAE,CAAC;IAAEY,IAAI,EAAEhB;EAAM,CAAC,CAAC;EACrBK,QAAQ,EAAE,CAAC;IAAEW,IAAI,EAAEhB;EAAM,CAAC,CAAC;EAC3BM,SAAS,EAAE,CAAC;IAAEU,IAAI,EAAEhB;EAAM,CAAC,CAAC;EAC5BQ,gBAAgB,EAAE,CAAC;IAAEQ,IAAI,EAAEhB;EAAM,CAAC;AACtC,CAAC,EAAAE,qBAAA,CACJ;AACDD,oBAAoB,GAAGL,UAAU,CAAC,CAC9BG,SAAS,CAAC;EACNkB,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAErB,oBAAoB;EAC9BsB,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACtB,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEG,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}