{"extends": "../../.eslintrc.json", "ignorePatterns": ["!**/*", "**/*.stories.ts"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["projects/lib/tsconfig.lib.json", "projects/lib/tsconfig.spec.json"], "createDefaultProgram": true}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "swui", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "swui", "style": "kebab-case"}]}}, {"files": ["*.html"], "rules": {}}]}