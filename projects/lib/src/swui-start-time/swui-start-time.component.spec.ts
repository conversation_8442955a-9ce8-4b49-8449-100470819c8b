import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SwuiStartTimeComponent } from './swui-start-time.component';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { START_TIME_MODULES } from './swui-start-time.module';
import { CommonModule } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DebugElement } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';

describe('SwuiStartTimeComponent', () => {
  let component: SwuiStartTimeComponent;
  let fixture: ComponentFixture<SwuiStartTimeComponent>;
  let host: DebugElement;
  let testValue: number;
  let hoursControl: UntypedFormControl;
  let minutesControl: UntypedFormControl;
  let secondsControl: UntypedFormControl;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        BrowserAnimationsModule,
        ...START_TIME_MODULES,
      ],
      declarations: [SwuiStartTimeComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiStartTimeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    host = fixture.debugElement;
    testValue = (1000 * 60 * 60 * 5) + (1000 * 60 * 4) + (1000 * 3);
    hoursControl = component.form.get('hours') as UntypedFormControl;
    minutesControl = component.form.get('minutes') as UntypedFormControl;
    secondsControl = component.form.get('seconds') as UntypedFormControl;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set value', () => {
    component.value = testValue;
    expect(component.value).toEqual(testValue);
  });

  it('should set required', () => {
    component.required = true;
    expect(component.required).toBe(coerceBooleanProperty(true));
  });

  it('should set disabled', () => {
    component.disabled = true;
    expect(component.disabled).toBe(coerceBooleanProperty(true));
    expect(component.form.disabled).toBe(true);

    component.disabled = false;
    expect(component.disabled).toBe(coerceBooleanProperty(false));
    expect(component.form.disabled).toBe(false);
  });

  it('should set placeholder', () => {
    component.placeholder = 'test';
    expect(component.placeholder).toBe('test');
  });

  it('should get error state', () => {
    expect(component.errorState).toBeFalsy();
  });

  it('should set state changes', () => {
    const nextSpy = spyOn(component.stateChanges, 'next');
    component.required = true;
    expect(nextSpy).toHaveBeenCalled();
  });

  it('should controlType to be defined', () => {
    expect(component.controlType).toBe('lib-swui-start-time');
  });

  it('should set host id', () => {
    expect(host.nativeElement.getAttribute('id')).toBeDefined();
  });

  it('should shouldLabelFloat to be true when not empty', () => {
    component.value = testValue;
    expect(component.shouldLabelFloat).toBe(true);
  });

  it('should shouldLabelFloat to be true when host focused', () => {
    dispatchFakeEvent(host.nativeElement, 'focus');
    expect(component.shouldLabelFloat).toBe(true);
  });

  it('should shouldLabelFloat return true', () => {
    expect(component.shouldLabelFloat).toBe(true);
  });

  it('should set host class floating when it is not empty', () => {
    component.value = testValue;
    fixture.detectChanges();
    expect(host.nativeElement.classList.contains('floating')).toBe(true);
  });

  it('should set host class floating when host focused', () => {
    dispatchFakeEvent(host.nativeElement, 'focus');
    fixture.detectChanges();
    expect(host.nativeElement.classList.contains('floating')).toBe(true);
  });

  it('should set aria-describedby on host', () => {
    expect(host.nativeElement.getAttribute('aria-describedby')).toBe('');
  });

  it('should call onTouched on focus', () => {
    spyOn(component, 'onTouched');
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should init form', () => {
    expect(component.form).toBeDefined();
  });

  it('should call onChange onInit when form value changed', () => {
    spyOn(component, 'onChange');
    component.ngOnInit();
    component.value = testValue;
    expect(component.onChange).toHaveBeenCalled();
  });

  it('should complete state change on destroy', () => {
    const completeSpy = spyOn(component.stateChanges, 'complete');
    component.ngOnDestroy();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should setDescribedByIds', () => {
    const testIds = ['test1', 'test2'];
    component.setDescribedByIds(testIds);
    expect(component.describedBy).toBe(testIds.join(' '));
  });

  it('patchform on writevalue', () => {
    component.writeValue(testValue);
    expect(component.value).toBe(testValue);
  });

  it('should set onChange in registerOnChange', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    component.writeValue(testValue);
    expect(test).toBe(true);
  });

  it('should set onChange in registerOnTouched', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnTouched(fn);
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(test).toBe(true);
  });

  it('should disable form', () => {
    component.setDisabledState(true);
    expect(component.form.disabled).toBe(true);

    component.setDisabledState(false);
    expect(component.form.disabled).toBe(false);
  });

  it('should not set valueAccessor if form control', () => {
    (fixture.componentInstance as any).ngControl = new UntypedFormControl(testValue);
    expect(component.ngControl.valueAccessor).toBeUndefined();
  });

  it('should disable seconds', () => {
    component.disableSeconds = true;
    expect(secondsControl.disabled).toBe(true);

    component.value = testValue;
    const seconds = 1000 * 3;
    expect(component.value).toBe(testValue - seconds);
  });

  it('should not cut last symbol if hours value.length > 2 && control !== hours && firstSymbol !== 0', () => {
    minutesControl.setValue('123');
    fixture.detectChanges();
    component.processInputValue('hours');
    expect(minutesControl.value).toBe('123');
  });

  it('should set 59 if value.length > 2 && control !== hours', () => {
    minutesControl.setValue('66');
    fixture.detectChanges();
    component.processInputValue('minutes');
    expect(minutesControl.value).toBe('59');
  });

  it('should not set 59 if value.length > 2 && control === hours', () => {
    minutesControl.setValue('66');
    fixture.detectChanges();
    component.processInputValue('hours');
    expect(minutesControl.value).toBe('66');
  });

  it('should cut first symbol 0 if first symbol = 0  && value.length > 2', () => {
    minutesControl.setValue('012');
    fixture.detectChanges();
    component.processInputValue('minutes');
    expect(minutesControl.value).toBe('12');
  });

  it('should set max value if first symbol = 0 && value.length > maxLength && value > maxValue && el !== days', () => {
    minutesControl.setValue('066');
    fixture.detectChanges();
    component.processInputValue('minutes');
    expect(minutesControl.value).toBe('59');
  });

  it('should set max hours value if first symbol = 0 && value.length > maxLength && value > maxValue && el === hours', () => {
    hoursControl.setValue('066');
    fixture.detectChanges();
    component.processInputValue('hours');
    expect(hoursControl.value).toBe('23');
  });

  it('should set max hours value if value > maxHoursValue && el === hours', () => {
    hoursControl.setValue('66');
    fixture.detectChanges();
    component.processInputValue('hours');
    expect(hoursControl.value).toBe('23');
  });

  it('should set empty string if value is not digits', () => {
    minutesControl.setValue('string value');
    fixture.detectChanges();
    component.processInputValue('minutes');
    expect(minutesControl.value).toBe('');
  });

  it('should set positive value instead of negative', () => {
    minutesControl.setValue('-20');
    fixture.detectChanges();
    component.processInputValue('minutes');
    expect(minutesControl.value).toBe('20');
  });


});

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent( node: Node | Window, type: string ) {
  node.dispatchEvent(createFakeEvent(type));
}
