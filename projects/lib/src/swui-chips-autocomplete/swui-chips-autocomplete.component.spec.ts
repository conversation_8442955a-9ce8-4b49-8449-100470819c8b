import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { CommonModule } from '@angular/common';
import { DebugElement } from '@angular/core';
import { async, ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { UntypedFormControl } from '@angular/forms';
import { MatOptionSelectionChange } from '@angular/material/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';

import { OptionModel } from '../swui-autoselect/option.model';
import { SwuiChipsAutocompleteComponent } from './swui-chips-autocomplete.component';
import { CHIPS_AUTOCOMPLETE_MODULES } from './swui-chips-autocomplete.module';
import createSpy = jasmine.createSpy;
import Spy = jasmine.Spy;


describe('SwuiChipsAutocompleteComponent', () => {
  let component: SwuiChipsAutocompleteComponent;
  let fixture: ComponentFixture<SwuiChipsAutocompleteComponent>;
  let host: DebugElement;
  let testValue: string[];
  let options: OptionModel[];
  let spySearch: Spy;
  let spyAdd: Spy;

  describe('async data', () => {

    beforeEach(async(() => {
      TestBed.configureTestingModule({
        imports: [
          CommonModule,
          BrowserAnimationsModule,
          ...CHIPS_AUTOCOMPLETE_MODULES,
        ],
        declarations: [SwuiChipsAutocompleteComponent]
      })
        .compileComponents();
    }));

    beforeEach(() => {
      fixture = TestBed.createComponent(SwuiChipsAutocompleteComponent);
      component = fixture.componentInstance;
      host = fixture.debugElement;
      options = [
        { id: 'test1', text: 'One' },
        { id: 'test2', text: 'Two' },
        { id: 'test3', text: 'Three' },
        { id: 'test4', text: 'Four', disabled: true },
        { id: 'test5', text: 'Five', disabled: true }
      ];

      spySearch = createSpy().and.callFake(( findText: string ): Observable<any[]> => {
        const items = options.filter(( { text } ) => {
          return text.toLocaleLowerCase().includes(findText.toLowerCase());
        });

        return of(items).pipe(delay(300));
      });
      spyAdd = createSpy().and.callFake(( text: string ): Observable<any> => {
        return of({ id: text, text }).pipe(delay(300));
      });

      component.searchFn = spySearch;

      component.addFn = spyAdd;

      fixture.detectChanges();
    });

    it('should call search', fakeAsync(() => {
      component.inputFormControl.setValue('test');

      tick(301);

      expect(spySearch.calls.allArgs()).toEqual([[''], ['test']]);
    }));

    it('should call add', fakeAsync(async () => {
      component.inputFormControl.setValue('test');

      tick(301);

      expect(component.hasFounded).toBeFalsy();
      await fixture.whenStable();

      component.add({ source: { value: 'test' } } as MatOptionSelectionChange);

      expect(spyAdd).toHaveBeenCalledWith('test');
    }));
  });

  describe('sync data', () => {

    beforeEach(async(() => {
      TestBed.configureTestingModule({
        imports: [
          CommonModule,
          BrowserAnimationsModule,
          ...CHIPS_AUTOCOMPLETE_MODULES,
        ],
        declarations: [SwuiChipsAutocompleteComponent]
      })
        .compileComponents();
    }));

    beforeEach(() => {
      fixture = TestBed.createComponent(SwuiChipsAutocompleteComponent);
      component = fixture.componentInstance;
      host = fixture.debugElement;
      testValue = ['test1', 'test2', 'test3'];
      options = [
        { id: 'test1', text: 'One' },
        { id: 'test2', text: 'Two' },
        { id: 'test3', text: 'Three' },
        { id: 'test4', text: 'Four', disabled: true },
        { id: 'test5', text: 'Five', disabled: true }
      ];

      fixture.detectChanges();
    });

    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should set value', () => {
      component.value = testValue;
      expect(component.value).toEqual(testValue);
    });

    it('should set required', () => {
      component.required = true;
      expect(component.required).toBe(coerceBooleanProperty(true));
    });

    it('should set disabled', () => {
      component.disabled = true;
      expect(component.disabled).toBe(coerceBooleanProperty(true));

      component.disabled = false;
      expect(component.disabled).toBe(coerceBooleanProperty(false));
    });

    it('should set placeholder', () => {
      component.placeholder = 'test';
      expect(component.placeholder).toBe('test');
    });

    it('should get empty true if controls are empty', () => {
      expect(component.empty).toBe(true);
    });

    it('should get empty false if controls are not empty', () => {
      component.value = testValue;
      expect(component.empty).toBe(false);
    });

    it('should get error state', () => {
      expect(component.errorState).toBeFalsy();
    });

    it('should set state changes', () => {
      const nextSpy = spyOn(component.stateChanges, 'next');
      component.required = true;
      expect(nextSpy).toHaveBeenCalled();
    });

    it('should controlType to be defined', () => {
      expect(component.controlType).toBe('lib-swui-chips-autocomplete');
    });

    it('should set host id', () => {
      expect(host.nativeElement.getAttribute('id')).toBeDefined();
    });

    it('should shouldLabelFloat to be true when not empty', () => {
      component.value = testValue;
      expect(component.shouldLabelFloat).toBe(true);
    });

    it('should shouldLabelFloat to be true when host focused', () => {
      expect(component.shouldLabelFloat).toBe(false);

      dispatchFakeEvent(host.nativeElement, 'focus');
      expect(component.shouldLabelFloat).toBe(true);
    });

    it('should set host class floating when it is not empty', () => {
      component.value = testValue;
      fixture.detectChanges();
      expect(host.nativeElement.classList.contains('floating')).toBe(true);
    });

    it('should set host class floating when host focused', () => {
      dispatchFakeEvent(host.nativeElement, 'focus');
      fixture.detectChanges();
      expect(host.nativeElement.classList.contains('floating')).toBe(true);
    });

    it('should set aria-describedby on host', () => {
      expect(host.nativeElement.getAttribute('aria-describedby')).toBe('');
    });

    it('should call onTouched on focus', () => {
      spyOn(component, 'onTouched');
      dispatchFakeEvent(host.nativeElement, 'focus');
      dispatchFakeEvent(host.nativeElement, 'blur');
      expect(component.onTouched).toHaveBeenCalled();
    });

    it('should call onChange onInit when selected value changed', () => {
      spyOn(component, 'onChange');
      component.ngOnInit();
      component.value = testValue;
      expect(component.onChange).toHaveBeenCalled();
    });

    it('should call initFilteredItems onInit', () => {
      spyOn(component, 'initFilteredItems');
      component.ngOnInit();
      expect(component.initFilteredItems).toHaveBeenCalled();
    });

    it('should select item', () => {
      component.selectItem('test4');
      expect(component.value).toEqual(['test4']);

      const pushSpy = spyOn(component.selectedItems, 'push');
      component.selectItem(undefined);
      expect(pushSpy).toHaveBeenCalledTimes(0);
    });

    it('should remove item', () => {
      component.value = testValue;
      component.remove('test1');
      expect(component.value).toEqual(['test2', 'test3']);
    });

    it('should not set valueAccessor if form control', () => {
      (fixture.componentInstance as any).ngControl = new UntypedFormControl(testValue);
      expect(component.ngControl.valueAccessor).toBeUndefined();
    });

    it('should disable control', () => {
      component.setDisabledState(true);
      expect(component.disabled).toBe(true);

      component.setDisabledState(false);
      expect(component.disabled).toBe(false);
    });

    it('should set onChange in registerOnChange', () => {
      let test = false;
      const fn = () => {
        test = true;
      };
      component.registerOnChange(fn);
      component.writeValue(testValue);
      expect(test).toBe(true);
    });

    it('should set onChange in registerOnTouched', () => {
      let test = false;
      const fn = () => {
        test = true;
      };
      component.registerOnTouched(fn);
      dispatchFakeEvent(host.nativeElement, 'focus');
      dispatchFakeEvent(host.nativeElement, 'blur');
      expect(test).toBe(true);
    });

    it('should setDescribedByIds', () => {
      const testIds = ['test1', 'test2'];
      component.setDescribedByIds(testIds);
      expect(component.describedBy).toBe(testIds.join(' '));
    });

    it('should complete state change on destroy', () => {
      const completeSpy = spyOn(component.stateChanges, 'complete');
      component.ngOnDestroy();
      expect(completeSpy).toHaveBeenCalled();
    });

    it('should set selected items in writevalue', () => {
      component.writeValue(testValue);
      expect(component.selectedItems).toEqual(testValue);
    });

    it('should set items', () => {
      component.items = options;
      expect(component.items).toEqual(options);
    });

    it('should disable option', () => {
      component.items = options;
      component.selectItem('test1');
      expect(component.itemsMap.get('test1').selected).toBe(true);
    });

    it('should enable option', () => {
      component.items = options;
      component.selectItem('test1');
      component.remove('test1');
      expect(component.itemsMap.get('test1').selected).toBe(false);
    });

    it('should filter items', () => {
      component.items = options;
      component.initFilteredItems();
      let expected = [] as string[];
      component.filteredItems.subscribe(( val: OptionModel[] ) => {
        val.forEach(item => {
          expected.push(item.text);
        });
      });
      expect(expected).toEqual(['One', 'Two', 'Three', 'Four', 'Five']);
    });
  });

});

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent( node: Node | Window, type: string ) {
  node.dispatchEvent(createFakeEvent(type));
}
