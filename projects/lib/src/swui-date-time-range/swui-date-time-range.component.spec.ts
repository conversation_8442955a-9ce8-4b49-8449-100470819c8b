import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { DebugElement } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { UntypedFormControl } from '@angular/forms';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { TranslateModule } from '@ngx-translate/core';
import * as moment from 'moment';

import { CustomPeriodModule } from './custom-period/custom-period.module';
import { DATE_TIME_RANGE_MODULES } from './swui-date-time-range.module';


import { SwuiDateTimeRangeComponent } from './swui-date-time-range.component';


describe('SwuiDateTimeRangeComponent', () => {
  let component: SwuiDateTimeRangeComponent;
  let fixture: ComponentFixture<SwuiDateTimeRangeComponent>;
  let host: DebugElement;
  let testDateRange: any;
  let testDate: moment.Moment;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        BrowserAnimationsModule,
        CustomPeriodModule,
        TranslateModule.forRoot(),
        ...DATE_TIME_RANGE_MODULES,
      ],
      declarations: [SwuiDateTimeRangeComponent],
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiDateTimeRangeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    testDate = moment.utc().clone();
    testDateRange = { from: testDate.add(-1, 'd'), to: testDate.add(1, 'd') };
    host = fixture.debugElement;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set value', () => {
    component.value = testDateRange;
    expect(component.value).toBeTruthy();
    expect(component.value).toEqual(component.form.getRawValue());
  });

  it('should set required', () => {
    component.required = true;
    expect(component.required).toBe(coerceBooleanProperty(true));
  });

  it('should set disabled', () => {
    component.disabled = true;
    expect(component.disabled).toBe(coerceBooleanProperty(true));
    expect(component.form.disabled).toBe(true);

    component.disabled = false;
    expect(component.disabled).toBe(coerceBooleanProperty(false));
    expect(component.form.disabled).toBe(false);
  });

  it('should get empty', () => {
    expect(component.empty).toBe(true);

    component.value = testDateRange;
    expect(component.empty).toBe(false);
  });

  it('should set placeholder', () => {
    component.placeholder = 'test';
    expect(component.placeholder).toBe('test');
  });

  it('should get error state', () => {
    expect(component.errorState).toBeFalsy();
  });

  it('should set state changes', () => {
    const nextSpy = spyOn(component.stateChanges, 'next');
    component.required = true;
    expect(nextSpy).toHaveBeenCalled();
  });

  it('should controlType to be defined', () => {
    expect(component.controlType).toBe('lib-swui-date-time-range');
  });

  it('should set host id', () => {
    expect(host.nativeElement.getAttribute('id')).toBeDefined();
  });

  it('should setDescribedByIds', () => {
    const testIds = ['test1', 'test2'];
    component.setDescribedByIds(testIds);
    expect(component.describedBy).toBe(testIds.join(' '));
  });

  it('should shouldLabelFloat to be true', () => {
    expect(component.shouldLabelFloat).toBe(true);
  });

  it('should complete state change on destroy', () => {
    const completeSpy = spyOn(component.stateChanges, 'complete');
    component.ngOnDestroy();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should init form', () => {
    expect(component.form).toBeDefined();
  });

  it('should set onChange in registerOnChange', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    component.writeValue(testDateRange);
    expect(test).toBe(true);
  });

  it('should get config', () => {
    component.dateFormat = 'DD.MM.YYYY';
    component.timeFormat = 'HH:mm:ss';
    component.disableTime = {
      hour: false,
      minute: false,
      second: false
    };
    component.hideTime = true;

    const expected = {
      dateFormat: component.dateFormat,
      timeFormat: component.timeFormat,
      timeDisableLevel: component.disableTime,
      disableTimepicker: component.hideTime,
      timeZone: undefined,
    };

    expect(component.config).toEqual(expected);
  });

  it('should set custom period', () => {
    component.onPeriodChange(testDateRange);
    expect(component.form.getRawValue()).toEqual(testDateRange);
  });

  it('should reset control', () => {
    component.writeValue(testDateRange);
    const fromControl = component.form.get('from') as UntypedFormControl;
    component.onResetClick(createFakeEvent('click'), fromControl);
    expect(fromControl.value).toBeFalsy();
  });

  it('should return fromcontrol', () => {
    const control = component.fromControl;
    const expected = component.form.get('from') as UntypedFormControl;
    expect(control).toEqual(expected);
  });

  it('should return tocontrol', () => {
    const control = component.toControl;
    const expected = component.form.get('to') as UntypedFormControl;
    expect(control).toEqual(expected);
  });

  it('should set maxDate', () => {
    component.maxDate = testDate;
    expect(component.processedFromMaxDate).toEqual(testDate);
  });

  it('should set mindDate', () => {
    component.minDate = testDate;
    expect(component.processedToMinDate).toEqual(testDate);
  });

  it('should rise from label', () => {
    component.writeValue({ from: testDate, to: null });
    expect(component.fromLabelRised).toBe(true);

    component.writeValue({ from: null, to: null });
    expect(component.fromLabelRised).toBe(false);
  });

  it('should rise to label', () => {
    component.writeValue({ from: null, to: testDate });
    expect(component.toLabelRised).toBe(true);

    component.writeValue({ from: null, to: null });
    expect(component.toLabelRised).toBe(false);
  });

});

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

