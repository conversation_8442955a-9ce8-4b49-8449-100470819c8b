import { moduleMetadata, storiesOf } from '@storybook/angular';
import * as moment from 'moment';

import { SwuiCalendarComponent } from './swui-calendar.component';
import { SwuiCalendarModule } from './swui-calendar.module';
import { action } from 'storybook/actions';


const template = `<lib-swui-calendar [ngModel]="value" [timeZone]="timezone" (ngModelChange)="change($event)"></lib-swui-calendar>`;

storiesOf('Date/Calendar', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        SwuiCalendarModule
      ],
    })
  )
  .add('default', () => ({
    component: SwuiCalendarComponent,
    props: {
      change: action('change'),
    },
  }))
  .add('minDate today -2', () => ({
    component: SwuiCalendarComponent,
    props: {
      change: action('change'),
      minDate: moment.utc().add(-2, 'days')
    }
  }))
  .add('maxDate today +2', () => ({
    component: SwuiCalendarComponent,
    props: {
      change: action('change'),
      maxDate: moment.utc().add(2, 'days')
    }
  }))
  .add('form', () => ({
    template,
    props: {
      change: action('change')
    }
  }))
  .add('timezone UTC', () => ({
    template,
    props: {
      change: action('change'),
      timezone: 'Etc/UTC'
    }
  }))
  .add('timeZone [Europe/Minsk]', () => ({
    template,
    props: {
      change: action('change'),
      value: moment.utc().startOf('day').toISOString(),
      config: {
        timeZone: 'Europe/Minsk',
        timeFormat: 'HH:mm z'
      }
    },
  }));
