import { moduleMetadata, storiesOf } from '@storybook/angular';
import * as moment from 'moment';
import { CommonModule } from '@angular/common';
import { action } from 'storybook/actions';
import { SwuiDatetimepickerModule } from './swui-datetimepicker.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';


const template = `
      <mat-form-field appearance="outline">
        <mat-label>Test label</mat-label>
        <lib-swui-datetimepicker
          [ngModel]="value"
          (ngModelChange)="change($event)"
          [disabled]="disabled"
          [config]="config"
          [minDate]="minDate"
          [maxDate]="maxDate">
        </lib-swui-datetimepicker>
        <button mat-button mat-icon-button matSuffix [disabled]="disabled">
          <mat-icon>date_range</mat-icon>
        </button>
      </mat-form-field>
    `;

storiesOf('Date/DateTime Picker', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        CommonModule,
        BrowserAnimationsModule,
        SwuiDatetimepickerModule,
        MatButtonModule,
        MatIconModule,
        MatFormFieldModule,
        MatIconModule
      ],
    })
  )
  .add('usage', () => ({
    template,
    props: {
      change: action('change'),
    }
  }))
  .add('minDate', () => ({
    template,
    props: {
      change: action('change'),
      minDate: moment.utc().add(-2, 'days')
    },
  }))
  .add('maxDate', () => ({
    template,
    props: {
      change: action('change'),
      maxDate: moment.utc().add(2, 'days')
    },
  }))
  .add('disableTimepicker 2', () => ({
    template,
    props: {
      change: action('change'),
      config: {
        disableTimepicker: true
      }
    },
  }))
  .add('timeDisableLevel seconds', () => ({
    template,
    props: {
      change: action('change'),
      config: {
        timeDisableLevel: {
          second: false
        }
      }
    },
  }))
  .add('timeFormat HH.mm.ss', () => ({
    template,
    props: {
      change: action('change'),
      config: {
        timeFormat: 'HH.mm.ss'
      }
    },
  }))
  .add('dateFormat YYYY-MM-DD', () => ({
    template,
    props: {
      change: action('change'),
      config: {
        dateFormat: 'YYYY-MM-DD'
      }
    },
  }))
  .add('disabled', () => ({
    template,
    props: {
      change: action('change'),
      value: '2019-01-14T09:20:06.246Z',
      disabled: true
    },
  }))
  .add('timeZone [America/Adak]', () => ({
    template,
    props: {
      change: action('change'),
      config: {
        timeZone: 'America/Adak',
        timeFormat: 'HH:mm z'
      }
    },
  }))
  .add('timeZone [UTC]', () => ({
    template,
    props: {
      change: action('change'),
      config: {
        timeZone: 'Etc/UTC',
        timeFormat: 'hh:mm z'
      }
    },
  }))
  .add('timeZone [Europe/Minsk]', () => ({
    template,
    props: {
      change: action('change'),
      config: {
        timeZone: 'Europe/Minsk',
        timeFormat: 'HH:mm z'
      }
    },
  }));
