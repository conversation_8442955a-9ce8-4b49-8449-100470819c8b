import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { GamesSelectManagerService } from '../games-select-manager.service';
import { GameSelectItem, getLabelClass, SelectItemLabel } from '../game-select-item/game-select-item.model';
import { fromEvent, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';

@Component({
    // tslint:disable-next-line:component-selector
    selector: 'sw-available-games',
    templateUrl: 'available-games.component.html',
    styleUrls: [
        '../games-select-manager.scss'
    ],
    standalone: false
})
export class AvailableGamesComponent implements OnInit, OnDestroy {

  @Input() disabled = false;
  @Input() loading = false;
  @Input() height = '500px';

  @ViewChild('searchInput', { static: true }) searchInput: ElementRef | undefined;
  searchTerm: string | undefined;

  availableChecked = false;
  available: GameSelectItem[] | undefined;

  private subscriptions: Subscription[] = [];
  private allGames: GameSelectItem[] | undefined;

  constructor( private service: GamesSelectManagerService,
  ) {
    this.subscribeToChanges();
  }

  get scrollPortHeight(): number {
    return parseFloat(this.height) - 58;
  }

  ngOnInit() {
    this.createSearchStream();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  checkAvailableChanged() {
    this.setChecked(this.available, this.availableChecked);
    this.calculateCheckedGames();
  }

  getLabelClass( label: SelectItemLabel ): string {
    return getLabelClass(label);
  }

  gameSelectionChanged() {
    this.refreshAvailableChecked();
    this.calculateCheckedGames();
  }

  trackByFn( _index: number, item: GameSelectItem ) {
    return item.id;
  }

  private refreshAvailableChecked() {
    this.availableChecked = !!this.available && this.available.every(game => game.checked) && this.available.length > 0;
  }

  private createSearchStream() {
    if (this.searchInput) {
      let sub = fromEvent(this.searchInput.nativeElement, 'input')
        .pipe(
          map(( e: any ) => e['currentTarget']['value']),
          debounceTime(100),
          distinctUntilChanged()
        )
        .subscribe(( search ) => this.setSearchTerm(search));
      this.subscriptions.push(sub);
    }
  }

  private calculateCheckedGames() {
    this.service.calculateCheckedAvailableGames((this.allGames || []).filter(game => game.checked));
  }

  private setChecked( games: GameSelectItem[] | undefined, checked: boolean ) {
    (games || []).forEach(game => {
      game.checked = checked;
    });
  }

  private setSearchTerm( search: string ) {
    this.searchTerm = search;
    this.available = this.service.filterGames(this.allGames || [], this.searchTerm);
    this.refreshAvailableChecked();
  }

  private subscribeToChanges() {
    this.subscriptions.push(
      this.service.availableGames$.subscribe(games => {
        this.allGames = games;
        this.available = games;
        this.setSearchTerm(this.searchTerm || '');
        this.calculateCheckedGames();
      })
    );
  }
}
