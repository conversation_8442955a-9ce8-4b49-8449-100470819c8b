import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { gameSelectItemTypes } from '../game-select-item/game-select-item-types';
import { GameSelectItem, getLabelClass, SelectItemLabel } from '../game-select-item/game-select-item.model';
import { GamesSelectManagerService, SelectedGamesExtraColumnScheme } from '../games-select-manager.service';

@Component({
    // tslint:disable-next-line:component-selector
    selector: 'sw-selected-items',
    templateUrl: 'selected-items.component.html',
    styleUrls: ['../games-select-manager.scss'],
    standalone: false
})

export class SelectedItemsComponent implements OnInit {

  @Input() extraColumn: SelectedGamesExtraColumnScheme | undefined;
  @Input() disabled = false;
  @Input() emptyGamesText = 'COMPONENTS.GAMES_SELECT_MANAGER.emptySelectedGames';
  @Input() height = '500px';

  @Output() dropListDropped: EventEmitter<GameSelectItem[]> = new EventEmitter();

  @ViewChild('scrollTable', { static: true }) table: ElementRef | undefined;

  items: GameSelectItem[] = [];
  allChecked = false;
  itemTypes = gameSelectItemTypes;

  constructor(
    private service: GamesSelectManagerService,
  ) {

    this.service.selectedItems$.subscribe(( games: GameSelectItem[] ) => {
      this.items = games;
      this.refreshAllChecked();
      this.calculateCheckedItems();
    });
  }

  ngOnInit() {
  }

  checkAllChanged() {
    if (this.disabled) {
      return;
    }
    this.items.forEach(item => item.checked = this.allChecked);
    this.calculateCheckedItems();
  }

  getLabelClass( label: GameSelectItem | SelectItemLabel ): string[] {
    return [
      getLabelClass(label)
    ];
  }

  itemSelectionChanged() {
    this.refreshAllChecked();
    this.calculateCheckedItems();
  }

  extraColumnValueChanged() {
    this.service.refreshSelected();
  }

  drop( event: CdkDragDrop<GameSelectItem[], any> ) {
    moveItemInArray(this.items, event.previousIndex, event.currentIndex);
    this.dropListDropped.emit(this.items);
  }

  private refreshAllChecked() {
    this.allChecked = this.items && this.items.length > 0 && this.items.every(item => item.checked);
  }

  private calculateCheckedItems() {
    const checked = this.items.filter(item => item.checked);
    const labelTypes = [
      gameSelectItemTypes.PROVIDER,
      gameSelectItemTypes.LABEL
    ];

    this.service.calculateCheckedSelectedGames(checked.filter(i => i.type === gameSelectItemTypes.GAME));
    this.service.calculateCheckedSelectedLabels(checked.filter(i => labelTypes.indexOf(i.type) > -1));
    this.service.calculateCheckedSelectedIntersections(
      checked.filter(i => i.type === gameSelectItemTypes.INTERSECTION)
    );
  }

}
