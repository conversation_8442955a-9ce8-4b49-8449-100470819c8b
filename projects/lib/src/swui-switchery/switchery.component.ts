import { Component, forwardRef, Input } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
    selector: 'lib-switchery',
    templateUrl: './switchery.component.html',
    styleUrls: [
        './switchery.component.scss'
    ],
    providers: [
        { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => SwitcheryComponent), multi: true }
    ],
    standalone: false
})
export class SwitcheryComponent implements ControlValueAccessor {
  @Input() public prefix: string[] = [];
  @Input() public inline = false;

  isDisabled = false;

  private propagateChange: any;
  private _checked = false;

  @Input()
  public set checked( value: boolean ) {
    this._checked = value;

    if (typeof this.propagateChange === 'function') {
      this.propagateChange(this.checked);
    }
  }

  public get checked(): boolean {
    return this._checked;
  }

  writeValue( value: boolean ) {
    this.checked = value === true;
  }

  registerOnChange( fn: () => void ) {
    this.propagateChange = fn;
  }

  registerOnTouched() {
  }

  setDisabledState( isDisabled: boolean ) {
    this.isDisabled = isDisabled;
  }

  checkedClass() {
    return {
      'checked': this.checked,
      'disabled': this.isDisabled,
    };
  }

  actualPrefix(): string {
    let prefix = '';

    if (!!this.prefix[0] && this.checked) {
      prefix = this.prefix[0];
    }
    if (!!this.prefix[1] && !this.checked) {
      prefix = this.prefix[1];
    }

    return prefix;
  }

  toggleChecked( event: Event ) {
    event.stopPropagation();
    event.preventDefault();
    if (this.isDisabled) {
      return;
    }

    this.checked = !this.checked;
  }
}
