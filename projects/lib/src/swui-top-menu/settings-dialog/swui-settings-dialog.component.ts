import { ChangeDetectionStrategy, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { isEqual } from 'lodash';
import * as moment from 'moment';
import 'moment-timezone';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AppSettings } from '../../services/settings/app-settings';
import { DEFAULT_SETTINGS, SettingsService } from '../../services/settings/settings.service';

const TIMEZONES = moment.tz.names()
  .filter(name => name.indexOf('Etc') === -1)
  .map(name => {
    const zone = moment.tz(name);
    return {
      text: `${zone.format('Z')} ${name}`,
      zone: zone.utcOffset(),
      id: name
    };
  })
  .sort(( a, b ) => {
    const zoneDiff = b.zone - a.zone;
    if (zoneDiff !== 0) {
      return zoneDiff;
    }
    if (a.text > b.text) {
      return 1;
    }
    if (a.text < b.text) {
      return -1;
    }
    return 0;
  });

const TIME_FORMATS = ['HH:mm:ss', 'HH:mm', 'HH.mm.ss', 'HH.mm'].map(format => ({
  text: moment.utc().format(format),
  id: format
}));

const DATE_FORMATS = ['DD.MM.YYYY', 'DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY/MM/DD', 'YY/MM/DD'].map(format => ({
  text: moment.utc().format(format),
  id: format
}));

const EXAMPLE_NUMBER = 1234567.89;

const CURRENCY_FORMATS = ['de-DE', 'zh-CN', 'ru-RU'].map(format => {
  const toLocale = ( lang: string ) => EXAMPLE_NUMBER.toLocaleString(lang, { minimumFractionDigits: 2 });
  const text = toLocale(format);
  const primary = text === toLocale(window.navigator.language);
  return {
    id: primary ? window.navigator.language : format,
    text,
    primary
  };
});

@Component({
    selector: 'lib-swui-settings-dialog',
    templateUrl: './swui-settings-dialog.component.html',
    styleUrls: ['./swui-settings-dialog.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class SwuiSettingsDialogComponent implements OnInit, OnDestroy {
  readonly form: UntypedFormGroup;
  private readonly destroyed = new Subject<void>();

  constructor( private readonly service: SettingsService ) {
    this.form = new UntypedFormGroup({
      timezoneName: new UntypedFormControl(''),
      pageSize: new UntypedFormControl(''),
      dateFormat: new UntypedFormControl(''),
      timeFormat: new UntypedFormControl(''),
      currencyFormat: new UntypedFormControl('')
    });
  }

  ngOnInit() {
    this.service.appSettings$.pipe(
      takeUntil(this.destroyed)
    ).subscribe(settings => {
      this.setValue(settings);
    });
  }

  ngOnDestroy(): void {
    this.destroyed.next(undefined);
    this.destroyed.complete();
  }

  get timezoneOptions() {
    return TIMEZONES;
  }

  get pageSizeOptions() {
    return [10, 20, 30, 50, 100];
  }

  get dateFormatOptions() {
    return DATE_FORMATS;
  }

  get timeFormatOptions() {
    return TIME_FORMATS;
  }

  get currencyFormatOptions() {
    return CURRENCY_FORMATS;
  }

  get hasDefaultValues() {
    return isEqual(this.form.value, DEFAULT_SETTINGS);
  }

  onReset( event: MouseEvent ) {
    event.preventDefault();
    this.setValue(DEFAULT_SETTINGS);
    this.form.markAsDirty();
  }

  private setValue( settings: AppSettings ) {
    this.form.setValue({
      timezoneName: settings.timezoneName,
      pageSize: settings.pageSize,
      dateFormat: settings.dateFormat,
      timeFormat: settings.timeFormat,
      currencyFormat: settings.currencyFormat
    }, { emitEvent: false });
  }
}
