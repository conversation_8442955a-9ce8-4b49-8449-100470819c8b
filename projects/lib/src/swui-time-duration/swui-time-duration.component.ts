import { Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, FormGroupDirective, NgControl } from '@angular/forms';
import { MatFormFieldControl } from '@angular/material/form-field';
import { FocusMonitor } from '@angular/cdk/a11y';
import { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';
import * as moment from 'moment';
import { SwuiTimeDuration } from './swui-time-duration.interface';
import { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';
import { ErrorStateMatcher } from '@angular/material/core';
import { takeUntil } from 'rxjs/operators';

const CONTROL_NAME = 'lib-swui-time-duration';
let nextUniqueId = 0;

@Component({
    selector: 'lib-swui-time-duration',
    templateUrl: './swui-time-duration.component.html',
    styleUrls: ['./swui-time-duration.component.scss'],
    providers: [{ provide: MatFormFieldControl, useExisting: SwuiTimeDurationComponent }],
    standalone: false
})

export class SwuiTimeDurationComponent extends SwuiMatFormFieldControl<number | undefined> implements OnInit {
  @Input()
  get value(): number | null {
    return this._value;
  }

  set value( val: number | null ) {
    this._value = coerceNumberProperty(val, 0);
    this.patchForm(this._value);
    this.stateChanges.next(undefined);
  }

  @Input()
  get daysDisabled(): boolean {
    return this._daysDisabled;
  }

  set daysDisabled( value: boolean ) {
    this._daysDisabled = coerceBooleanProperty(value);
    this._daysDisabled ? this.daysControl.disable() : this.daysControl.enable();
  }

  @Input()
  get secondsDisabled(): boolean {
    return this._secondsDisabled;
  }

  set secondsDisabled( value: boolean ) {
    this._secondsDisabled = coerceBooleanProperty(value);
    const sec = this.form.get('seconds') as UntypedFormControl;
    sec.disable();
  }

  get empty() {
    return this.value === null;
  }

  readonly form: UntypedFormGroup;
  readonly controlType = CONTROL_NAME;

  @ViewChild('days') daysInput?: ElementRef;
  @ViewChild('hours') hoursInput?: ElementRef;
  @ViewChild('minutes') minutesInput?: ElementRef;
  @ViewChild('seconds') secondsInput?: ElementRef;

  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return true;
  }

  private _daysDisabled = false;
  private _secondsDisabled = false;
  private _value: number | null = null;

  constructor( fm: FocusMonitor,
               elRef: ElementRef<HTMLElement>,
               @Optional() @Self() ngControl: NgControl,
               @Optional() parentFormGroup: FormGroupDirective,
               errorStateMatcher: ErrorStateMatcher,
               fb: UntypedFormBuilder ) {
    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);
    this.form = fb.group({
        days: [''],
        hours: [''],
        minutes: [''],
        seconds: [''],
      },
      { updateOn: 'blur' });
  }

  ngOnInit() {
    this.form.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.processInputValue();
    });
  }

  get daysControl(): UntypedFormControl {
    return this.form.get('days') as UntypedFormControl;
  }

  onContainerClick( event: Event ) {
    if (this.daysInput && (event.target as Element).tagName.toLowerCase() !== 'input') {
      this.daysInput.nativeElement.focus();
    }
  }

  writeValue( value: number ) {
    if (!value) {
      return;
    }
    this.patchForm(value);
  }

  processInputValue() {
    let { days, hours, minutes, seconds } = this.form.value;

    let checkedDays = !days || isNaN(days) ? 0 : days;
    let checkedHours = !hours || isNaN(hours) ? 0 : hours;
    let checkedMinutes = !minutes || isNaN(minutes) ? 0 : minutes;
    let checkedSeconds = !seconds || isNaN(seconds) ? 0 : seconds;

    const daysAsMilliseconds = moment.duration(parseInt(checkedDays, 10), 'days').asMilliseconds();
    const hoursAsMilliseconds = moment.duration(parseInt(checkedHours, 10), 'hours').asMilliseconds();
    const minutesAsMilliseconds = moment.duration(parseInt(checkedMinutes, 10), 'minute').asMilliseconds();
    const secondsAsMilliseconds = moment.duration(parseInt(checkedSeconds, 10), 'seconds').asMilliseconds();

    const result = daysAsMilliseconds + hoursAsMilliseconds + minutesAsMilliseconds + secondsAsMilliseconds;

    let duration = this.secondsToHms(result);

    this.form.patchValue(duration, { emitEvent: false });
    this._value = result;
    this.onChange(result);
  }

  protected onDisabledState( disabled: boolean ) {
    disabled ? this.form.disable() : this.form.enable();
  }

  protected isErrorState(): boolean {
    const daysState = this.daysInput ? this.daysInput.nativeElement.errorState : false;
    const hoursState = this.hoursInput ? this.hoursInput.nativeElement.errorState : false;
    const minutesState = this.minutesInput ? this.minutesInput.nativeElement.errorState : false;
    const secondsState = this.secondsInput ? this.secondsInput.nativeElement.errorState : false;
    return daysState || hoursState || minutesState || secondsState;
  }

  private patchForm( value: number ) {
    const time = this.secondsToHms(value);
    this.form.patchValue(time);
  }

  private secondsToHms( v: number ): SwuiTimeDuration {
    let total_seconds = Math.floor(v / 1000);
    let total_minutes = Math.floor(total_seconds / 60);
    let total_hours = Math.floor(total_minutes / 60);
    let total_days = Math.floor(total_hours / 24);

    let seconds_result = total_seconds % 60;
    let minutes_result = total_minutes % 60;
    let hours_result = total_hours % 24;

    const processValue = ( value: number ): string => value < 10 ? '0' + value.toString() : value.toString();
    return {
      days: processValue(total_days),
      hours: processValue(hours_result),
      minutes: processValue(minutes_result),
      seconds: processValue(seconds_result)
    };
  }
}
