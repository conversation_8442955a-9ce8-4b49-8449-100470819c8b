import { Component, ComponentFactoryResolver, Injector, Input, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { SwuiGridWidgetConfig, SwuiGridWidgetRegistry } from '../registry/registry';
import { SwuiGridDataSource } from '../swui-grid.datasource';
import { SwuiGridSchemaField } from '../swui-grid.model';

@Component({
    // tslint:disable-next-line:component-selector
    selector: '[grid-widget-chooser]',
    template: '<div #target></div>',
    standalone: false
})
export class SwuiGridWidgetChooserComponent implements OnInit {
  @Input() registry?: SwuiGridWidgetRegistry;
  @Input() type?: 'td' | 'footer';
  @Input() schema?: SwuiGridSchemaField;
  @Input() row?: any;
  @Input() field?: any;
  @Input() action?: any;
  @Input() dataSource?: SwuiGridDataSource<any>;

  @ViewChild('target', { read: ViewContainerRef, static: true })
  private viewRef?: ViewContainerRef;

  constructor( private readonly resolver: ComponentFactoryResolver ) {
  }

  ngOnInit() {
    if (this.viewRef && this.registry && this.schema && this.type) {
      const typeSchema = this.schema[this.type];
      const config: SwuiGridWidgetConfig<any> = Object.assign({},
        {
          field: this.field || this.schema.field,
          title: this.schema.title,
          value: this.schema.value,
        },
        typeSchema ? {
          title: typeSchema.title,
          value: typeSchema.value
        } : {},
        {
          row: this.row,
          schema: this.schema,
          action: this.action
        },
      );

      if (config.value === undefined && this.row) {
        config.value = this.row[config.field];
      }

      if (typeof this.dataSource !== 'undefined' && this.type === 'footer') {
        config.dataSource = this.dataSource;
      }

      const { component, fn } = this.registry.getWidgetRegistryConfig(this.type, this.schema);
      this.viewRef.createComponent(this.resolver.resolveComponentFactory(component), 0, Injector.create({
        providers: fn(config)
      }));
    }
  }
}
