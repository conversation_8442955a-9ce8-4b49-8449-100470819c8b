/* tslint:disable:max-line-length */
import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ReplaySubject } from 'rxjs';

import { I18nModule } from '../../i18n.module';
import { SwDexieModule } from '../../services/sw-dexie/sw-dexie.module';
import { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';
import { TestWidgetComponent } from './test-widget/test-widget.component';
import { TestWidgetModule } from './test-widget/test-widget.module';
import { SwuiGridModule } from '../swui-grid.module';
import { users as demoData } from './users-example.data';
import { SCHEMA_LIST as demoSchema } from './users-example.schema';
import { BulkAction } from '../bulk-actions/bulk-actions.model';
import { WidgetAddTypes } from '../swui-grid.config';
import { APP_BASE_HREF } from '@angular/common';
import { TestGridModule } from './test-grid/test-grid.module';
import { SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN, SWUI_GRID_SELECTION_TRANSFORMER_TOKEN } from '../swui-grid.component';
import { action } from 'storybook/actions';
import { RouteNoopModule } from './route-noop.module';
import { SwuiGridSchemaField } from '../swui-grid.model';

const EN = require('./locale.json');

export const template = `
  <div style="height: 100vh; padding: 32px; overflow: auto;" class="mat-body-1">
    <div class="mat-elevation-z0">
      <lib-swui-grid
        [gridId]="gridId"
        [data]="data"
        [schema]="schema"
        [bulkActions]="bulkActions"
        (widgetActionEmitted)="widgetActionEmitted($event)"
        [rowActions]="rowActions"
        [ignoreQueryParams]="true"
        [columnsManagement]="true"
        [pagination]="false"
        [stickyHeader]="stickyHeader"
        [footer]="footer"
        [sortActive]="sortActive"
        [sortDirection]="sortDirection">
      </lib-swui-grid>
    </div>
  </div>
`;

const props = {
  gridId: 'grid-examples-simple-grid',
  schema: demoSchema,
  data: demoData,
  widgetActionEmitted: action('widgetActionEmitted')
};

storiesOf('Grid/Core', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        TestWidgetModule,
        MatButtonModule,
        MatIconModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        RouteNoopModule,
        SwDexieModule.forRoot('SwUboLibrary'),
        SwuiGridModule.forRoot({
          widgets: [
            { type: 'tdstorybooktest', component: TestWidgetComponent }
          ],
          widgetAddType: WidgetAddTypes.EXPAND,
        }),
        TestGridModule,
      ],
      providers: [
        { provide: APP_BASE_HREF, useValue: '/iframe.html' },
        {
          provide: SWUI_GRID_SELECTION_TRANSFORMER_TOKEN,
          useValue: {
            transform: ( item: any ) => {
              let result = item;
              if ('selectionTransformDemo' in item) {
                result = item['selectionTransformDemo'];
              }
              return result;
            }
          }
        },
        {
          provide: SwHubAuthService, useValue: {
            isLogged() {
              return true;
            },
            username: 'username',
            allowedTo() {
              return true;
            },
            isTwoFactor: true,
            logged: new ReplaySubject<void>()
          }
        },
        {
          provide: SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN,
          useValue: {
            available: ( item: any ) => {
              let available = true;
              if ('selectionRowAvailableDemo' in item) {
                available = item['selectionRowAvailableDemo'];
              }
              return available;
            }
          }
        }
      ],
    })
  )
  .add('Simple grid powered by mat-table', () => ({
    template,
    props: {
      ...props,
      sortActive: 'createdAt',
      sortDirection: 'desc'
    },
  }))
  .add('Sticky header', () => ({
    template,
    props: {
      ...props,
      stickyHeader: true,
    },
  }))
  .add('External widget', () => ({
    template,
    props: {
      ...props,
      schema: demoSchema.map(( item: SwuiGridSchemaField ) => {
        if (item.type === 'string') {
          return { ...item, td: { type: 'storybooktest' } };
        }
        return item;
      })
    },
  }))
  .add('Bulk and Row Actions', () => ({
    template,
    props: {
      ...props,
      bulkActions: [
        {
          title: 'Do Bulk console.log',
          icon: 'web',
          isDisabledAction: () => false,
          fn: () => console.log('atata'),
        },
        {
          title: 'Do Bulk console.log',
          icon: 'web',
          isDisabledAction: () => false,
          fn: () => console.log('atata'),
        }
      ],
      rowActions: [
        {
          title: 'Do Console log',
          icon: 'web',
          fn: ( item: any ) => console.log('CONSOLE LOG OK', item)
        },
        {
          title: 'Action is disabled',
          icon: 'error',
          fn: () => {
          },
          canActivateFn: () => false,
        }
      ]
    },
  }))
  .add('Selection Availability / Transform', () => ({
    template: `<div style="height: 100vh; padding: 32px; overflow: auto;">
    <div class="mat-elevation-z0">
        <lib-swui-grid
          [gridId]="gridId"
          [data]="data"
          [schema]="schema"
          [bulkActions]="bulkActions"
          [bulkSelectionOnly]="true"
          [columnsManagement]="false" #grid>
          <button mat-flat-button (click)="renderSelection(grid.selection.selected)">View selection items in console</button>
      </lib-swui-grid>
    </div>
  </div>`,
    props: {
      ...props,
      data: [...demoData].map(( item ) => ({
        ...item,
        selectionTransformDemo: item.username,
        selectionRowAvailableDemo: item.status !== 'suspended',
      })),
      menuIcon: 'menu',
      bulkActions: [
        {
          title: 'Just a title for selection mode',
        },
      ],
      renderSelection: ( items: string[] ) => console.log('selection is', items),
    },
  }))
  .add('Row Actions customization', () => ({
    template: `<div style="height: 100vh; padding: 32px; overflow: auto;">
    <div class="mat-elevation-z0">
        <lib-swui-grid
          [gridId]="gridId"
          [data]="data"
          [schema]="schema"
          [rowActionsColumnTitle]="''"
          [rowActionsMenuIcon]="menuIcon"
          [bulkActions]="bulkActions"
          (widgetActionEmitted)="widgetActionEmitted($event)"
          [rowActions]="rowActions">
      </lib-swui-grid>
    </div>
  </div>`,
    props: {
      ...props,
      menuIcon: 'menu',
      rowActions: [
        {
          title: 'Superb menu action',
          icon: 'search',
          showOnHover: true,
          inMenu: false,
          fn: () => true,
        },
        {
          title: 'Add new record',
          icon: 'add_box',
          inMenu: false,
          fn: () => true,
        },
        {
          title: 'Do Console log',
          icon: 'web',
          fn: ( item: any ) => console.log('CONSOLE LOG OK', item)
        },
        {
          title: 'Action is disabled',
          icon: 'error',
          fn: () => {
          },
          canActivateFn: () => false,
        }
      ]
    },
  }))
  .add('Empty Grid', () => ({
    template,
    props: {
      ...props,
      data: [],
    },
  }))
  .add('Change/Replace local data', () => ({
    template: '<test-grid></test-grid>',
  }))
  .add('Projected Buttons', () => ({
    template: `
      <div style="height: 100vh; padding: 32px; overflow: auto;">
        <div class="mat-elevation-z0">
          <lib-swui-grid
            #grid
            [data]="data"
            [schema]="schema"
            gridId="grid-examples-content-projection"
            [disableRefreshAction]="disableRefreshAction">

            <input type="search" placeholder="Search" style="margin-right: 10px">
            <button mat-stroked-button (click)="testClick()" color="accent">
              <mat-icon>new_releases</mat-icon> Projected Button
            </button>
            <button mat-raised-button style="margin: 0 10px" (click)="data = []">Clear Data</button>
            <button mat-icon-button color="primary" (click)="data = demoData">
              <mat-icon>filter_alt</mat-icon>
            </button>
          </lib-swui-grid>
        </div>
      </div>
     `,
    props: {
      ...props,
      data: [...demoData],
      disableRefreshAction: true,
      bulkActions: [
        new BulkAction({
          title: 'Do Bulk console.log',
          icon: 'web',
          fn: ( items: any[] ) => items.map(item => console.log('item', item)),
          canActivateFn: ( item: any ) => item['username'] !== '1111'
        })
      ],
      testClick: () => console.log('wow!'),
    },
  }));
