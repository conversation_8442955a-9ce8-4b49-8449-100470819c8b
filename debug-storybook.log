[19:26:09.922] [INFO] Checking the health of your Storybook..
[19:26:09.923] [DEBUG] Getting Storybook info...
[19:26:09.925] [DEBUG] Loading main config...
[19:26:10.301] [DEBUG] Getting stories paths...
[19:26:10.367] [DEBUG] Getting package manager...
[19:26:12.147] [DEBUG] Getting Storybook version...
[19:26:18.934] [WARN] .storybook: 1 issue found
[19:26:18.934] [INFO] You are currently using Storybook 9.1.2 but you have packages which are incompatible with it:

- @storybook/addon-essentials@8.6.14 which depends on 8.6.14
 Repo: https://github.com/storybookjs/storybook/tree/next/code/addons/essentials
- @storybook/manager-api@8.6.14 which depends on ^8.2.0 || ^8.3.0-0 || ^8.4.0-0 || ^8.5.0-0 || ^8.6.0-0
 Repo: https://github.com/storybookjs/storybook/tree/next/code/lib/manager-api
- @storybook/theming@8.6.14 which depends on ^8.2.0 || ^8.3.0-0 || ^8.4.0-0 || ^8.5.0-0 || ^8.6.0-0
 Repo: https://github.com/storybookjs/storybook/tree/next/code/lib/theming

Please consider updating your packages or contacting the maintainers for compatibility details.

For more on Storybook 9 compatibility, see the linked GitHub issue:
https://github.com/storybookjs/storybook/issues/30944
[19:26:18.951] [INFO] Storybook doctor is complete!
[19:26:18.952] [INFO] You can always recheck the health of your project(s) by running:
npx storybook doctor