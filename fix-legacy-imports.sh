#!/bin/bash

# Fix Angular Material 17 legacy imports
echo "Fixing Angular Material 17 legacy imports..."

# Find all TypeScript files in the projects/lib/src directory
find projects/lib/src -name "*.ts" -type f | while read file; do
    echo "Processing: $file"
    
    # Replace legacy imports with new imports
    sed -i '' \
        -e "s|@angular/material/legacy-autocomplete|@angular/material/autocomplete|g" \
        -e "s|@angular/material/legacy-button|@angular/material/button|g" \
        -e "s|@angular/material/legacy-card|@angular/material/card|g" \
        -e "s|@angular/material/legacy-checkbox|@angular/material/checkbox|g" \
        -e "s|@angular/material/legacy-chips|@angular/material/chips|g" \
        -e "s|@angular/material/legacy-core|@angular/material/core|g" \
        -e "s|@angular/material/legacy-dialog|@angular/material/dialog|g" \
        -e "s|@angular/material/legacy-form-field|@angular/material/form-field|g" \
        -e "s|@angular/material/legacy-input|@angular/material/input|g" \
        -e "s|@angular/material/legacy-list|@angular/material/list|g" \
        -e "s|@angular/material/legacy-menu|@angular/material/menu|g" \
        -e "s|@angular/material/legacy-paginator|@angular/material/paginator|g" \
        -e "s|@angular/material/legacy-progress-bar|@angular/material/progress-bar|g" \
        -e "s|@angular/material/legacy-progress-spinner|@angular/material/progress-spinner|g" \
        -e "s|@angular/material/legacy-radio|@angular/material/radio|g" \
        -e "s|@angular/material/legacy-select|@angular/material/select|g" \
        -e "s|@angular/material/legacy-slide-toggle|@angular/material/slide-toggle|g" \
        -e "s|@angular/material/legacy-slider|@angular/material/slider|g" \
        -e "s|@angular/material/legacy-snack-bar|@angular/material/snack-bar|g" \
        -e "s|@angular/material/legacy-table|@angular/material/table|g" \
        -e "s|@angular/material/legacy-tabs|@angular/material/tabs|g" \
        -e "s|@angular/material/legacy-tooltip|@angular/material/tooltip|g" \
        "$file"
    
    # Remove "Legacy" prefixes from class names and interfaces
    sed -i '' \
        -e "s|MatLegacyAutocomplete as MatAutocomplete|MatAutocomplete|g" \
        -e "s|MatLegacyAutocompleteTrigger as MatAutocompleteTrigger|MatAutocompleteTrigger|g" \
        -e "s|MatLegacyButton as MatButton|MatButton|g" \
        -e "s|MatLegacyButtonModule as MatButtonModule|MatButtonModule|g" \
        -e "s|MatLegacyCard as MatCard|MatCard|g" \
        -e "s|MatLegacyCardModule as MatCardModule|MatCardModule|g" \
        -e "s|MatLegacyCheckbox as MatCheckbox|MatCheckbox|g" \
        -e "s|MatLegacyCheckboxModule as MatCheckboxModule|MatCheckboxModule|g" \
        -e "s|MatLegacyChip as MatChip|MatChip|g" \
        -e "s|MatLegacyChipInputEvent as MatChipInputEvent|MatChipInputEvent|g" \
        -e "s|MatLegacyChipsModule as MatChipsModule|MatChipsModule|g" \
        -e "s|MatLegacyDialog as MatDialog|MatDialog|g" \
        -e "s|MatLegacyDialogModule as MatDialogModule|MatDialogModule|g" \
        -e "s|MatLegacyDialogRef as MatDialogRef|MatDialogRef|g" \
        -e "s|MatLegacyFormField as MatFormField|MatFormField|g" \
        -e "s|MatLegacyFormFieldControl as MatFormFieldControl|MatFormFieldControl|g" \
        -e "s|MatLegacyFormFieldModule as MatFormFieldModule|MatFormFieldModule|g" \
        -e "s|MatLegacyInput as MatInput|MatInput|g" \
        -e "s|MatLegacyInputModule as MatInputModule|MatInputModule|g" \
        -e "s|MatLegacyList as MatList|MatList|g" \
        -e "s|MatLegacyListModule as MatListModule|MatListModule|g" \
        -e "s|MatLegacyMenu as MatMenu|MatMenu|g" \
        -e "s|MatLegacyMenuModule as MatMenuModule|MatMenuModule|g" \
        -e "s|MatLegacyMenuTrigger as MatMenuTrigger|MatMenuTrigger|g" \
        -e "s|MatLegacyOption as MatOption|MatOption|g" \
        -e "s|MatLegacyOptionSelectionChange as MatOptionSelectionChange|MatOptionSelectionChange|g" \
        -e "s|MatLegacyPaginator as MatPaginator|MatPaginator|g" \
        -e "s|MatLegacyPaginatorModule as MatPaginatorModule|MatPaginatorModule|g" \
        -e "s|MatLegacyProgressBar as MatProgressBar|MatProgressBar|g" \
        -e "s|MatLegacyProgressBarModule as MatProgressBarModule|MatProgressBarModule|g" \
        -e "s|MatLegacyProgressSpinner as MatProgressSpinner|MatProgressSpinner|g" \
        -e "s|MatLegacyProgressSpinnerModule as MatProgressSpinnerModule|MatProgressSpinnerModule|g" \
        -e "s|MatLegacyRadioButton as MatRadioButton|MatRadioButton|g" \
        -e "s|MatLegacyRadioGroup as MatRadioGroup|MatRadioGroup|g" \
        -e "s|MatLegacyRadioModule as MatRadioModule|MatRadioModule|g" \
        -e "s|MatLegacySelect as MatSelect|MatSelect|g" \
        -e "s|MatLegacySelectModule as MatSelectModule|MatSelectModule|g" \
        -e "s|MatLegacySlideToggle as MatSlideToggle|MatSlideToggle|g" \
        -e "s|MatLegacySlideToggleModule as MatSlideToggleModule|MatSlideToggleModule|g" \
        -e "s|MatLegacySlider as MatSlider|MatSlider|g" \
        -e "s|MatLegacySliderModule as MatSliderModule|MatSliderModule|g" \
        -e "s|MatLegacySnackBar as MatSnackBar|MatSnackBar|g" \
        -e "s|MatLegacySnackBarModule as MatSnackBarModule|MatSnackBarModule|g" \
        -e "s|MatLegacySnackBarRef as MatSnackBarRef|MatSnackBarRef|g" \
        -e "s|MatLegacyTable as MatTable|MatTable|g" \
        -e "s|MatLegacyTableModule as MatTableModule|MatTableModule|g" \
        -e "s|MatLegacyTab as MatTab|MatTab|g" \
        -e "s|MatLegacyTabGroup as MatTabGroup|MatTabGroup|g" \
        -e "s|MatLegacyTabsModule as MatTabsModule|MatTabsModule|g" \
        -e "s|MatLegacyTooltip as MatTooltip|MatTooltip|g" \
        -e "s|MatLegacyTooltipModule as MatTooltipModule|MatTooltipModule|g" \
        "$file"
    
    # Fix constants and tokens
    sed -i '' \
        -e "s|MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA|MAT_DIALOG_DATA|g" \
        -e "s|MAT_LEGACY_SNACK_BAR_DATA as MAT_SNACK_BAR_DATA|MAT_SNACK_BAR_DATA|g" \
        -e "s|MAT_LEGACY_SNACK_BAR_DEFAULT_OPTIONS as MAT_SNACK_BAR_DEFAULT_OPTIONS|MAT_SNACK_BAR_DEFAULT_OPTIONS|g" \
        "$file"
done

echo "Legacy imports fixed successfully!"
